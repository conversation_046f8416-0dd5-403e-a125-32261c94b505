package com.dell.it.hip.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonToken;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Custom deserializer for tags field to handle backward compatibility.
 * Supports both legacy formats and new key-value pair format:
 * 
 * Legacy formats:
 * - String array: ["tag1", "tag2", "tag3"]
 * - Comma-separated string: "tag1,tag2,tag3"
 * 
 * New format:
 * - Key-value objects: [{"key": "environment", "value": "production"}, {"key": "team", "value": "payments"}]
 */
public class TagsDeserializer extends JsonDeserializer<List<Tag>> {

    @Override
    public List<Tag> deserialize(JsonParser parser, DeserializationContext context) throws IOException {
        List<Tag> tags = new ArrayList<>();
        JsonToken token = parser.getCurrentToken();
        
        if (token == JsonToken.START_ARRAY) {
            // Handle array format
            JsonNode arrayNode = parser.getCodec().readTree(parser);
            
            for (JsonNode node : arrayNode) {
                if (node.isObject()) {
                    // New format: {"key": "...", "value": "..."}
                    String key = node.has("key") ? node.get("key").asText() : "";
                    String value = node.has("value") ? node.get("value").asText() : "";
                    tags.add(new Tag(key, value));
                } else if (node.isTextual()) {
                    // Legacy format: ["tag1", "tag2"]
                    String tagValue = node.asText();
                    tags.add(new Tag("legacy", tagValue));
                }
            }
        } else if (token == JsonToken.VALUE_STRING) {
            // Handle legacy comma-separated string format: "tag1,tag2,tag3"
            String tagsString = parser.getValueAsString();
            if (tagsString != null && !tagsString.trim().isEmpty()) {
                String[] tagArray = tagsString.split(",");
                for (String tagValue : tagArray) {
                    String trimmedTag = tagValue.trim();
                    if (!trimmedTag.isEmpty()) {
                        tags.add(new Tag("legacy", trimmedTag));
                    }
                }
            }
        } else if (token == JsonToken.START_OBJECT) {
            // Handle single object case (though not expected in current usage)
            ObjectMapper mapper = (ObjectMapper) parser.getCodec();
            Tag tag = mapper.readValue(parser, Tag.class);
            tags.add(tag);
        }
        
        return tags;
    }
}
