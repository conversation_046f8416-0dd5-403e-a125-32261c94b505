package com.dell.it.hip.config;

import com.dell.it.hip.config.Handlers.DynamicKafkaHandlerConfig;
import com.dell.it.hip.config.Handlers.DynamicIbmmqHandlerConfig;
import com.dell.it.hip.config.Handlers.DynamicRabbitMQHandlerConfig;
import com.dell.it.hip.config.adapters.DynamicKafkaAdapterConfig;
import com.dell.it.hip.config.adapters.DynamicIBMMQAdapterConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Simple validation test to verify that our property naming refactoring works correctly.
 * This test validates that JSON deserialization works with the new dot-separated lowercase naming convention.
 */
public class PropertyNamingValidationTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testKafkaHandlerConfigNewPropertyNames() throws Exception {
        // Test with the new property names
        String json = "{\n"
                + "  \"kafka.producer.bootstrap.servers\": \"localhost:9092\",\n"
                + "  \"kafka.producer.security.protocol\": \"SASL_SSL\",\n"
                + "  \"kafka.producer.sasl.mechanism\": \"PLAIN\",\n"
                + "  \"kafka.producer.sasl.jaas.config\": \"test-config\",\n"
                + "  \"kafka.producer.ssl.truststore.location\": \"/path/to/truststore\",\n"
                + "  \"kafka.producer.ssl.truststore.type\": \"JKS\",\n"
                + "  \"kafka.producer.gzip.enabled\": true\n"
                + "}";

        DynamicKafkaHandlerConfig config = objectMapper.readValue(json, DynamicKafkaHandlerConfig.class);
        
        assertNotNull(config);
        assertEquals("localhost:9092", config.getBootstrapServers());
        assertEquals("SASL_SSL", config.getSecurityProtocol());
        assertEquals("PLAIN", config.getSaslMechanism());
        assertEquals("test-config", config.getSasljaasconfig());
        assertEquals("/path/to/truststore", config.getSslTruststoreLocation());
        assertEquals("JKS", config.getSsltruststoretype());
        assertTrue(config.getGzipEnabled());
    }

    @Test
    public void testIbmmqHandlerConfigNewPropertyNames() throws Exception {
        // Test with the new property names
        String json = "{\n"
                + "  \"ibmmq.producer.queue.manager\": \"QM1\",\n"
                + "  \"ibmmq.producer.conn.name\": \"localhost(1414)\",\n"
                + "  \"ibmmq.producer.ssl.cipher.suite\": \"TLS_RSA_WITH_AES_256_CBC_SHA256\",\n"
                + "  \"ibmmq.producer.gzip.enabled\": false\n"
                + "}";

        DynamicIbmmqHandlerConfig config = objectMapper.readValue(json, DynamicIbmmqHandlerConfig.class);
        
        assertNotNull(config);
        assertEquals("QM1", config.getQueueManager());
        assertEquals("localhost(1414)", config.getConnName());
        assertEquals("TLS_RSA_WITH_AES_256_CBC_SHA256", config.getCipherSuite());
        assertFalse(config.getGzipEnabled());
    }

    @Test
    public void testRabbitMQHandlerConfigNewPropertyNames() throws Exception {
        // Test with the new property names
        String json = "{\n"
                + "  \"rabbitmq.producer.routing.key\": \"test.routing.key\",\n"
                + "  \"rabbitmq.producer.gzip.enabled\": true\n"
                + "}";

        DynamicRabbitMQHandlerConfig config = objectMapper.readValue(json, DynamicRabbitMQHandlerConfig.class);
        
        assertNotNull(config);
        assertEquals("test.routing.key", config.getRoutingKey());
        assertTrue(config.getGzipEnabled());
    }

    @Test
    public void testIBMMQAdapterConfigNewPropertyNames() throws Exception {
        // Test with the new property names
        String json = "{\n"
                + "  \"ibmmq.consumer.queue.manager\": \"QM1\",\n"
                + "  \"ibmmq.consumer.conn.name\": \"localhost(1414)\",\n"
                + "  \"ibmmq.consumer.ssl.cipher.suite\": \"TLS_RSA_WITH_AES_256_CBC_SHA256\",\n"
                + "  \"ibmmq.consumer.ssl.peer.name\": \"CN=localhost\",\n"
                + "  \"ibmmq.consumer.ssl.keystore.password\": \"keypass\",\n"
                + "  \"ibmmq.consumer.ssl.truststore.password\": \"trustpass\"\n"
                + "}";

        DynamicIBMMQAdapterConfig config = objectMapper.readValue(json, DynamicIBMMQAdapterConfig.class);
        
        assertNotNull(config);
        assertEquals("QM1", config.getQueueManager());
        assertEquals("localhost(1414)", config.getConnName());
        assertEquals("TLS_RSA_WITH_AES_256_CBC_SHA256", config.getSslCipherSuite());
        assertEquals("CN=localhost", config.getSslPeerName());
        assertEquals("keypass", config.getSslKeystorePassword());
        assertEquals("trustpass", config.getSslTruststorePassword());
    }

    @Test
    public void testKafkaAdapterConfigExistingPropertyNames() throws Exception {
        // Test that the adapter config (which was already correct) still works
        String json = "{\n"
                + "  \"kafka.consumer.bootstrap.servers\": \"localhost:9092\",\n"
                + "  \"kafka.consumer.security.protocol\": \"SASL_SSL\",\n"
                + "  \"kafka.consumer.sasl.mechanism\": \"PLAIN\"\n"
                + "}";

        DynamicKafkaAdapterConfig config = objectMapper.readValue(json, DynamicKafkaAdapterConfig.class);
        
        assertNotNull(config);
        assertEquals("localhost:9092", config.getBootstrapServers());
        assertEquals("SASL_SSL", config.getSecurityProtocol());
        assertEquals("PLAIN", config.getSaslMechanism());
    }
}
