package com.dell.it.hip.config.FlowSteps;

import lombok.Data;


public class ValidationConfig {
    private boolean structural;
    private boolean schema;
    private String schemaType;      // e.g. "XSD", "JSONSCHEMA", "STAEDI"
    private String schemaKey;       // Redis key for schema
    // ...more if needed

    // Getters & Setters
    // ...

    public boolean isStructural() {
        return structural;
    }

    public void setStructural(boolean structural) {
        this.structural = structural;
    }

    public boolean isSchema() {
        return schema;
    }

    public void setSchema(boolean schema) {
        this.schema = schema;
    }

    public String getSchemaType() {
        return schemaType;
    }

    public void setSchemaType(String schemaType) {
        this.schemaType = schemaType;
    }

    public String getSchemaKey() {
        return schemaKey;
    }

    public void setSchemaKey(String schemaKey) {
        this.schemaKey = schemaKey;
    }
}