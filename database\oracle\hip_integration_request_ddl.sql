-- =====================================================
-- Oracle DDL Script for HIPIntegrationRequestEntity
-- =====================================================
-- This script creates the table structure for the HIP Integration Request entity
-- Based on: com.dell.it.hip.config.HIPIntegrationRequestEntity
-- Generated: Oracle Database Compatible DDL
-- =====================================================

-- Create sequence for primary key generation
-- Oracle doesn't have auto-increment, so we use sequences
CREATE SEQUENCE hip_integration_requests_seq
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE
    ORDER;

-- Create the main table
CREATE TABLE hip_integration_requests (
    -- Primary Key
    id                          NUMBER(19,0) NOT NULL,
    
    -- Required Business Fields
    service_manager_name        VARCHAR2(255) NOT NULL,
    hip_integration_name        VARCHAR2(255) NOT NULL,
    version                     VARCHAR2(100) NOT NULL,
    business_flow_name          VARCHAR2(255) NOT NULL,
    
    -- Optional Business Fields
    tags                        VARCHAR2(1000),
    business_flow_type          VARCHAR2(255),
    hip_integration_type        VARCHAR2(255),
    business_flow_version       VARCHAR2(100),
    
    -- JSON Storage Fields (using CLOB for large JSON objects)
    adapters_json               CLOB,
    handlers_json               CLOB,
    flow_steps_json             CLOB,
    property_sheets_json        CLOB,
    throttle_settings_json      CLOB,
    
    -- Status and Control Fields
    status                      VARCHAR2(50),
    deleted                     NUMBER(1,0) DEFAULT 0,
    
    -- Audit Fields
    created_at                  TIMESTAMP NOT NULL,
    updated_at                  TIMESTAMP,
    
    -- Primary Key Constraint
    CONSTRAINT pk_hip_integration_requests PRIMARY KEY (id),
    
    -- Check Constraints
    CONSTRAINT chk_hip_int_req_deleted CHECK (deleted IN (0, 1))
);

-- Add table comment
COMMENT ON TABLE hip_integration_requests IS 'Stores HIP Integration Request configurations and metadata';

-- Add column comments
COMMENT ON COLUMN hip_integration_requests.id IS 'Primary key - unique identifier for each integration request';
COMMENT ON COLUMN hip_integration_requests.service_manager_name IS 'Name of the service manager handling this integration';
COMMENT ON COLUMN hip_integration_requests.hip_integration_name IS 'Name of the HIP integration';
COMMENT ON COLUMN hip_integration_requests.version IS 'Version of the integration configuration';
COMMENT ON COLUMN hip_integration_requests.business_flow_name IS 'Name of the business flow';
COMMENT ON COLUMN hip_integration_requests.tags IS 'Comma-separated tags for categorization (max 1000 chars)';
COMMENT ON COLUMN hip_integration_requests.business_flow_type IS 'Type of business flow';
COMMENT ON COLUMN hip_integration_requests.hip_integration_type IS 'Type of HIP integration';
COMMENT ON COLUMN hip_integration_requests.business_flow_version IS 'Version of the business flow';
COMMENT ON COLUMN hip_integration_requests.adapters_json IS 'JSON configuration for adapters';
COMMENT ON COLUMN hip_integration_requests.handlers_json IS 'JSON configuration for handlers';
COMMENT ON COLUMN hip_integration_requests.flow_steps_json IS 'JSON configuration for flow steps';
COMMENT ON COLUMN hip_integration_requests.property_sheets_json IS 'JSON configuration for property sheets';
COMMENT ON COLUMN hip_integration_requests.throttle_settings_json IS 'JSON configuration for throttle settings';
COMMENT ON COLUMN hip_integration_requests.status IS 'Current status of the integration request';
COMMENT ON COLUMN hip_integration_requests.deleted IS 'Soft delete flag (0=active, 1=deleted)';
COMMENT ON COLUMN hip_integration_requests.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN hip_integration_requests.updated_at IS 'Timestamp when record was last updated';

-- Create indexes for performance optimization
-- Index on service manager name for filtering
CREATE INDEX idx_hip_int_req_service_mgr ON hip_integration_requests (service_manager_name);

-- Index on integration name for lookups
CREATE INDEX idx_hip_int_req_name ON hip_integration_requests (hip_integration_name);

-- Index on version for version-based queries
CREATE INDEX idx_hip_int_req_version ON hip_integration_requests (version);

-- Index on status for status-based filtering
CREATE INDEX idx_hip_int_req_status ON hip_integration_requests (status);

-- Index on deleted flag for active record filtering
CREATE INDEX idx_hip_int_req_deleted ON hip_integration_requests (deleted);

-- Composite index for unique business key lookups
CREATE INDEX idx_hip_int_req_business_key ON hip_integration_requests (
    service_manager_name, 
    hip_integration_name, 
    version
);

-- Index on created_at for temporal queries
CREATE INDEX idx_hip_int_req_created_at ON hip_integration_requests (created_at);

-- Index on updated_at for change tracking
CREATE INDEX idx_hip_int_req_updated_at ON hip_integration_requests (updated_at);

-- Add unique constraint for business key combination
-- This ensures no duplicate combinations of serviceManagerName, hipIntegrationName, and version
-- Based on the @UniqueConstraint annotation in the original HIPIntegrationRequest class
ALTER TABLE hip_integration_requests
ADD CONSTRAINT uk_hip_int_req_business_key UNIQUE (
    service_manager_name,
    hip_integration_name,
    version
);

-- Create trigger for auto-populating primary key
CREATE OR REPLACE TRIGGER trg_hip_integration_requests_pk
    BEFORE INSERT ON hip_integration_requests
    FOR EACH ROW
BEGIN
    IF :NEW.id IS NULL THEN
        SELECT hip_integration_requests_seq.NEXTVAL INTO :NEW.id FROM dual;
    END IF;
END;
/

-- Create trigger for auto-updating timestamps
CREATE OR REPLACE TRIGGER trg_hip_integration_requests_audit
    BEFORE INSERT OR UPDATE ON hip_integration_requests
    FOR EACH ROW
BEGIN
    IF INSERTING THEN
        :NEW.created_at := SYSTIMESTAMP;
        :NEW.updated_at := SYSTIMESTAMP;
    ELSIF UPDATING THEN
        :NEW.updated_at := SYSTIMESTAMP;
    END IF;
END;
/

-- Grant permissions (adjust schema/user as needed)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON hip_integration_requests TO hip_app_user;
-- GRANT SELECT ON hip_integration_requests_seq TO hip_app_user;

-- =====================================================
-- End of DDL Script
-- =====================================================
