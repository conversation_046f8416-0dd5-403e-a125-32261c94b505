package com.dell.it.hip.client;

import org.apache.kafka.clients.consumer.*;
import org.apache.kafka.clients.producer.*;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * KafkaClient utility class for testing Kafka message send and receive operations.
 * This class mirrors the implementation patterns used in DynamicKafkaInputAdapter 
 * and DynamicKafkaOutputHandler for compatibility with HIP services framework.
 * 
 * Usage:
 * 1. Create KafkaClient with consumer and producer configurations
 * 2. Use sendMessage() to test handler functionality
 * 3. Use receiveMessage() to test adapter functionality
 * 4. Use startListener() for continuous message consumption
 */
public class KafkaClient implements AutoCloseable {
    
    private static final Logger logger = LoggerFactory.getLogger(KafkaClient.class);
    
    // Configuration for consumer (adapter testing)
    private final KafkaClientConfig consumerConfig;
    
    // Configuration for producer (handler testing)  
    private final KafkaClientConfig producerConfig;
    
    // Connection management
    private KafkaProducer<byte[], byte[]> producer;
    private KafkaConsumer<byte[], byte[]> consumer;
    private ExecutorService listenerExecutor;
    private final AtomicBoolean listenerRunning = new AtomicBoolean(false);
    
    public KafkaClient(KafkaClientConfig consumerConfig, KafkaClientConfig producerConfig) {
        this.consumerConfig = consumerConfig;
        this.producerConfig = producerConfig;
    }
    
    /**
     * Send a message to Kafka using the same approach as DynamicKafkaOutputHandler
     */
    public void sendMessage(String message) throws Exception {
        sendMessage(message.getBytes(StandardCharsets.UTF_8));
    }
    
    /**
     * Send a byte array message to Kafka using the same approach as DynamicKafkaOutputHandler
     */
    public void sendMessage(byte[] payload) throws Exception {
        logger.info("Sending message to Kafka topic: {}", producerConfig.getTopic());
        
        KafkaProducer<byte[], byte[]> kafkaProducer = getOrCreateProducer();
        
        ProducerRecord<byte[], byte[]> record = new ProducerRecord<>(producerConfig.getTopic(), payload);
        
        try {
            RecordMetadata metadata = kafkaProducer.send(record).get();
            logger.info("Message sent successfully to topic: {}, partition: {}, offset: {}", 
                       metadata.topic(), metadata.partition(), metadata.offset());
        } catch (Exception ex) {
            logger.error("Kafka send failed: {}", ex.getMessage(), ex);
            throw ex;
        }
    }
    
    /**
     * Send a message with custom headers
     */
    public void sendMessage(byte[] payload, Map<String, String> headers) throws Exception {
        logger.info("Sending message with headers to Kafka topic: {}", producerConfig.getTopic());
        
        KafkaProducer<byte[], byte[]> kafkaProducer = getOrCreateProducer();
        
        ProducerRecord<byte[], byte[]> record = new ProducerRecord<>(producerConfig.getTopic(), payload);
        
        // Add headers
        if (headers != null) {
            headers.forEach((key, value) -> 
                record.headers().add(key, value.getBytes(StandardCharsets.UTF_8)));
        }
        
        try {
            RecordMetadata metadata = kafkaProducer.send(record).get();
            logger.info("Message with headers sent successfully to topic: {}, partition: {}, offset: {}", 
                       metadata.topic(), metadata.partition(), metadata.offset());
        } catch (Exception ex) {
            logger.error("Kafka send with headers failed: {}", ex.getMessage(), ex);
            throw ex;
        }
    }
    
    /**
     * Receive a single message from Kafka using the same approach as DynamicKafkaInputAdapter
     */
    public KafkaMessage receiveMessage(long timeoutMs) throws Exception {
        logger.info("Receiving message from Kafka topic: {}", consumerConfig.getTopic());
        
        KafkaConsumer<byte[], byte[]> kafkaConsumer = getOrCreateConsumer();
        
        try {
            ConsumerRecords<byte[], byte[]> records = kafkaConsumer.poll(Duration.ofMillis(timeoutMs));
            
            if (records.isEmpty()) {
                logger.info("No message received within timeout: {}ms", timeoutMs);
                return null;
            }
            
            ConsumerRecord<byte[], byte[]> record = records.iterator().next();
            
            // Extract headers
            Map<String, String> headers = new HashMap<>();
            record.headers().forEach(header -> {
                if (header.value() != null) {
                    headers.put(header.key(), new String(header.value(), StandardCharsets.UTF_8));
                }
            });
            
            KafkaMessage kafkaMessage = new KafkaMessage(
                new String(record.value(), StandardCharsets.UTF_8),
                record.topic(),
                record.partition(),
                record.offset(),
                headers
            );
            
            logger.info("Message received successfully from topic: {}, partition: {}, offset: {}", 
                       record.topic(), record.partition(), record.offset());
            
            return kafkaMessage;
            
        } catch (Exception ex) {
            logger.error("Kafka receive failed: {}", ex.getMessage(), ex);
            throw ex;
        }
    }
    
    /**
     * Start a continuous message listener using the same approach as DynamicKafkaInputAdapter
     */
    public void startListener(MessageHandler messageHandler) throws Exception {
        if (listenerRunning.get()) {
            logger.warn("Listener is already running");
            return;
        }
        
        logger.info("Starting Kafka listener for topic: {}", consumerConfig.getTopic());
        
        listenerExecutor = Executors.newSingleThreadExecutor();
        listenerRunning.set(true);
        
        listenerExecutor.submit(() -> {
            KafkaConsumer<byte[], byte[]> kafkaConsumer = getOrCreateConsumer();
            
            while (listenerRunning.get()) {
                try {
                    ConsumerRecords<byte[], byte[]> records = kafkaConsumer.poll(Duration.ofMillis(1000));
                    
                    for (ConsumerRecord<byte[], byte[]> record : records) {
                        // Extract headers
                        Map<String, String> headers = new HashMap<>();
                        record.headers().forEach(header -> {
                            if (header.value() != null) {
                                headers.put(header.key(), new String(header.value(), StandardCharsets.UTF_8));
                            }
                        });
                        
                        KafkaMessage kafkaMessage = new KafkaMessage(
                            new String(record.value(), StandardCharsets.UTF_8),
                            record.topic(),
                            record.partition(),
                            record.offset(),
                            headers
                        );
                        
                        try {
                            messageHandler.handleMessage(kafkaMessage);
                        } catch (Exception ex) {
                            logger.error("Error handling message: {}", ex.getMessage(), ex);
                        }
                    }
                } catch (Exception ex) {
                    if (listenerRunning.get()) {
                        logger.error("Error in Kafka listener: {}", ex.getMessage(), ex);
                    }
                }
            }
        });
        
        logger.info("Kafka listener started for topic: {}", consumerConfig.getTopic());
    }
    
    /**
     * Stop the message listener
     */
    public void stopListener() {
        if (!listenerRunning.get()) {
            return;
        }
        
        listenerRunning.set(false);
        
        if (listenerExecutor != null) {
            listenerExecutor.shutdown();
            try {
                if (!listenerExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    listenerExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                listenerExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        logger.info("Kafka listener stopped");
    }
    
    /**
     * Wait for a specific number of messages with timeout
     */
    public List<KafkaMessage> waitForMessages(int expectedCount, long timeoutMs, MessageHandler messageHandler) throws Exception {
        CountDownLatch latch = new CountDownLatch(expectedCount);
        List<KafkaMessage> receivedMessages = Collections.synchronizedList(new ArrayList<>());
        
        startListener(message -> {
            receivedMessages.add(message);
            messageHandler.handleMessage(message);
            latch.countDown();
        });
        
        boolean received = latch.await(timeoutMs, TimeUnit.MILLISECONDS);
        stopListener();
        
        if (!received) {
            throw new RuntimeException("Did not receive expected " + expectedCount + " messages within " + timeoutMs + "ms");
        }
        
        logger.info("Successfully received {} messages", expectedCount);
        return receivedMessages;
    }
    
    /**
     * Close all connections and clean up resources
     */
    @Override
    public void close() {
        stopListener();
        
        if (producer != null) {
            try { producer.close(); } catch (Exception ignored) {}
        }
        if (consumer != null) {
            try { consumer.close(); } catch (Exception ignored) {}
        }
        
        logger.info("KafkaClient closed");
    }
    
    // Private helper methods
    
    private KafkaProducer<byte[], byte[]> getOrCreateProducer() {
        if (producer == null) {
            Properties props = createProducerProperties(producerConfig);
            producer = new KafkaProducer<>(props);
        }
        return producer;
    }
    
    private KafkaConsumer<byte[], byte[]> getOrCreateConsumer() {
        if (consumer == null) {
            Properties props = createConsumerProperties(consumerConfig);
            consumer = new KafkaConsumer<>(props);
            consumer.subscribe(Collections.singletonList(consumerConfig.getTopic()));
        }
        return consumer;
    }
    
    private Properties createProducerProperties(KafkaClientConfig config) {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, config.getBootstrapServers());
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, ByteArraySerializer.class.getName());

        // Handle idempotence and acks configuration compatibility
        boolean enableIdempotence = Boolean.TRUE.equals(config.getEnableIdempotence());
        if (enableIdempotence) {
            // When idempotence is enabled, acks must be "all" to guarantee idempotence
            props.put(ProducerConfig.ACKS_CONFIG, "all");
            props.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, "true");
            logger.debug("Idempotence enabled: automatically setting acks=all for idempotent producer");
        } else {
            // When idempotence is disabled, use the configured acks value or default to "all"
            props.put(ProducerConfig.ACKS_CONFIG, config.getAcks() != null ? config.getAcks().toString() : "all");
            // Always set idempotence property explicitly
            props.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG,
                     config.getEnableIdempotence() != null ? config.getEnableIdempotence().toString() : "false");
        }

        if (config.getBatchSize() != null) props.put(ProducerConfig.BATCH_SIZE_CONFIG, config.getBatchSize());
        if (config.getLingerMs() != null) props.put(ProducerConfig.LINGER_MS_CONFIG, config.getLingerMs());
        if (config.getBufferMemory() != null) props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, config.getBufferMemory());
        if (config.getRetries() != null) props.put(ProducerConfig.RETRIES_CONFIG, config.getRetries());
        if (config.getMaxInFlightRequestsPerConnection() != null)
            props.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, config.getMaxInFlightRequestsPerConnection());
        if (config.getDeliveryTimeoutMs() != null) props.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG, config.getDeliveryTimeoutMs());
        if (config.getRequestTimeoutMs() != null) props.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, config.getRequestTimeoutMs());
        if (config.getCompressionType() != null) props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, config.getCompressionType());
        
        // Security configs
        if (config.getSecurityProtocol() != null) props.put("security.protocol", config.getSecurityProtocol());
        if (config.getSaslMechanism() != null) props.put("sasl.mechanism", config.getSaslMechanism());
        if (config.getSaslJaasConfig() != null && config.getUsername() != null && config.getPassword() != null) {
            String saslJaasConfig = config.getSaslJaasConfig() + " username=\"" + config.getUsername() + "\" password=\"" + config.getPassword() + "\";";
            props.put("sasl.jaas.config", saslJaasConfig);
        }
        if (config.getSslTruststoreLocation() != null) props.put("ssl.truststore.location", config.getSslTruststoreLocation());
        if (config.getSslTruststorePassword() != null) props.put("ssl.truststore.password", config.getSslTruststorePassword());
        if (config.getSslTruststoreType() != null) props.put("ssl.truststore.type", config.getSslTruststoreType());
        if (config.getProtocols() != null) props.put("ssl.enabled.protocols", config.getProtocols());
        if (config.getSslKeystoreLocation() != null) props.put("ssl.keystore.location", config.getSslKeystoreLocation());
        if (config.getSslKeystorePassword() != null) props.put("ssl.keystore.password", config.getSslKeystorePassword());
        if (config.getSslKeyPassword() != null) props.put("ssl.key.password", config.getSslKeyPassword());
        
        if (config.getParameters() != null) {
            config.getParameters().forEach(props::putIfAbsent);
        }
        
        return props;
    }

    private Properties createConsumerProperties(KafkaClientConfig config) {
        Properties props = new Properties();
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, config.getBootstrapServers());
        props.put(ConsumerConfig.GROUP_ID_CONFIG, config.getGroupId());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, ByteArrayDeserializer.class.getName());
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, ByteArrayDeserializer.class.getName());

        if (config.getAutoOffsetReset() != null) props.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, config.getAutoOffsetReset());
        if (config.getMaxPollRecords() != null) props.put(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, config.getMaxPollRecords());
        if (config.getFetchMinBytes() != null) props.put(ConsumerConfig.FETCH_MIN_BYTES_CONFIG, config.getFetchMinBytes());
        if (config.getFetchMaxBytes() != null) props.put(ConsumerConfig.FETCH_MAX_BYTES_CONFIG, config.getFetchMaxBytes());
        if (config.getMaxPartitionFetchBytes() != null) props.put(ConsumerConfig.MAX_PARTITION_FETCH_BYTES_CONFIG, config.getMaxPartitionFetchBytes());
        if (config.getSessionTimeoutMs() != null) props.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, config.getSessionTimeoutMs());
        if (config.getHeartbeatIntervalMs() != null) props.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, config.getHeartbeatIntervalMs());
        if (config.getEnableAutoCommit() != null) props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, config.getEnableAutoCommit());
        if (config.getAutoCommitIntervalMs() != null) props.put(ConsumerConfig.AUTO_COMMIT_INTERVAL_MS_CONFIG, config.getAutoCommitIntervalMs());
        if (config.getMaxPollIntervalMs() != null) props.put(ConsumerConfig.MAX_POLL_INTERVAL_MS_CONFIG, config.getMaxPollIntervalMs());
        if (config.getRequestTimeoutMs() != null) props.put(ConsumerConfig.REQUEST_TIMEOUT_MS_CONFIG, config.getRequestTimeoutMs());
        if (config.getRetries() != null) props.put(ConsumerConfig.RETRY_BACKOFF_MS_CONFIG, config.getRetries());
        if (config.getIsolationLevel() != null) props.put(ConsumerConfig.ISOLATION_LEVEL_CONFIG, config.getIsolationLevel());
        if (config.getAllowAutoCreateTopics() != null) props.put(ConsumerConfig.ALLOW_AUTO_CREATE_TOPICS_CONFIG, config.getAllowAutoCreateTopics());
        if (config.getClientId() != null) props.put(ConsumerConfig.CLIENT_ID_CONFIG, config.getClientId());

        // Security configs
        if (config.getSecurityProtocol() != null) props.put("security.protocol", config.getSecurityProtocol());
        if (config.getSaslMechanism() != null) props.put("sasl.mechanism", config.getSaslMechanism());
        if (config.getSaslJaasConfig() != null && config.getUsername() != null && config.getPassword() != null) {
            String saslJaasConfig = config.getSaslJaasConfig() + " username=\"" + config.getUsername() + "\" password=\"" + config.getPassword() + "\";";
            props.put("sasl.jaas.config", saslJaasConfig);
        }
        if (config.getSslTruststoreLocation() != null) props.put("ssl.truststore.location", config.getSslTruststoreLocation());
        if (config.getSslTruststorePassword() != null) props.put("ssl.truststore.password", config.getSslTruststorePassword());
        if (config.getSslTruststoreType() != null) props.put("ssl.truststore.type", config.getSslTruststoreType());
        if (config.getProtocols() != null) props.put("ssl.enabled.protocols", config.getProtocols());
        if (config.getSslKeystoreLocation() != null) props.put("ssl.keystore.location", config.getSslKeystoreLocation());
        if (config.getSslKeystorePassword() != null) props.put("ssl.keystore.password", config.getSslKeystorePassword());
        if (config.getSslKeyPassword() != null) props.put("ssl.key.password", config.getSslKeyPassword());

        if (config.getParameters() != null) {
            config.getParameters().forEach(props::putIfAbsent);
        }

        return props;
    }

    /**
     * Functional interface for handling received messages
     */
    @FunctionalInterface
    public interface MessageHandler {
        void handleMessage(KafkaMessage message);
    }

    /**
     * Represents a Kafka message with metadata
     */
    public static class KafkaMessage {
        private final String content;
        private final String topic;
        private final int partition;
        private final long offset;
        private final Map<String, String> headers;

        public KafkaMessage(String content, String topic, int partition, long offset, Map<String, String> headers) {
            this.content = content;
            this.topic = topic;
            this.partition = partition;
            this.offset = offset;
            this.headers = headers != null ? new HashMap<>(headers) : new HashMap<>();
        }

        public String getContent() { return content; }
        public String getTopic() { return topic; }
        public int getPartition() { return partition; }
        public long getOffset() { return offset; }
        public Map<String, String> getHeaders() { return new HashMap<>(headers); }

        @Override
        public String toString() {
            return String.format("KafkaMessage{topic='%s', partition=%d, offset=%d, content='%s', headers=%s}",
                               topic, partition, offset, content, headers);
        }
    }
}
