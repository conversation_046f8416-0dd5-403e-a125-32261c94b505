adapters:
  - type: rabbitmq
    propertyRef: rabbit1
    host: rabbit-server
    port: 5672
    virtualHost: /
    queueName: orderQueue
    authenticationType: TLS
    username: rabbituser
    password: rabbitpassword
    sslTruststoreLocation: /etc/rabbit/cacerts
    sslTruststorePassword: changeit
    sslKeystoreLocation: /etc/rabbit/keystore
    sslKeystorePassword: changeit
    sslKeyPassword: changeit
    concurrency: 4
    prefetchCount: 20
    acknowledgeMode: AUTO
    compressed: false
    headersToExtract: [ traceparent, businessKey, x-request-id ]