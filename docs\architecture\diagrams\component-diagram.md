# Component Diagram

## HIP Services - Component Overview

```mermaid
graph TD
    subgraph Client Layer
        A[Web Browser] -->|HTTP/HTTPS| B[API Gateway]
        C[Mobile App] -->|HTTP/HTTPS| B
        D[External Systems] -->|SFTP/HTTP/JMS| E[Integration Adapters]
    end

    subgraph API Gateway Layer
        B -->|Route| F[Authentication Service]
        B -->|Route| G[Management API]
        B -->|Route| H[Integration API]
        B -->|Route| I[Metrics API]
    end

    subgraph Core Services
        J[HIPIntegrationOrchestrationService]
        K[HIPIntegrationRuntimeService]
        L[ServiceManager]
        M[ClusterCoordinator]
        
        J -->|Manages| K
        J -->|Uses| L
        J -->|Coordinates| M
    end

    subgraph Integration Runtime
        N[Adapters]
        O[Flow Steps]
        P[Handlers]
        
        N -->|Processes| O
        O -->|Routes| P
    end

    subgraph Data Access
        Q[Repositories]
        R[Entities]
        S[Cache]
        
        Q -->|Manages| R
        Q -->|Uses| S
    end

    subgraph External Systems
        T[Database]
        U[Message Broker]
        V[Object Storage]
    end

    %% Connections
    G --> J
    H --> K
    K --> N
    K --> O
    K --> P
    
    J --> Q
    
    N --> D
    P --> U
    Q --> T
    S --> V

    %% Styles
    classDef client fill:#f9f,stroke:#333,stroke-width:2px;
    classDef gateway fill:#bbf,stroke:#333,stroke-width:2px;
    classDef core fill:#f96,stroke:#333,stroke-width:2px;
    classDef runtime fill:#9f9,stroke:#333,stroke-width:2px;
    classDef data fill:#99f,stroke:#333,stroke-width:2px;
    classDef external fill:#f99,stroke:#333,stroke-width:2px;

    class A,B,C client;
    class F,G,H,I gateway;
    class J,K,L,M core;
    class N,O,P runtime;
    class Q,R,S data;
    class T,U,V external;
```

## Component Descriptions

### 1. Client Layer
- **Web Browser**: User interface for administration and monitoring
- **Mobile App**: Mobile access to the platform
- **External Systems**: Integrated systems using various protocols

### 2. API Gateway Layer
- **Authentication Service**: Handles JWT validation and user authentication
- **Management API**: CRUD operations for integration definitions
- **Integration API**: Runtime integration endpoints
- **Metrics API**: System and business metrics

### 3. Core Services
- **HIPIntegrationOrchestrationService**: Manages integration lifecycle
- **HIPIntegrationRuntimeService**: Executes integration flows
- **ServiceManager**: Manages service instances
- **ClusterCoordinator**: Handles cluster coordination

### 4. Integration Runtime
- **Adapters**: Protocol-specific connectors (SFTP, HTTP, JMS, etc.)
- **Flow Steps**: Processing steps (validation, transformation, etc.)
- **Handlers**: Success/error handling components

### 5. Data Access
- **Repositories**: Data access layer
- **Entities**: Domain model
- **Cache**: Performance optimization

### 6. External Systems
- **Database**: Persistent storage
- **Message Broker**: Asynchronous messaging
- **Object Storage**: File storage

## Component Interactions

1. **Integration Registration**:
   - Admin defines integration via Management API
   - HIPIntegrationOrchestrationService validates and stores the definition
   - ServiceManager registers the integration

2. **Message Processing**:
   - Adapter receives message from external system
   - Flow Steps process the message through the defined pipeline
   - Handlers manage the final disposition of the message

3. **Monitoring**:
   - Metrics are collected and exposed via Metrics API
   - Logs are aggregated for analysis
   - Alerts are generated for abnormal conditions
