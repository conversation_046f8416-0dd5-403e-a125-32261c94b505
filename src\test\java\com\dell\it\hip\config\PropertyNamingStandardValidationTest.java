package com.dell.it.hip.config;

import com.dell.it.hip.config.Handlers.*;
import com.dell.it.hip.config.adapters.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Validation test to ensure the properties-naming-standard.txt documentation
 * accurately reflects the actual @JsonProperty annotations in our configuration classes.
 * 
 * This test validates a representative sample of properties from each technology
 * to ensure the documentation is correct and up-to-date.
 */
public class PropertyNamingStandardValidationTest {

    private ObjectMapper objectMapper;

    @BeforeEach
    public void setUp() {
        objectMapper = new ObjectMapper();
    }

    @Test
    public void testKafkaPropertiesFromDocumentation() throws Exception {
        // Test Kafka consumer properties from documentation
        String kafkaConsumerJson = """
            {
              "kafka.consumer.bootstrap.servers": "localhost:9092",
              "kafka.consumer.topic.name": "test-topic",
              "kafka.consumer.group.id": "test-group",
              "kafka.consumer.security.protocol": "SASL_SSL",
              "kafka.consumer.ssl.truststore.location": "/path/to/truststore"
            }
            """;

        DynamicKafkaAdapterConfig adapterConfig = objectMapper.readValue(kafkaConsumerJson, DynamicKafkaAdapterConfig.class);
        assertNotNull(adapterConfig);
        assertEquals("localhost:9092", adapterConfig.getBootstrapServers());
        assertEquals("test-topic", adapterConfig.getTopic());
        assertEquals("test-group", adapterConfig.getGroupId());

        // Test Kafka producer properties from documentation
        String kafkaProducerJson = """
            {
              "kafka.producer.bootstrap.servers": "localhost:9092",
              "kafka.producer.topic": "output-topic",
              "kafka.producer.gzip.enabled": true,
              "kafka.producer.acks": 1,
              "kafka.producer.batch.size": 16384
            }
            """;

        DynamicKafkaHandlerConfig handlerConfig = objectMapper.readValue(kafkaProducerJson, DynamicKafkaHandlerConfig.class);
        assertNotNull(handlerConfig);
        assertEquals("localhost:9092", handlerConfig.getBootstrapServers());
        assertEquals("output-topic", handlerConfig.getTopic());
        assertTrue(handlerConfig.getGzipEnabled());
    }

    @Test
    public void testIbmmqPropertiesFromDocumentation() throws Exception {
        // Test IBM MQ consumer properties from documentation
        String ibmmqConsumerJson = """
            {
              "ibmmq.consumer.queue.manager": "QM1",
              "ibmmq.consumer.queue": "TEST.QUEUE",
              "ibmmq.consumer.conn.name": "localhost(1414)",
              "ibmmq.consumer.ssl.cipher.suite": "TLS_RSA_WITH_AES_256_CBC_SHA256"
            }
            """;

        DynamicIBMMQAdapterConfig adapterConfig = objectMapper.readValue(ibmmqConsumerJson, DynamicIBMMQAdapterConfig.class);
        assertNotNull(adapterConfig);
        assertEquals("QM1", adapterConfig.getQueueManager());
        assertEquals("TEST.QUEUE", adapterConfig.getQueueName());
        assertEquals("localhost(1414)", adapterConfig.getConnName());

        // Test IBM MQ producer properties from documentation
        String ibmmqProducerJson = """
            {
              "ibmmq.producer.queue.manager": "QM1",
              "ibmmq.producer.queue": "OUTPUT.QUEUE",
              "ibmmq.producer.gzip.enabled": false,
              "ibmmq.producer.persistent": true
            }
            """;

        DynamicIbmmqHandlerConfig handlerConfig = objectMapper.readValue(ibmmqProducerJson, DynamicIbmmqHandlerConfig.class);
        assertNotNull(handlerConfig);
        assertEquals("QM1", handlerConfig.getQueueManager());
        assertEquals("OUTPUT.QUEUE", handlerConfig.getQueue());
        assertFalse(handlerConfig.getGzipEnabled());
    }

    @Test
    public void testRabbitMQPropertiesFromDocumentation() throws Exception {
        // Test RabbitMQ consumer properties from documentation
        String rabbitmqConsumerJson = """
            {
              "rabbitmq.consumer.host": "localhost",
              "rabbitmq.consumer.port": 5672,
              "rabbitmq.consumer.queueName": "test.queue",
              "rabbitmq.consumer.username": "guest",
              "rabbitmq.consumer.concurrency": 5
            }
            """;

        DynamicRabbitMQAdapterConfig adapterConfig = objectMapper.readValue(rabbitmqConsumerJson, DynamicRabbitMQAdapterConfig.class);
        assertNotNull(adapterConfig);
        assertEquals("localhost", adapterConfig.getHost());
        assertEquals(Integer.valueOf(5672), adapterConfig.getPort());
        assertEquals("test.queue", adapterConfig.getQueueName());

        // Test RabbitMQ producer properties from documentation
        String rabbitmqProducerJson = """
            {
              "rabbitmq.producer.host": "localhost",
              "rabbitmq.producer.exchange": "test.exchange",
              "rabbitmq.producer.routing.key": "test.routing.key",
              "rabbitmq.producer.gzip.enabled": true
            }
            """;

        DynamicRabbitMQHandlerConfig handlerConfig = objectMapper.readValue(rabbitmqProducerJson, DynamicRabbitMQHandlerConfig.class);
        assertNotNull(handlerConfig);
        assertEquals("localhost", handlerConfig.getHost());
        assertEquals("test.exchange", handlerConfig.getExchange());
        assertEquals("test.routing.key", handlerConfig.getRoutingKey());
    }

    @Test
    public void testSftpPropertiesFromDocumentation() throws Exception {
        // Test SFTP consumer properties from documentation
        String sftpConsumerJson = """
            {
              "sftp.consumer.host": "sftp.example.com",
              "sftp.consumer.port": 22,
              "sftp.consumer.username": "testuser",
              "sftp.consumer.remote.directory": "/incoming",
              "sftp.consumer.polling.interval.ms": 60000
            }
            """;

        DynamicSFTPAdapterConfig adapterConfig = objectMapper.readValue(sftpConsumerJson, DynamicSFTPAdapterConfig.class);
        assertNotNull(adapterConfig);
        assertEquals("sftp.example.com", adapterConfig.getHost());
        assertEquals(Integer.valueOf(22), adapterConfig.getPort());
        assertEquals("testuser", adapterConfig.getUsername());

        // Test SFTP producer properties from documentation
        String sftpProducerJson = """
            {
              "sftp.producer.host": "sftp.example.com",
              "sftp.producer.port": 22,
              "sftp.producer.remote.directory": "/outgoing",
              "sftp.producer.gzip.enabled": false
            }
            """;

        DynamicSftpHandlerConfig handlerConfig = objectMapper.readValue(sftpProducerJson, DynamicSftpHandlerConfig.class);
        assertNotNull(handlerConfig);
        assertEquals("sftp.example.com", handlerConfig.getHost());
        assertEquals(22, handlerConfig.getPort());
        assertEquals("/outgoing", handlerConfig.getRemoteDirectory());
    }

    @Test
    public void testNasPropertiesFromDocumentation() throws Exception {
        // Test NAS consumer properties from documentation
        String nasConsumerJson = """
            {
              "nas.consumer.protocol": "smb",
              "nas.consumer.host": "nas.example.com",
              "nas.consumer.share.name": "shared",
              "nas.consumer.username": "nasuser",
              "nas.consumer.polling.interval.ms": 30000
            }
            """;

        DynamicNASAdapterConfig adapterConfig = objectMapper.readValue(nasConsumerJson, DynamicNASAdapterConfig.class);
        assertNotNull(adapterConfig);
        assertEquals("smb", adapterConfig.getProtocol());
        assertEquals("nas.example.com", adapterConfig.getHost());
        assertEquals("shared", adapterConfig.getShareName());

        // Test NAS producer properties from documentation
        String nasProducerJson = """
            {
              "nas.producer.protocol": "smb",
              "nas.producer.host": "nas.example.com",
              "nas.producer.share.name": "output",
              "nas.producer.file.separator": "/",
              "nas.producer.gzip.enabled": true
            }
            """;

        DynamicNasHandlerConfig handlerConfig = objectMapper.readValue(nasProducerJson, DynamicNasHandlerConfig.class);
        assertNotNull(handlerConfig);
        assertEquals("smb", handlerConfig.getProtocol());
        assertEquals("nas.example.com", handlerConfig.getHost());
        assertEquals("output", handlerConfig.getShareName());
    }

    @Test
    public void testHttpsPropertiesFromDocumentation() throws Exception {
        // Test HTTPS consumer properties from documentation
        String httpsConsumerJson = """
            {
              "https.consumer.api.key.header": "X-API-Key",
              "https.consumer.api.key.value": "test-key",
              "https.consumer.oauth.required": true,
              "https.consumer.max.concurrency": 10,
              "https.consumer.request.timeout.ms": 5000
            }
            """;

        DynamicHttpsAdapterConfig adapterConfig = objectMapper.readValue(httpsConsumerJson, DynamicHttpsAdapterConfig.class);
        assertNotNull(adapterConfig);
        assertEquals("X-API-Key", adapterConfig.getApiKeyHeader());
        assertEquals("test-key", adapterConfig.getApiKeyValue());
        assertTrue(adapterConfig.isOAuthRequired());

        // Test HTTPS producer properties from documentation
        String httpsProducerJson = """
            {
              "https.producer.endpoint.url": "https://api.example.com/webhook",
              "https.producer.http.method": "POST",
              "https.producer.api.key.header": "Authorization",
              "https.producer.oauth.enabled": true,
              "https.producer.connect.timeout.ms": 3000
            }
            """;

        DynamicHttpsHandlerConfig handlerConfig = objectMapper.readValue(httpsProducerJson, DynamicHttpsHandlerConfig.class);
        assertNotNull(handlerConfig);
        assertEquals("https://api.example.com/webhook", handlerConfig.getEndpointUrl());
        assertEquals("POST", handlerConfig.getHttpMethod());
        assertEquals("Authorization", handlerConfig.getApiKeyHeader());
    }

    @Test
    public void testPropertyNamingConventionConsistency() {
        // This test validates that our naming convention is consistently applied
        // All consumer properties should start with {technology}.consumer.
        // All producer properties should start with {technology}.producer.
        
        // This is validated by the successful deserialization tests above
        // If any property name was incorrect, the JSON deserialization would fail
        
        assertTrue(true, "Property naming convention consistency validated through successful deserialization tests");
    }
}
