package com.dell.it.hip.util.dataformatUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Scanner;
public class CsvUtil {
    /**
     * Extract a field by column name or zero-based column index.
     * @param csvLine a single line of CSV data (as String)
     * @param expression column name or "col[index]" (e.g., "col[2]")
     * @param headers list of column names, or null if unknown
     */
    public static String extractField(String csvLine, String expression, List<String> headers) {
        if (csvLine == null || expression == null) return null;
        String[] tokens = csvLine.split(",", -1); // don't discard trailing empty columns
        if (expression.startsWith("col[")) {
            int idx = Integer.parseInt(expression.substring(4, expression.length() - 1));
            return (idx >= 0 && idx < tokens.length) ? tokens[idx] : null;
        } else if (headers != null && !headers.isEmpty()) {
            for (int i = 0; i < headers.size(); i++) {
                if (headers.get(i).equalsIgnoreCase(expression)) {
                    return tokens[i];
                }
            }
        }
        return null;
    }
    // Convenience: for CSV without headers, use index form: "col[0]", "col[1]", etc.
    public static String extractField(String csvLine, String expression) {
        return extractField(csvLine, expression, null);
    }

    // Split CSV into lines, optionally handle header row
    public static List<String> splitLines(String csv) {
        if (csv == null || csv.isEmpty()) return Collections.emptyList();
        List<String> lines = new ArrayList<>();
        try (Scanner scanner = new Scanner(csv)) {
            while (scanner.hasNextLine()) {
                lines.add(scanner.nextLine());
            }
        }
        return lines;
    }
    // Optionally: split and keep header separate
    public static List<String[]> splitLinesWithHeader(String csv) {
        if (csv == null || csv.isEmpty()) return Collections.emptyList();
        List<String[]> rows = new ArrayList<>();
        try (Scanner scanner = new Scanner(csv)) {
            while (scanner.hasNextLine()) {
                String line = scanner.nextLine();
                rows.add(line.split(",(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)", -1)); // naive CSV split (not RFC 4180)
            }
        }
        return rows;
    }
}