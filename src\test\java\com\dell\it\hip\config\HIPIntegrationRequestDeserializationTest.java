package com.dell.it.hip.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive JUnit test for HIPIntegrationRequest JSON deserialization.
 * Tests the new tags array format with key-value pairs and ensures backward compatibility.
 */
public class HIPIntegrationRequestDeserializationTest {

    private ObjectMapper objectMapper;

    @BeforeEach
    public void setUp() {
        objectMapper = new ObjectMapper();
    }

    @Test
    public void testDeserializeFromInputJsonFile() throws IOException {
        // Read the JSON file from test resources
        ClassPathResource resource = new ClassPathResource("input.json");
        String jsonContent = new String(Files.readAllBytes(Paths.get(resource.getURI())));

        // Deserialize to HIPIntegrationRequest
        HIPIntegrationRequest request = objectMapper.readValue(jsonContent, HIPIntegrationRequest.class);

        // Validate basic fields
        assertNotNull(request);
        assertEquals("testing-doctype1", request.getHipIntegrationName());
        assertEquals("1.0", request.getVersion());
        assertEquals("INBOUND", request.getHipIntegrationType());
        assertEquals("PAYMENT", request.getBusinessFlowType());
        assertEquals("payment-team", request.getOwner());
        assertEquals("testing-doctype-flowname", request.getBusinessFlowName());
        assertEquals("1.0", request.getBusinessFlowVersion());

        // Validate tags array deserialization
        List<Tag> tags = request.getTags();
        assertNotNull(tags, "Tags should not be null");
        assertEquals(1, tags.size(), "Should have exactly one tag");

        Tag tag = tags.get(0);
        assertNotNull(tag, "Tag should not be null");
        assertEquals("test key", tag.getKey(), "Tag key should match");
        assertEquals("test value", tag.getValue(), "Tag value should match");

        // Validate adapters
        assertNotNull(request.getAdapters());
        assertEquals(1, request.getAdapters().size());
        assertEquals("ibmmqAdapter", request.getAdapters().get(0).getType());
        assertEquals("ibmAdp", request.getAdapters().get(0).getPropertyRef());

        // Validate flow steps
        assertNotNull(request.getFlowSteps());
        assertEquals(2, request.getFlowSteps().size());
        assertEquals("docTypeProcessor", request.getFlowSteps().get(0).getType());
        assertEquals("flowRouting", request.getFlowSteps().get(1).getType());

        // Validate handlers
        assertNotNull(request.getHandlers());
        assertEquals(1, request.getHandlers().size());
        assertEquals("kafkaHandler", request.getHandlers().get(0).getType());
        assertEquals("primary", request.getHandlers().get(0).getRole());

        // Validate property sheets
        assertNotNull(request.getPropertySheets());
        assertEquals(4, request.getPropertySheets().size());
        assertTrue(request.getPropertySheets().contains("ibmAdp"));
        assertTrue(request.getPropertySheets().contains("test-kafka-producer"));

        // Validate throttle settings
        assertNotNull(request.getThrottleSettings());
        assertTrue(request.getThrottleSettings().isEnabled());
        assertEquals(0, request.getThrottleSettings().getMaxMessagesPerPeriod());
        assertEquals(0, request.getThrottleSettings().getPeriodSeconds());

        // Validate other fields
        assertEquals("hipServiceManagertest1", request.getServiceManagerName());
        assertEquals("Active", request.getStatus());
    }

    @Test
    public void testNewTagsFormatDeserialization() throws IOException {
        String json = """
            {
              "hipIntegrationName": "test-integration",
              "version": "1.0",
              "tags": [
                {"key": "environment", "value": "production"},
                {"key": "team", "value": "payments"},
                {"key": "priority", "value": "high"}
              ]
            }
            """;

        HIPIntegrationRequest request = objectMapper.readValue(json, HIPIntegrationRequest.class);

        assertNotNull(request);
        assertEquals("test-integration", request.getHipIntegrationName());
        assertEquals("1.0", request.getVersion());

        List<Tag> tags = request.getTags();
        assertNotNull(tags);
        assertEquals(3, tags.size());

        // Verify first tag
        Tag envTag = tags.get(0);
        assertEquals("environment", envTag.getKey());
        assertEquals("production", envTag.getValue());

        // Verify second tag
        Tag teamTag = tags.get(1);
        assertEquals("team", teamTag.getKey());
        assertEquals("payments", teamTag.getValue());

        // Verify third tag
        Tag priorityTag = tags.get(2);
        assertEquals("priority", priorityTag.getKey());
        assertEquals("high", priorityTag.getValue());
    }

    @Test
    public void testLegacyStringArrayTagsDeserialization() throws IOException {
        String json = """
            {
              "hipIntegrationName": "legacy-integration",
              "version": "1.0",
              "tags": ["orders", "processing", "v1.2"]
            }
            """;

        HIPIntegrationRequest request = objectMapper.readValue(json, HIPIntegrationRequest.class);

        assertNotNull(request);
        List<Tag> tags = request.getTags();
        assertNotNull(tags);
        assertEquals(3, tags.size());

        // Verify legacy tags are converted with "legacy" key
        assertEquals("legacy", tags.get(0).getKey());
        assertEquals("orders", tags.get(0).getValue());

        assertEquals("legacy", tags.get(1).getKey());
        assertEquals("processing", tags.get(1).getValue());

        assertEquals("legacy", tags.get(2).getKey());
        assertEquals("v1.2", tags.get(2).getValue());
    }

    @Test
    public void testLegacyCommaSeparatedStringTagsDeserialization() throws IOException {
        String json = """
            {
              "hipIntegrationName": "legacy-integration",
              "version": "1.0",
              "tags": "orders,processing,v1.2"
            }
            """;

        HIPIntegrationRequest request = objectMapper.readValue(json, HIPIntegrationRequest.class);

        assertNotNull(request);
        List<Tag> tags = request.getTags();
        assertNotNull(tags);
        assertEquals(3, tags.size());

        // Verify legacy comma-separated tags are converted
        assertEquals("legacy", tags.get(0).getKey());
        assertEquals("orders", tags.get(0).getValue());

        assertEquals("legacy", tags.get(1).getKey());
        assertEquals("processing", tags.get(1).getValue());

        assertEquals("legacy", tags.get(2).getKey());
        assertEquals("v1.2", tags.get(2).getValue());
    }

    @Test
    public void testEmptyTagsDeserialization() throws IOException {
        String json = """
            {
              "hipIntegrationName": "empty-tags-integration",
              "version": "1.0",
              "tags": []
            }
            """;

        HIPIntegrationRequest request = objectMapper.readValue(json, HIPIntegrationRequest.class);

        assertNotNull(request);
        List<Tag> tags = request.getTags();
        assertNotNull(tags);
        assertTrue(tags.isEmpty());
    }

    @Test
    public void testNullTagsDeserialization() throws IOException {
        String json = """
            {
              "hipIntegrationName": "null-tags-integration",
              "version": "1.0"
            }
            """;

        HIPIntegrationRequest request = objectMapper.readValue(json, HIPIntegrationRequest.class);

        assertNotNull(request);
        // Tags should be null when not present in JSON
        assertNull(request.getTags());
    }

    @Test
    public void testTagsConvenienceMethodsForBackwardCompatibility() {
        HIPIntegrationRequest request = new HIPIntegrationRequest();

        // Test setTagsFromString method
        request.setTagsFromString("tag1,tag2,tag3");
        List<Tag> tags = request.getTags();
        assertNotNull(tags);
        assertEquals(3, tags.size());
        assertEquals("legacy", tags.get(0).getKey());
        assertEquals("tag1", tags.get(0).getValue());

        // Test getTagsAsString method - legacy tags should return just values for backward compatibility
        String tagsString = request.getTagsAsString();
        assertEquals("tag1,tag2,tag3", tagsString);

        // Test with mixed tag types
        request.getTags().add(new Tag("environment", "production"));
        String mixedTagsString = request.getTagsAsString();
        assertTrue(mixedTagsString.contains("tag1"));
        assertTrue(mixedTagsString.contains("tag2"));
        assertTrue(mixedTagsString.contains("tag3"));
        assertTrue(mixedTagsString.contains("environment:production"));
    }

    @Test
    public void testSerializationRoundTrip() throws IOException {
        // Create a request with new tag format
        HIPIntegrationRequest originalRequest = new HIPIntegrationRequest();
        originalRequest.setHipIntegrationName("roundtrip-test");
        originalRequest.setVersion("1.0");
        
        List<Tag> tags = List.of(
            new Tag("environment", "production"),
            new Tag("team", "payments")
        );
        originalRequest.setTags(tags);

        // Serialize to JSON
        String json = objectMapper.writeValueAsString(originalRequest);

        // Deserialize back
        HIPIntegrationRequest deserializedRequest = objectMapper.readValue(json, HIPIntegrationRequest.class);

        // Verify round trip
        assertNotNull(deserializedRequest);
        assertEquals(originalRequest.getHipIntegrationName(), deserializedRequest.getHipIntegrationName());
        assertEquals(originalRequest.getVersion(), deserializedRequest.getVersion());
        
        List<Tag> deserializedTags = deserializedRequest.getTags();
        assertNotNull(deserializedTags);
        assertEquals(2, deserializedTags.size());
        assertEquals("environment", deserializedTags.get(0).getKey());
        assertEquals("production", deserializedTags.get(0).getValue());
        assertEquals("team", deserializedTags.get(1).getKey());
        assertEquals("payments", deserializedTags.get(1).getValue());
    }
}
