package com.dell.it.hip.strategy.flows.rules;

import java.util.Map;

import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.ContivoMap;
import com.dell.it.hip.config.rules.Rule;
import com.dell.it.hip.util.redis.ContivoMapCache;
public class SelectContivoMapRuleAction implements RuleAction {
    private final ContivoMapCache contivoMapCache;

    public SelectContivoMapRuleAction(ContivoMapCache contivoMapCache) {
        this.contivoMapCache = contivoMapCache;
    }

    @Override
    public String getName() { return "SelectContivoMap"; }
    @Override
    public String getType() { return "SelectContivoMap"; }

    @Override
    public Message<?> performAction(
            Message<?> message,
            Map<String, Object> params,
            Rule rule,
            HIPIntegrationDefinition def,
            Map<String, Object> context
    ) {
        String mapIdentifier = (String) params.get("mapIdentifier");
        String mapIdentifierVersion = (String) params.get("mapIdentifierVersion");
        if (mapIdentifier == null || mapIdentifierVersion == null) {
            throw new IllegalArgumentException("Missing mapIdentifier or mapIdentifierVersion param in SelectContivoMapRuleAction");
        }

        // Load and cache map
        ContivoMap contivoMap = contivoMapCache.getOrLoad(mapIdentifier, mapIdentifierVersion);

        // Put only the identifier info in the header (for trace/debug)
        Message<?> newMsg = MessageBuilder.fromMessage(message)
                .setHeader("HIP.contivoMapId", mapIdentifier)
                .setHeader("HIP.contivoMapVersion", mapIdentifierVersion)
                .build();

        // Put the actual map object in context for use by the flow step
        if (context != null) {
            context.put("contivoMap", contivoMap);
        }
        return newMsg;
    }
}