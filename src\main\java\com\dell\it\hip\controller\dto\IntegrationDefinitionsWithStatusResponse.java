package com.dell.it.hip.controller.dto;

import java.util.List;
import java.util.Map;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.IntegrationStatus;

/**
 * Response wrapper for the /{name}/definitions endpoint that includes both
 * integration definitions and their status information.
 * 
 * This maintains backward compatibility while providing enhanced functionality
 * by separating definitions and status data into distinct fields.
 */
public class IntegrationDefinitionsWithStatusResponse {
    
    /**
     * List of integration definitions (unchanged from original response)
     */
    private List<HIPIntegrationDefinition> definitions;
    
    /**
     * Status information organized by version
     * Key: version string, Value: IntegrationStatus
     */
    private Map<String, IntegrationStatus> status;
    
    /**
     * Default constructor
     */
    public IntegrationDefinitionsWithStatusResponse() {
    }
    
    /**
     * Constructor with all fields
     * 
     * @param definitions List of integration definitions
     * @param status Map of version to status
     */
    public IntegrationDefinitionsWithStatusResponse(List<HIPIntegrationDefinition> definitions, 
                                                   Map<String, IntegrationStatus> status) {
        this.definitions = definitions;
        this.status = status;
    }
    
    /**
     * Get the integration definitions
     * 
     * @return List of HIPIntegrationDefinition objects
     */
    public List<HIPIntegrationDefinition> getDefinitions() {
        return definitions;
    }
    
    /**
     * Set the integration definitions
     * 
     * @param definitions List of HIPIntegrationDefinition objects
     */
    public void setDefinitions(List<HIPIntegrationDefinition> definitions) {
        this.definitions = definitions;
    }
    
    /**
     * Get the status information organized by version
     * 
     * @return Map of version string to IntegrationStatus
     */
    public Map<String, IntegrationStatus> getStatus() {
        return status;
    }
    
    /**
     * Set the status information
     * 
     * @param status Map of version string to IntegrationStatus
     */
    public void setStatus(Map<String, IntegrationStatus> status) {
        this.status = status;
    }
    
    @Override
    public String toString() {
        return "IntegrationDefinitionsWithStatusResponse{" +
                "definitions=" + (definitions != null ? definitions.size() + " items" : "null") +
                ", status=" + (status != null ? status.size() + " versions" : "null") +
                '}';
    }
}
