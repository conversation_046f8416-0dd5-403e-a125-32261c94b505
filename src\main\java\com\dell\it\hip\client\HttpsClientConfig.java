package com.dell.it.hip.client;

import java.util.List;
import java.util.Map;

/**
 * Configuration class for HttpsClient that mirrors the property structure
 * used in DynamicHttpsAdapterConfig and DynamicHttpsHandlerConfig.
 * 
 * This class can be used for both consumer (adapter testing) and producer (handler testing)
 * configurations by using the appropriate property prefixes.
 * 
 * Supports HTTPS operations with authentication, OAuth2, and various HTTP methods.
 */
public class HttpsClientConfig {
    
    // Core HTTPS connection properties
    private String endpointUrl;
    private String httpMethod = "POST"; // GET, POST, PUT, etc.
    private Map<String, String> headers; // default headers
    
    // Authentication
    private String apiKeyHeader; // e.g. "x-api-key"
    private String apiKeyValue;
    private boolean oAuthRequired = false;
    
    // Connection settings
    private Long connectTimeoutMs = 10000L; // Default 10 seconds
    private Long readTimeoutMs = 30000L; // Default 30 seconds
    private Integer maxInMemorySize = 2 * 1024 * 1024; // Default 2MB
    
    // Consumer-specific properties (adapter testing)
    private List<String> headersToExtract;
    private Integer maxRequestSizeBytes = 2 * 1024 * 1024; // Default 2MB
    private Integer maxConcurrency = 100;
    private Integer requestTimeoutMs = 30000; // per request, ms
    private Integer rateLimitPerSecond; // soft throttle
    private List<String> allowedHttpMethods;
    
    // Producer-specific properties (handler testing)
    private Integer retryAttempts = 1;
    private Long retryBackoffMs = 1000L;
    private Boolean compressed = false; // gzip or not
    
    // OAuth2 properties
    private Boolean oauthEnabled = false;
    private String oauthTokenUrl;
    private String oauthClientId;
    private String oauthClientSecret;
    private String oauthScope;
    private String oauthAudience;
    private Map<String, String> oauthAdditionalParams;
    
    // Default constructor
    public HttpsClientConfig() {}
    
    // Builder pattern constructor
    private HttpsClientConfig(Builder builder) {
        this.endpointUrl = builder.endpointUrl;
        this.httpMethod = builder.httpMethod;
        this.headers = builder.headers;
        this.apiKeyHeader = builder.apiKeyHeader;
        this.apiKeyValue = builder.apiKeyValue;
        this.oAuthRequired = builder.oAuthRequired;
        this.connectTimeoutMs = builder.connectTimeoutMs;
        this.readTimeoutMs = builder.readTimeoutMs;
        this.maxInMemorySize = builder.maxInMemorySize;
        this.headersToExtract = builder.headersToExtract;
        this.maxRequestSizeBytes = builder.maxRequestSizeBytes;
        this.maxConcurrency = builder.maxConcurrency;
        this.requestTimeoutMs = builder.requestTimeoutMs;
        this.rateLimitPerSecond = builder.rateLimitPerSecond;
        this.allowedHttpMethods = builder.allowedHttpMethods;
        this.retryAttempts = builder.retryAttempts;
        this.retryBackoffMs = builder.retryBackoffMs;
        this.compressed = builder.compressed;
        this.oauthEnabled = builder.oauthEnabled;
        this.oauthTokenUrl = builder.oauthTokenUrl;
        this.oauthClientId = builder.oauthClientId;
        this.oauthClientSecret = builder.oauthClientSecret;
        this.oauthScope = builder.oauthScope;
        this.oauthAudience = builder.oauthAudience;
        this.oauthAdditionalParams = builder.oauthAdditionalParams;
    }
    
    /**
     * Create a consumer configuration for testing HTTPS adapter
     */
    public static HttpsClientConfig createConsumerConfig() {
        return new Builder()
            .endpointUrl("https://httpbin.org/post")
            .httpMethod("POST")
            .maxConcurrency(100)
            .maxRequestSizeBytes(2 * 1024 * 1024)
            .requestTimeoutMs(30000)
            .build();
    }
    
    /**
     * Create a producer configuration for testing HTTPS handler
     */
    public static HttpsClientConfig createProducerConfig() {
        return new Builder()
            .endpointUrl("https://httpbin.org/post")
            .httpMethod("POST")
            .connectTimeoutMs(10000L)
            .readTimeoutMs(30000L)
            .maxInMemorySize(2 * 1024 * 1024)
            .retryAttempts(3)
            .retryBackoffMs(1000L)
            .compressed(false)
            .build();
    }
    
    /**
     * Create a configuration with API key authentication
     */
    public static HttpsClientConfig createApiKeyConfig(String url, String keyHeader, String keyValue) {
        return new Builder()
            .endpointUrl(url)
            .httpMethod("POST")
            .apiKeyHeader(keyHeader)
            .apiKeyValue(keyValue)
            .connectTimeoutMs(10000L)
            .readTimeoutMs(30000L)
            .retryAttempts(3)
            .build();
    }
    
    /**
     * Create a configuration with OAuth2 authentication
     */
    public static HttpsClientConfig createOAuth2Config(String url, String tokenUrl, String clientId, String clientSecret) {
        return new Builder()
            .endpointUrl(url)
            .httpMethod("POST")
            .oauthEnabled(true)
            .oauthTokenUrl(tokenUrl)
            .oauthClientId(clientId)
            .oauthClientSecret(clientSecret)
            .connectTimeoutMs(10000L)
            .readTimeoutMs(30000L)
            .retryAttempts(3)
            .build();
    }
    
    // Getters and setters
    public String getEndpointUrl() { return endpointUrl; }
    public void setEndpointUrl(String endpointUrl) { this.endpointUrl = endpointUrl; }
    
    public String getHttpMethod() { return httpMethod; }
    public void setHttpMethod(String httpMethod) { this.httpMethod = httpMethod; }
    
    public Map<String, String> getHeaders() { return headers; }
    public void setHeaders(Map<String, String> headers) { this.headers = headers; }
    
    public String getApiKeyHeader() { return apiKeyHeader; }
    public void setApiKeyHeader(String apiKeyHeader) { this.apiKeyHeader = apiKeyHeader; }
    
    public String getApiKeyValue() { return apiKeyValue; }
    public void setApiKeyValue(String apiKeyValue) { this.apiKeyValue = apiKeyValue; }
    
    public boolean isOAuthRequired() { return oAuthRequired; }
    public void setOAuthRequired(boolean oAuthRequired) { this.oAuthRequired = oAuthRequired; }
    
    public Long getConnectTimeoutMs() { return connectTimeoutMs; }
    public void setConnectTimeoutMs(Long connectTimeoutMs) { this.connectTimeoutMs = connectTimeoutMs; }
    
    public Long getReadTimeoutMs() { return readTimeoutMs; }
    public void setReadTimeoutMs(Long readTimeoutMs) { this.readTimeoutMs = readTimeoutMs; }
    
    public Integer getMaxInMemorySize() { return maxInMemorySize; }
    public void setMaxInMemorySize(Integer maxInMemorySize) { this.maxInMemorySize = maxInMemorySize; }
    
    public List<String> getHeadersToExtract() { return headersToExtract; }
    public void setHeadersToExtract(List<String> headersToExtract) { this.headersToExtract = headersToExtract; }
    
    public Integer getMaxRequestSizeBytes() { return maxRequestSizeBytes; }
    public void setMaxRequestSizeBytes(Integer maxRequestSizeBytes) { this.maxRequestSizeBytes = maxRequestSizeBytes; }
    
    public Integer getMaxConcurrency() { return maxConcurrency; }
    public void setMaxConcurrency(Integer maxConcurrency) { this.maxConcurrency = maxConcurrency; }
    
    public Integer getRequestTimeoutMs() { return requestTimeoutMs; }
    public void setRequestTimeoutMs(Integer requestTimeoutMs) { this.requestTimeoutMs = requestTimeoutMs; }
    
    public Integer getRateLimitPerSecond() { return rateLimitPerSecond; }
    public void setRateLimitPerSecond(Integer rateLimitPerSecond) { this.rateLimitPerSecond = rateLimitPerSecond; }
    
    public List<String> getAllowedHttpMethods() { return allowedHttpMethods; }
    public void setAllowedHttpMethods(List<String> allowedHttpMethods) { this.allowedHttpMethods = allowedHttpMethods; }
    
    public Integer getRetryAttempts() { return retryAttempts; }
    public void setRetryAttempts(Integer retryAttempts) { this.retryAttempts = retryAttempts; }
    
    public Long getRetryBackoffMs() { return retryBackoffMs; }
    public void setRetryBackoffMs(Long retryBackoffMs) { this.retryBackoffMs = retryBackoffMs; }
    
    public Boolean getCompressed() { return compressed; }
    public void setCompressed(Boolean compressed) { this.compressed = compressed; }
    
    public Boolean getOauthEnabled() { return oauthEnabled; }
    public void setOauthEnabled(Boolean oauthEnabled) { this.oauthEnabled = oauthEnabled; }
    
    public Boolean isOauthEnabled() { return oauthEnabled; }
    
    public String getOauthTokenUrl() { return oauthTokenUrl; }
    public void setOauthTokenUrl(String oauthTokenUrl) { this.oauthTokenUrl = oauthTokenUrl; }
    
    public String getOauthClientId() { return oauthClientId; }
    public void setOauthClientId(String oauthClientId) { this.oauthClientId = oauthClientId; }
    
    public String getOauthClientSecret() { return oauthClientSecret; }
    public void setOauthClientSecret(String oauthClientSecret) { this.oauthClientSecret = oauthClientSecret; }
    
    public String getOauthScope() { return oauthScope; }
    public void setOauthScope(String oauthScope) { this.oauthScope = oauthScope; }
    
    public String getOauthAudience() { return oauthAudience; }
    public void setOauthAudience(String oauthAudience) { this.oauthAudience = oauthAudience; }
    
    public Map<String, String> getOauthAdditionalParams() { return oauthAdditionalParams; }
    public void setOauthAdditionalParams(Map<String, String> oauthAdditionalParams) { this.oauthAdditionalParams = oauthAdditionalParams; }

    // Builder pattern for easy configuration
    public static class Builder {
        private String endpointUrl;
        private String httpMethod = "POST";
        private Map<String, String> headers;
        private String apiKeyHeader;
        private String apiKeyValue;
        private boolean oAuthRequired = false;
        private Long connectTimeoutMs = 10000L;
        private Long readTimeoutMs = 30000L;
        private Integer maxInMemorySize = 2 * 1024 * 1024;
        private List<String> headersToExtract;
        private Integer maxRequestSizeBytes = 2 * 1024 * 1024;
        private Integer maxConcurrency = 100;
        private Integer requestTimeoutMs = 30000;
        private Integer rateLimitPerSecond;
        private List<String> allowedHttpMethods;
        private Integer retryAttempts = 1;
        private Long retryBackoffMs = 1000L;
        private Boolean compressed = false;
        private Boolean oauthEnabled = false;
        private String oauthTokenUrl;
        private String oauthClientId;
        private String oauthClientSecret;
        private String oauthScope;
        private String oauthAudience;
        private Map<String, String> oauthAdditionalParams;

        public Builder endpointUrl(String endpointUrl) { this.endpointUrl = endpointUrl; return this; }
        public Builder httpMethod(String httpMethod) { this.httpMethod = httpMethod; return this; }
        public Builder headers(Map<String, String> headers) { this.headers = headers; return this; }
        public Builder apiKeyHeader(String apiKeyHeader) { this.apiKeyHeader = apiKeyHeader; return this; }
        public Builder apiKeyValue(String apiKeyValue) { this.apiKeyValue = apiKeyValue; return this; }
        public Builder oAuthRequired(boolean oAuthRequired) { this.oAuthRequired = oAuthRequired; return this; }
        public Builder connectTimeoutMs(Long connectTimeoutMs) { this.connectTimeoutMs = connectTimeoutMs; return this; }
        public Builder readTimeoutMs(Long readTimeoutMs) { this.readTimeoutMs = readTimeoutMs; return this; }
        public Builder maxInMemorySize(Integer maxInMemorySize) { this.maxInMemorySize = maxInMemorySize; return this; }
        public Builder headersToExtract(List<String> headersToExtract) { this.headersToExtract = headersToExtract; return this; }
        public Builder maxRequestSizeBytes(Integer maxRequestSizeBytes) { this.maxRequestSizeBytes = maxRequestSizeBytes; return this; }
        public Builder maxConcurrency(Integer maxConcurrency) { this.maxConcurrency = maxConcurrency; return this; }
        public Builder requestTimeoutMs(Integer requestTimeoutMs) { this.requestTimeoutMs = requestTimeoutMs; return this; }
        public Builder rateLimitPerSecond(Integer rateLimitPerSecond) { this.rateLimitPerSecond = rateLimitPerSecond; return this; }
        public Builder allowedHttpMethods(List<String> allowedHttpMethods) { this.allowedHttpMethods = allowedHttpMethods; return this; }
        public Builder retryAttempts(Integer retryAttempts) { this.retryAttempts = retryAttempts; return this; }
        public Builder retryBackoffMs(Long retryBackoffMs) { this.retryBackoffMs = retryBackoffMs; return this; }
        public Builder compressed(Boolean compressed) { this.compressed = compressed; return this; }
        public Builder oauthEnabled(Boolean oauthEnabled) { this.oauthEnabled = oauthEnabled; return this; }
        public Builder oauthTokenUrl(String oauthTokenUrl) { this.oauthTokenUrl = oauthTokenUrl; return this; }
        public Builder oauthClientId(String oauthClientId) { this.oauthClientId = oauthClientId; return this; }
        public Builder oauthClientSecret(String oauthClientSecret) { this.oauthClientSecret = oauthClientSecret; return this; }
        public Builder oauthScope(String oauthScope) { this.oauthScope = oauthScope; return this; }
        public Builder oauthAudience(String oauthAudience) { this.oauthAudience = oauthAudience; return this; }
        public Builder oauthAdditionalParams(Map<String, String> oauthAdditionalParams) { this.oauthAdditionalParams = oauthAdditionalParams; return this; }

        public HttpsClientConfig build() {
            return new HttpsClientConfig(this);
        }
    }
}
