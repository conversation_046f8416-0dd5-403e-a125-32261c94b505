package com.dell.it.hip.core.registry;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;

@Component
public class DedupMetricRegistry {

    private final StringRedisTemplate redisTemplate;

    @Autowired
    public DedupMetricRegistry(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    private String getDupKey(HIPIntegrationDefinition def, FlowStepConfigRef ref) {
        return String.format("dedup:dup:%s:%s:%s:%s",
                def.getServiceManagerName(),
                def.getHipIntegrationName(),
                def.getVersion(),
                ref.getPropertyRef());
    }
    private String getInsertKey(HIPIntegrationDefinition def, FlowStepConfigRef ref) {
        return String.format("dedup:ins:%s:%s:%s:%s",
                def.getServiceManagerName(),
                def.getHipIntegrationName(),
                def.getVersion(),
                ref.getPropertyRef());
    }

    public void incrementDuplicate(HIPIntegrationDefinition def, FlowStepConfigRef ref) {
        redisTemplate.opsForValue().increment(getDupKey(def, ref));
    }
    public void incrementInserted(HIPIntegrationDefinition def, FlowStepConfigRef ref) {
        redisTemplate.opsForValue().increment(getInsertKey(def, ref));
    }
    public long getDuplicateCount(HIPIntegrationDefinition def, FlowStepConfigRef ref) {
        String key = getDupKey(def, ref);
        String val = redisTemplate.opsForValue().get(key);
        return val == null ? 0 : Long.parseLong(val);
    }
    public long getInsertedCount(HIPIntegrationDefinition def, FlowStepConfigRef ref) {
        String key = getInsertKey(def, ref);
        String val = redisTemplate.opsForValue().get(key);
        return val == null ? 0 : Long.parseLong(val);
    }
}