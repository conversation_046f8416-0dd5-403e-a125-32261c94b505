package com.dell.it.hip.config.FlowSteps;

import java.util.List;

import com.dell.it.hip.config.rules.RuleEnabledStepConfig;
import com.dell.it.hip.config.rules.RuleRef;
public class FlowTargetsRoutingConfig implements RuleEnabledStepConfig {
    private List<RuleRef> ruleRefs;
    // ...other config properties...
    private boolean isDbBacked;

    public void setRules(List<RuleRef> ruleRefs) {
        this.ruleRefs = ruleRefs;
    }

    @Override
    public List<RuleRef> getRuleRefs() {
        return ruleRefs;
    }

    public boolean isDbBacked() {
        return isDbBacked;
    }

    public void setDbBacked(boolean dbBacked) {
        this.isDbBacked = dbBacked;
    }
}