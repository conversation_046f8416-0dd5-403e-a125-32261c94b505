package com.dell.it.hip.util.ediUtils;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

import org.redisson.api.RAtomicLong;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import io.xlate.edi.schema.Schema;
import io.xlate.edi.schema.SchemaFactory;
import io.xlate.edi.stream.EDIInputFactory;
import io.xlate.edi.stream.EDIOutputFactory;
import io.xlate.edi.stream.EDIStreamEvent;
import io.xlate.edi.stream.EDIStreamException;
import io.xlate.edi.stream.EDIStreamReader;
import io.xlate.edi.stream.EDIStreamWriter;

public class EdiDocumentGenerator {
	private final RedissonClient redisson;
	private final String baseKey;

	public EdiDocumentGenerator(RedissonClient redisson, String baseKey) throws Exception {
		this.redisson = redisson;
		this.baseKey = baseKey;
	}

	public String generate855Document(String senderId, String receiverId, 
			String isaQualifier, String gsFunctionalCode,
			String poNumber) throws Exception {

		ControlNumbers controls = generateControlNumbers(senderId, receiverId);
		EDIOutputFactory factory = EDIOutputFactory.newFactory();
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		EDIStreamWriter ediWriter = factory.createEDIStreamWriter(stream);

		// Write ISA Segment
		ediWriter.writeStartSegment("ISA")
		.writeElement("00") // Auth Qualifier
		.writeElement("")   // Auth Info
		.writeElement("00") // Security Qualifier
		.writeElement("")   // Security Info
		.writeElement(senderId)
		.writeElement(isaQualifier)
		.writeElement(receiverId)
		.writeElement(isaQualifier)
		.writeElement(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMdd")))
		.writeElement(LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmm")))
		.writeElement("U")  // Standards ID
		.writeElement("00401") // Version
		.writeElement(controls.isaControlNumber)
		.writeElement("0")  // Ack Request
		.writeElement("P")  // Usage Indicator
		.writeElement(">")  // Component Separator
		.writeEndSegment();

		// Write GS Segment
		ediWriter.writeStartSegment("GS")
		.writeElement(gsFunctionalCode)
		.writeElement(senderId)
		.writeElement(receiverId)
		.writeElement(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")))
		.writeElement(LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmm")))
		.writeElement(controls.gsControlNumber)
		.writeElement("X")  // Responsible Agency
		.writeElement("004010") // Version
		.writeEndSegment();

		// Write ST/855 Transaction
		write855Transaction(ediWriter, controls.stControlNumber, poNumber);

		// Write GE Segment
		ediWriter.writeStartSegment("GE")
		.writeElement("1")  // Number of Transactions
		.writeElement(controls.gsControlNumber)
		.writeEndSegment();

		// Write IEA Segment
		ediWriter.writeStartSegment("IEA")
		.writeElement("1")  // Number of Groups
		.writeElement(controls.isaControlNumber)
		.writeEndSegment();

		ediWriter.endInterchange();
		ediWriter.close();
		String envelope = new String(stream.toByteArray(), StandardCharsets.UTF_8);
		//Message<?> ediMessage = writeEDIwithEnvelope(message, envelope);
		stream.close();
		return envelope;
	}

	private void write855Transaction(EDIStreamWriter writer, String stControlNumber, String poNumber) throws EDIStreamException {

		 writer.writeStartSegment("ST")
		.writeElement("855") // Transaction Set ID
		.writeElement(stControlNumber)
		.writeEndSegment();

		// BEG Segment
		writer.writeStartSegment("BEG")
		.writeElement("00") // Transaction Set Purpose Code
		.writeElement("00") // Acknowledgment Type
		.writeElement(poNumber) // Purchase Order Number
		.writeEndSegment();

		// DTM Segment
		writer.writeStartSegment("DTM")
		.writeElement("011") // Date Qualifier
		.writeElement(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")))
		.writeEndSegment();

		// AK1 Segment
		writer.writeStartSegment("AK1")
		.writeElement("PO") // Functional Identifier Code
		.writeElement("1")  // Group Control Number
		.writeEndSegment();

		// SE Segment
		writer.writeStartSegment("SE")
		.writeElement("5") // Segment Count (update as needed)
		.writeElement(stControlNumber)
		.writeEndSegment();
		
		writer.endInterchange();
		writer.close();
	}

	private ControlNumbers generateControlNumbers(String senderId, String receiverId) throws InterruptedException {

		String isaControlNumber = generateNumber("isa", 9);
		String gsControlNumber = generateNumber("gs:" + isaControlNumber, 9);
		String stControlNumber = generateNumber("st:" + isaControlNumber + ":" + gsControlNumber, 9);

		return new ControlNumbers(
				isaControlNumber,
				gsControlNumber,
				stControlNumber,
				senderId,
				receiverId,
				LocalDateTime.now()
				);
	}

	private String generateNumber(String keySuffix, int length) throws InterruptedException {
		 String lockKey = baseKey + ":lock:" + keySuffix;
		    RLock lock = redisson.getLock(lockKey);
		    
		    try {
		        // 2. Acquire distributed lock (timeout after 5 seconds)
		        if (!lock.tryLock(5, 30, TimeUnit.SECONDS)) {
		            throw new RuntimeException("Failed to acquire lock for: " + keySuffix);
		        }
		        
		        // 3. Get or create the atomic counter
		        String counterKey = baseKey + ":" + keySuffix;
		        RAtomicLong counter = redisson.getAtomicLong(counterKey);
		        
		        // 4. Atomically increment and return the value
		        return String.format("%0" + length + "d", counter.getAndIncrement());
		    } finally {
		        lock.unlock();
		    }
	}

	private static class ControlNumbers {
		final String isaControlNumber;
		final String gsControlNumber;
		final String stControlNumber;
		final String senderId;
		final String receiverId;
		final LocalDateTime timestamp;

		ControlNumbers(String isa, String gs, String st, 
				String sender, String receiver, LocalDateTime ts) {
			this.isaControlNumber = isa;
			this.gsControlNumber = gs;
			this.stControlNumber = st;
			this.senderId = sender;
			this.receiverId = receiver;
			this.timestamp = ts;
		}
	}
	
	public boolean hasValidIsaEnvelope(String ediDocument) {
	    try {
	    	 EDIInputFactory factory = EDIInputFactory.newFactory();
	         EDIStreamReader reader = factory.createEDIStreamReader(new ByteArrayInputStream(ediDocument.getBytes(StandardCharsets.UTF_8)));
	        // Document must start with an interchange
	        if (reader.next() != EDIStreamEvent.START_INTERCHANGE) {
	            return false;
	        }
	        
	        // Next event must be START_SEGMENT for ISA
	        if (reader.next() != EDIStreamEvent.START_SEGMENT || 
	            !"ISA".equals(reader.getText())) {
	            return false;
	        }
	        
	        // ISA must have exactly 16 elements (X12 standard)
	        int elementCount = 0;
	        while (reader.next() != EDIStreamEvent.END_SEGMENT) {
	            if (reader.next() == EDIStreamEvent.ELEMENT_DATA) {
	                elementCount++;
	            }
	        }
	        
	        return elementCount == 16;
	    } catch (Exception e) {
	        return false;
	    }
	}
}