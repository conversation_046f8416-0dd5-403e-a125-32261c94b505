package com.dell.it.hip.util.validation;

import java.util.ArrayList;
import java.util.List;

public class MessageFormatDetector {
    public enum Format { JSON, XML, CSV, EDI_X12, EDI_EDIFACT, FLAT_FILE,UNKNOWN }

    public static String detect(String payload) {
        if (payload == null) return Format.UNKNOWN.name();
        String t = payload.trim();
        if (t.startsWith("{") || t.startsWith("["))        return Format.JSON.name();
        if (t.startsWith("<"))                             return Format.XML.name();
        if (t.startsWith("ISA") || t.startsWith("ST"))     return Format.EDI_X12.name();
        if (t.startsWith("UNA") || t.startsWith("UNB"))    return Format.EDI_EDIFACT.name();
        if (t.contains(",") || t.contains(";"))             return Format.CSV.name();
        if (fixedWidthFlatFile(t)) return Format.FLAT_FILE.name();
        return Format.UNKNOWN.name();
    }
    
    private static boolean fixedWidthFlatFile(String payload) {
        // Split on ^ delimiter
        String[] segments = payload.split("\\^");
        List<String> cleanSegments = new ArrayList<>();

        // Remove empty or blank segments
        for (String seg : segments) {
            if (!seg.trim().isEmpty()) cleanSegments.add(seg.trim());
        }

        if (cleanSegments.size() < 3) return false;

        // Group every 3 segments into a logical "record"
        List<String> records = new ArrayList<>();
        StringBuilder recordBuilder = new StringBuilder();

        for (int i = 0; i < cleanSegments.size(); i++) {
            recordBuilder.append(cleanSegments.get(i));
            if ((i + 1) % 3 == 0 || i == cleanSegments.size() - 1) {
                records.add(recordBuilder.toString());
                recordBuilder.setLength(0);
            }
        }

        // Calculate average record length
        int totalLength = 0;
        for (String record : records) {
            totalLength += record.length();
        }
        double avgLength = totalLength / (double) records.size();

        // Count how many records are within +/- 30 characters of average
        int matchCount = 0;
        for (String record : records) {
            if (Math.abs(record.length() - avgLength) <= 30) {
                matchCount++;
            }
        }

        boolean hasNoDelimiters = !payload.contains(",") && !payload.contains(";") && !payload.contains("\t");

        System.out.println("Records matched: " + matchCount + "/" + records.size());

        return matchCount >= records.size() * 0.7 && hasNoDelimiters;
    }
}