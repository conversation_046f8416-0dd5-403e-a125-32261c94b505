# TestContainers Configuration
# This file configures TestContainers behavior for integration tests

# Docker environment detection
testcontainers.reuse.enable=true
testcontainers.docker.client.strategy=org.testcontainers.dockerclient.EnvironmentAndSystemPropertyClientProviderStrategy

# Logging configuration
testcontainers.logging.level=INFO

# Container startup timeout (in seconds)
testcontainers.startup.timeout=120

# Ryuk configuration (container cleanup)
testcontainers.ryuk.disabled=false
testcontainers.ryuk.container.timeout=30s

# Image pull policy
testcontainers.image.pull.policy=DefaultPullPolicy

# Docker socket configuration for Windows
# Uncomment if using Docker Desktop on Windows
# testcontainers.docker.socket.override=npipe:////./pipe/docker_engine

# Hub image name prefix (for corporate environments)
# testcontainers.hub.image.name.prefix=

# Disable checks for specific environments
# testcontainers.checks.disable=true
