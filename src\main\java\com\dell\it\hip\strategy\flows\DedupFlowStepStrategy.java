package com.dell.it.hip.strategy.flows;
import java.time.Duration;
import java.util.Collections;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.DedupFlowStepConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.core.registry.DedupMetricRegistry;
import com.dell.it.hip.util.logging.WiretapService;
import com.dell.it.hip.util.redis.DedupKeyUtil;
@Component("dedup")
public class DedupFlowStepStrategy implements FlowStepStrategy {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private WiretapService wiretapService;
    @Autowired(required = false)
    private DedupMetricRegistry metricsRegistry; // Optional, for Prometheus etc.

    @Override
    public String getType() {
        return "dedup";
    }

    @Override
    public List<Message<?>> executeStep(Message<?> message, FlowStepConfigRef stepConfigRef, HIPIntegrationDefinition def) {
        DedupFlowStepConfig config = (DedupFlowStepConfig) def.getConfigMap().get(stepConfigRef.getPropertyRef());

        String dedupKey = DedupKeyUtil.buildDedupKey(def, config, message);

        Boolean isDuplicate = redisTemplate.hasKey(dedupKey);
        String eventLevel = config.getWiretapEventLevel() != null ? config.getWiretapEventLevel() : "warn";

        if (Boolean.TRUE.equals(isDuplicate)) {
            // Wiretap duplicate event
            wiretapService.tap(message, def, stepConfigRef, eventLevel, "Deduplication: Duplicate detected, key=" + dedupKey);

            // Metrics (optional)
            if (config.isEnableMetrics() && metricsRegistry != null)
                metricsRegistry.incrementDuplicate(def, stepConfigRef);

            // Policy-based handling
            switch (config.getOnDuplicate().toUpperCase()) {
                case "DROP":
                    return Collections.emptyList();
                case "FLAG":
                    Message<?> flagged = MessageBuilder.fromMessage(message)
                            .setHeader("dedupFlag", true).build();
                    return List.of(flagged);
                case "CONTINUE":
                    return List.of(message);
                case "DEADLETTER":
                    // If you have a dead letter mechanism, send to wiretap as error and drop
                    wiretapService.tap(message, def, stepConfigRef, "error", "Dedup DEADLETTER: Duplicate message sent to DLQ, key=" + dedupKey);
                    return Collections.emptyList();
                default:
                    return Collections.emptyList();
            }
        } else {
            // Insert new key with expiry
            redisTemplate.opsForValue().set(dedupKey, "1", Duration.ofSeconds(config.getDedupExpirySeconds()));
            if (config.isLogOnInsert())
                wiretapService.tap(message, def, stepConfigRef, "info", "Dedup key created: " + dedupKey);

            if (config.isEnableMetrics() && metricsRegistry != null)
                metricsRegistry.incrementInserted(def, stepConfigRef);

            return List.of(message);
        }
    }

    // Enterprise admin API to clear a dedup key (use in orchestrationService)
    public void purgeDedupKey(HIPIntegrationDefinition def, DedupFlowStepConfig config, Message<?> message) {
        String dedupKey = DedupKeyUtil.buildDedupKey(def, config, message);
        redisTemplate.delete(dedupKey);
    }
}