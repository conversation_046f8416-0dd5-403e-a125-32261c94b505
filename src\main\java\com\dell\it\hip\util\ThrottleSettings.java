package com.dell.it.hip.util;

import java.io.Serializable;

/**
 * Per-integration or per-adapter throttling settings for cluster-wide message/operation limiting.
 */
public class ThrottleSettings implements Serializable {
    private int maxMessagesPerPeriod;
    private int periodSeconds;
    private boolean enabled = true;

    public ThrottleSettings() {}

    public ThrottleSettings(int maxMessagesPerPeriod, int periodSeconds, boolean enabled) {
        this.maxMessagesPerPeriod = maxMessagesPerPeriod;
        this.periodSeconds = periodSeconds;
        this.enabled = enabled;
    }

    public int getMaxMessagesPerPeriod() { return maxMessagesPerPeriod; }
    public void setMaxMessagesPerPeriod(int maxMessagesPerPeriod) { this.maxMessagesPerPeriod = maxMessagesPerPeriod; }

    public int getPeriodSeconds() { return periodSeconds; }
    public void setPeriodSeconds(int periodSeconds) { this.periodSeconds = periodSeconds; }

    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
}