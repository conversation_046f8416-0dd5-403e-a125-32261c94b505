package com.dell.it.hip.strategy.handlers;

import org.springframework.messaging.Message;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;

public interface HandlerStrategy {

    String getType();

    /**
     * Handle the output message for the given integration and handler config.
     * Fallback logic is NOT handled here; only the designated handler instance is invoked.
     */
    void handle(Message<?> message, HIPIntegrationDefinition def, HandlerConfigRef ref) throws Exception;

    /**
     * Pause (temporarily disable) a specific handler (by propertyRef/configRef).
     */
    void pause(HIPIntegrationDefinition def, HandlerConfigRef handlerRef);

    /**
     * Resume (re-enable) a specific handler (by propertyRef/configRef).
     */
    void resume(HIPIntegrationDefinition def, HandlerConfigRef handlerRef);

    /**
     * Check if a specific handler is paused.
     */
    default boolean isPaused(HIPIntegrationDefinition def, HandlerConfigRef handlerRef) { return false; }

    /**
     * Release all resources, shutdown all managed handler instances.
     */
    default void dispose() {}

    /**
     * Optional: Clean shutdown for a particular handler instance.
     */
    default void shutdown(HIPIntegrationDefinition def, HandlerConfigRef ref) {}
}