package com.dell.it.hip.strategy.edi;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;

import org.springframework.stereotype.Service;

import io.xlate.edi.stream.EDIOutputFactory;
import io.xlate.edi.stream.EDIStreamConstants;
import io.xlate.edi.stream.EDIStreamWriter;
import lombok.Data;

@Service
@Data
public class EDIWriter {

	public String ediStreamWriter(AcknowledgeObect ackobj) throws Exception {

		EDIOutputFactory factory = EDIOutputFactory.newFactory();
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		// (2) Optionally specify delimiters - here the given values are the same as
		// default
		factory.setProperty(EDIStreamConstants.Delimiters.SEGMENT, '~');
		factory.setProperty(EDIStreamConstants.Delimiters.DATA_ELEMENT, '+');

		// Write each segment on a new line
		factory.setProperty(EDIOutputFactory.PRETTY_PRINT, true);
		EDIStreamWriter writer = factory.createEDIStreamWriter(stream);
		writer.startInterchange(); 
   
		writer.writeStartSegment("ISA").writeElement(String.format("%-2s",ackobj.getISA().getISA01()))
				.writeElement(String.format("%-10s",ackobj.getISA().getISA02())).writeElement(String.format("%-2s",ackobj.getISA().getISA03()))
				.writeElement(String.format("%-10s",ackobj.getISA().getISA04())).writeElement(String.format("%-2s",ackobj.getISA().getISA05()))
				.writeElement(String.format("%-15s",ackobj.getISA().getISA06())).writeElement(String.format("%-2s",ackobj.getISA().getISA07()))
				.writeElement(String.format("%-15s",ackobj.getISA().getISA08()))
				.writeElement(String.format("%-6s",ackobj.getISA().getISA09()))
				.writeElement(String.format("%-4s",ackobj.getISA().getISA10()))
				.writeElement(String.format("%-1s",ackobj.getISA().getISA11()))
				.writeElement(String.format("%-1s",ackobj.getISA().getISA12()))
				.writeElement(String.format("%-9s",ackobj.getISA().getISA13()))
				.writeElement(String.format("%-1s",Integer.toString(ackobj.getISA().getISA14())))
				.writeElement(String.format("%-1s",ackobj.getISA().getISA15()))
				.writeElement(":")
				.writeEndSegment();

		for (FunctionalAck funcAck : ackobj.getFunctionalAck()) {
			writer.writeStartSegment("GS").writeElement(String.format("%-2s",funcAck.getGS().getGsFunctionalIdCode()))
					.writeElement(String.format("%-15s",funcAck.getGS().getGsSendersCode())).writeElement(String.format("%-15s",funcAck.getGS().getGsReceiversCode()))
					.writeElement(String.format("%-8s",funcAck.getGS().getGsDate())).writeElement(String.format("%-4s",funcAck.getGS().getGsTime()))
					.writeElement(String.format("%-9s",funcAck.getGS().getGsControlNumber()))
					.writeElement(String.format("%-1s",funcAck.getGS().getGsResponsibleAgencyCode()))
					.writeElement(String.format("%-5s",funcAck.getGS().getGsVersion())).writeEndSegment();
			writer.writeStartSegment("ST").writeElement(funcAck.getST().getTsIdentifierCode())
					.writeElement(funcAck.getST().getTsControlNumber()).writeEndSegment();
			writer.writeStartSegment("AK1").writeElement(funcAck.getAK1().getFunctionCode())
					.writeElement(funcAck.getAK1().getGroupControlNumber()).writeEndSegment();
			for (TransactionSetResponse transactionSetResponse : funcAck.getTransactionSetResponse()) {
				writer.writeStartSegment("AK2")
						.writeElement(transactionSetResponse.getAK2().getTransactionSetIdentifier())
						.writeElement(transactionSetResponse.getAK2().getTransactionControlNumber()).writeEndSegment();
				writer.writeStartSegment("AK5").writeElement(transactionSetResponse.getAK5().getTsAckCode())
						.writeEndSegment();
			}
			writer.writeStartSegment("AK9").writeElement(funcAck.getAK9().getAcknowledgmentCode())
					.writeElement(Integer.toString(funcAck.getAK9().getNumberOfTransactionSets()))
					.writeElement(Integer.toString(funcAck.getAK9().getNumberOfAcceptedSets()))
					.writeElement(Integer.toString(funcAck.getAK9().getNumberOfRejectedSets())).writeEndSegment();
			writer.writeStartSegment("SE").writeElement(Integer.toString(funcAck.getSE().getNumberOfIncludeSegements()))
					.writeElement(funcAck.getSE().getTsControlNumber()).writeEndSegment();
			writer.writeStartSegment("GE").writeElement(Integer.toString(funcAck.getGE().getNumOfTransactionSets()))
					.writeElement(funcAck.getGE().getGeControlNumber()).writeEndSegment();

		}
		writer.writeStartSegment("IEA").writeElement(Integer.toString(ackobj.getIEA().getIEA01()))
				.writeElement(ackobj.getIEA().getIEA02()).writeEndSegment();

		writer.endInterchange();
		writer.close();
		String envelope = new String(stream.toByteArray(), StandardCharsets.UTF_8);
		// Message<?> ediMessage = writeEDIwithEnvelope("", envelope);
		stream.close();
		return envelope;
	}

}
