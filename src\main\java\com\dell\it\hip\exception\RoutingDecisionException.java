package com.dell.it.hip.exception;

/**
 * Exception thrown when routing decision fails.
 */
public class RoutingDecisionException extends RuntimeException {
    
    private final String ruleKey;
    private final String integrationName;
    private final String version;
    
    public RoutingDecisionException(String message, String ruleKey, String integrationName, String version) {
        super(message);
        this.ruleKey = ruleKey;
        this.integrationName = integrationName;
        this.version = version;
    }
    
    public RoutingDecisionException(String message, String ruleKey, String integrationName, 
                                  String version, Throwable cause) {
        super(message, cause);
        this.ruleKey = ruleKey;
        this.integrationName = integrationName;
        this.version = version;
    }
    
    public RoutingDecisionException(String ruleKey, String integrationName, String version, Throwable cause) {
        super(String.format("Failed to make routing decision for rule '%s' in integration %s:%s: %s", 
                          ruleKey, integrationName, version, cause.getMessage()), cause);
        this.ruleKey = ruleKey;
        this.integrationName = integrationName;
        this.version = version;
    }
    
    public String getRuleKey() {
        return ruleKey;
    }
    
    public String getIntegrationName() {
        return integrationName;
    }
    
    public String getVersion() {
        return version;
    }
}
