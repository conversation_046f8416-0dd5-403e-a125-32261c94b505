# NAS Client Development and Configuration Analysis

## Overview

This document provides a comprehensive analysis of the NAS configuration properties in the HIP services framework and documents the development of the NasClient utility class for testing NAS adapter and handler functionality with both NFS and SMB protocols.

## 1. Configuration Property Analysis

### DynamicNASAdapterConfig Analysis

#### Used Properties (with JSON bindings added):
- ✅ `protocol` - `@JsonProperty("nas.consumer.protocol")` - Used in `pollNasForFiles()`
- ✅ `mountPath` - `@JsonProperty("nas.consumer.mount.path")` - Used in `pollNasForFiles()`
- ✅ `remoteDirectory` - `@JsonProperty("nas.consumer.remote.directory")` - Used in `pollNasForFiles()` for NFS
- ✅ `fileNamePattern` - `@JsonProperty("nas.consumer.file.filter")` - Used in `pollNasForFiles()`
- ✅ `fileSortOrder` - `@JsonProperty("nas.consumer.file.sort.order")` - Used in file sorting logic
- ✅ `maxFilesPerPoll` - `@JsonProperty("nas.consumer.max.files.per.poll")` - Used in `pollRemoteSmbFilesWithJCIFS()`
- ✅ `pollingIntervalMs` - `@JsonProperty("nas.consumer.polling.interval.ms")` - Used in `pollLoop()`
- ✅ `fileAgeMs` - `@JsonProperty("nas.consumer.file.age.ms")` - Used in `pollRemoteSmbFilesWithJCIFS()`
- ✅ `ignoreHiddenFiles` - `@JsonProperty("nas.consumer.ignore.hidden.files")` - Used in `pollRemoteSmbFilesWithJCIFS()`
- ✅ `charset` - `@JsonProperty("nas.consumer.charset")` - Used in `buildMessage()`
- ✅ `postProcessAction` - `@JsonProperty("nas.consumer.post.process.action")` - Used in `handlePostProcess()`
- ✅ `renamePattern` - `@JsonProperty("nas.consumer.rename.pattern")` - Used in `handlePostProcess()`
- ✅ `parameters` - `@JsonProperty("nas.consumer.parameters")` - Used in `pollNasForFiles()`

#### SMB-specific Used Properties (with JSON bindings added):
- ✅ `host` - `@JsonProperty("nas.consumer.host")` - Used in SMB operations
- ✅ `shareName` - `@JsonProperty("nas.consumer.share.name")` - Used in SMB operations
- ✅ `domain` - `@JsonProperty("nas.consumer.domain")` - Used in SMB operations
- ✅ `username` - `@JsonProperty("nas.consumer.username")` - Used in SMB operations
- ✅ `password` - `@JsonProperty("nas.consumer.password")` - Used in SMB operations

#### Removed Unused Properties:
- ❌ `compressed` - Not used anywhere
- ❌ `moveDirectory` - Not used anywhere

### DynamicNasHandlerConfig Analysis

#### Used Properties (with JSON bindings added):
- ✅ `protocol` - `@JsonProperty("nas.producer.protocol")` - Used in `doHandle()`
- ✅ `remoteDirectory` - `@JsonProperty("nas.producer.mount.path")` - Used in both SMB and NFS handlers
- ✅ `shareName` - `@JsonProperty("nas.producer.share.name")` - Used in `handleSmbWrite()`
- ✅ `gzipEnabled` - `@JsonProperty("nas.producer.gzip.enabled")` - Used in `doHandle()`
- ✅ `fileSeparator` - `@JsonProperty("nas.producer.file.separator")` - Used in `handleNfsWrite()`
- ✅ `host` - `@JsonProperty("nas.producer.host")` - Used in `handleSmbWrite()`
- ✅ `username` - `@JsonProperty("nas.producer.username")` - Used in `handleSmbWrite()`
- ✅ `password` - `@JsonProperty("nas.producer.password")` - Used in `handleSmbWrite()`
- ✅ `domain` - `@JsonProperty("nas.producer.domain")` - Used in `handleSmbWrite()`

#### Removed Unused Properties:
- ❌ `id` - Not used anywhere (should use inherited id from parent)
- ❌ `timeout` - Not used anywhere
- ❌ `archiveEnabled` - Not used anywhere
- ❌ `parameters` - Not used anywhere
- ❌ `useKerberos` - Not used anywhere

## 2. Configuration Changes Made

### DynamicNASAdapterConfig Updates:
1. Added missing `@JsonProperty` annotations for all used properties
2. Removed unused properties (`compressed`, `moveDirectory`)
3. Added Lombok `@Getter` and `@Setter` annotations
4. Extended `AdapterConfig` for consistency with framework patterns
5. Organized properties by category (protocol, SMB-specific, polling, content handling, post-processing)

### DynamicNasHandlerConfig Updates:
1. Added missing `@JsonProperty` annotations for all used properties
2. Removed unused properties and manual getters/setters
3. Added Lombok `@Getter` and `@Setter` annotations
4. Cleaned up imports and removed unused code

## 3. NasClient Utility Development

### NasClientConfig Class
Created a comprehensive configuration class that mirrors the patterns used in other client configs:

**Key Features:**
- Supports both consumer (adapter testing) and producer (handler testing) configurations
- Builder pattern for easy configuration
- Pre-configured factory methods for both NFS and SMB protocols
- All properties from the cleaned-up adapter/handler configs

**Test Configuration Properties:**
```java
// NFS Consumer Configuration
NasClientConfig.createNfsConsumerConfig()
    .protocol("nfs")
    .remoteDirectory("/opt/test")
    .fileSeparator("/")

// NFS Producer Configuration  
NasClientConfig.createNfsProducerConfig()
    .protocol("nfs")
    .remoteDirectory("/opt/test")
    .fileSeparator("/")

// SMB Consumer Configuration
NasClientConfig.createSmbConsumerConfig()
    .protocol("smb")
    .host("localhost")
    .shareName("test")
    .remoteDirectory("/opt/test")

// SMB Producer Configuration
NasClientConfig.createSmbProducerConfig()
    .protocol("smb")
    .host("localhost")
    .shareName("test")
    .remoteDirectory("/opt/test")
```

### NasClient Class
Created a comprehensive NAS client utility that mirrors the implementation patterns from existing adapter/handler classes:

**Key Features:**
- **Message Sending**: Uses the same approach as `DynamicNasOutputHandler` for both NFS and SMB
- **Message Receiving**: Uses the same approach as `DynamicNasInputAdapter` for both NFS and SMB
- **Protocol Support**: Full support for both NFS (local filesystem) and SMB (JCIFS-NG) protocols
- **Connection Management**: Mirrors the SSH client and session management patterns
- **File Processing**: Supports compression, decompression, and post-processing actions
- **Continuous Monitoring**: Provides listener functionality for continuous file monitoring

**Core Methods:**
```java
// Send messages (handler testing)
nasClient.sendMessage("test-file.txt", "Hello World");
nasClient.sendMessage("test-file.bin", byteArray);

// Receive messages (adapter testing)
NasMessage message = nasClient.receiveMessage();
NasMessage message = nasClient.receiveMessage("*.xml");

// Continuous monitoring
nasClient.startListener(message -> {
    System.out.println("Received: " + message.getFileName());
});

// Wait for specific number of files
List<NasMessage> messages = nasClient.waitForMessages(5, 30000, 
    message -> System.out.println("Got: " + message.getFileName()));
```

**Protocol-Specific Implementation:**
- **NFS Operations**: Direct filesystem operations using Java NIO
- **SMB Operations**: JCIFS-NG library for remote SMB access with NTLM authentication
- **Local Mount Support**: SMB can use local mounted shares for better performance

## 4. Comprehensive Test Suite (NasClientTest)

### Test Class Structure
Created `NasClientTest.java` following the same patterns as existing client tests:
- **Location**: `src/test/java/com/dell/it/hip/client/NasClientTest.java`
- **Framework**: JUnit 5 with standard assertions
- **Setup/Teardown**: Proper resource management with temporary directories
- **Protocol Coverage**: Comprehensive testing of both NFS and SMB protocols

### Test Coverage (20+ Test Methods)

#### **NFS Protocol Tests:**
1. ✅ **`testNfsSendMessage()`** - NFS file upload (handler testing)
2. ✅ **`testNfsSendByteArrayMessage()`** - NFS binary file upload
3. ✅ **`testNfsReceiveMessage()`** - NFS file download (adapter testing)
4. ✅ **`testNfsFileFiltering()`** - NFS file pattern matching
5. ✅ **`testNfsPostProcessing()`** - NFS delete/rename operations
6. ✅ **`testNfsCompressionHandling()`** - NFS compression/decompression
7. ✅ **`testNfsConnectionManagement()`** - NFS connection lifecycle
8. ✅ **`testNfsMessageListener()`** - NFS continuous monitoring
9. ✅ **`testNfsWaitForMessages()`** - NFS wait for specific files
10. ✅ **`testNfsFileExistenceCheck()`** - NFS file existence checking

#### **SMB Protocol Tests:**
11. ✅ **`testSmbSendMessageWithLocalMount()`** - SMB file upload with local mount
12. ✅ **`testSmbReceiveMessageWithLocalMount()`** - SMB file download with local mount
13. ✅ **`testSmbMessageListener()`** - SMB continuous monitoring
14. ✅ **`testSmbFileExistenceCheck()`** - SMB file existence checking

#### **Advanced Feature Tests:**
15. ✅ **`testCustomNfsConfiguration()`** - Custom NFS configurations
16. ✅ **`testErrorHandling()`** - Invalid configuration and error scenarios
17. ✅ **`testLargeFileHandling()`** - Large files (10KB+)
18. ✅ **`testSpecialCharacterHandling()`** - Unicode, special chars, symbols
19. ✅ **`testConcurrentNfsOperations()`** - Concurrent file operations
20. ✅ **`testUnsupportedProtocol()`** - Unsupported protocol handling

#### **Manual Testing:**
21. ✅ **`main()` method** - Interactive testing against actual NAS servers

### Key Test Features:

```java
// NFS File Upload Testing (Handler functionality)
@Test
void testNfsSendMessage() throws Exception {
    String fileName = TEST_FILE_PREFIX + "nfs-send-" + System.currentTimeMillis() + ".txt";
    String testMessage = TEST_CONTENT + System.currentTimeMillis();
    
    assertDoesNotThrow(() -> {
        nfsClient.sendMessage(fileName, testMessage);
    });
    
    // Verify file was created
    Path expectedFile = testNfsDirectory.resolve(fileName);
    assertTrue(Files.exists(expectedFile), "File should have been created");
}

// SMB File Download Testing (Adapter functionality)
@Test
void testSmbReceiveMessageWithLocalMount() throws Exception {
    // Create file then receive pattern
    Files.writeString(testSmbDirectory.resolve(fileName), testMessage);
    
    NasClient.NasMessage receivedMessage = smbClient.receiveMessage();
    
    assertNotNull(receivedMessage, "Should have received a message");
    assertEquals(fileName, receivedMessage.getFileName(), "Filename should match");
}

// Protocol-Specific Listener Testing
@Test
void testNfsMessageListener() throws Exception {
    AtomicInteger messageCount = new AtomicInteger(0);
    CountDownLatch latch = new CountDownLatch(2);
    
    nfsClient.startListener(message -> {
        messageCount.incrementAndGet();
        latch.countDown();
    });
    
    // Create files and verify processing
    boolean received = latch.await(15, TimeUnit.SECONDS);
    assertTrue(received, "Should have processed files within timeout");
}
```

## 5. Usage Instructions

### Basic Usage Example:
```java
// Create configurations
NasClientConfig nfsConsumerConfig = NasClientConfig.createNfsConsumerConfig();
NasClientConfig nfsProducerConfig = NasClientConfig.createNfsProducerConfig();

// Create client
try (NasClient nasClient = new NasClient(nfsConsumerConfig, nfsProducerConfig)) {
    
    // Test handler functionality (sending)
    nasClient.sendMessage("test-message.txt", "Hello from HIP NAS Client!");
    
    // Test adapter functionality (receiving)
    NasClient.NasMessage receivedMessage = nasClient.receiveMessage();
    if (receivedMessage != null) {
        System.out.println("Received file: " + receivedMessage.getFileName());
        System.out.println("Content: " + receivedMessage.getContent());
    }
    
    // Continuous monitoring
    nasClient.startListener(message -> {
        System.out.println("File received: " + message.getFileName());
        System.out.println("Size: " + message.getFileSize() + " bytes");
    });
    
    Thread.sleep(60000); // Monitor for 1 minute
    nasClient.stopListener();
}
```

### SMB Configuration Example:
```java
NasClientConfig smbConfig = new NasClientConfig.Builder()
    .protocol("smb")
    .host("nas-server.company.com")
    .shareName("shared")
    .remoteDirectory("/data/files")
    .username("domain\\user")
    .password("password")
    .domain("COMPANY")
    .fileNamePattern("*.json")
    .pollingIntervalMs(30000L)
    .postProcessAction("rename")
    .renamePattern("{file}.processed")
    .build();
```

## 6. Technical Implementation Details

### Protocol Support:
- **NFS**: Direct filesystem operations using Java NIO for mounted NFS shares
- **SMB**: JCIFS-NG library for remote SMB access with NTLM authentication
- **Hybrid**: SMB can use local mounted shares for better performance

### Connection Management:
- **NFS**: No connection management needed (direct filesystem access)
- **SMB**: JCIFS-NG context and authentication management
- **Resource Cleanup**: Proper resource cleanup in all scenarios

### File Operations:
- Mirrors exact file reading/writing patterns from existing adapters/handlers
- Supports compression/decompression using `CompressionUtil`
- Implements file existence checking before upload
- Supports post-processing actions (delete/rename)

### Error Handling:
- Comprehensive exception handling and logging
- Protocol-specific error handling
- Graceful degradation for connection issues

## 7. Independence and Self-Containment

- **No HIP Application Dependency**: Runs independently without full application startup
- **Isolated Testing**: Each test method is independent and uses temporary directories
- **Resource Management**: Proper setup/teardown ensures clean test environment
- **Configurable**: Uses test-specific configurations separate from production

## 8. Future Enhancements

1. **Remote SMB Testing**: Add tests against actual remote SMB servers
2. **Kerberos Authentication**: Add support for Kerberos authentication in SMB
3. **Advanced NFS Features**: Add support for NFS-specific mount options
4. **Performance Testing**: Add performance benchmarks to test suite
5. **Mock Testing**: Add unit tests with mocked file systems for faster execution
6. **Batch Operations**: Support for batch file operations
7. **Advanced Filtering**: More sophisticated file filtering options
