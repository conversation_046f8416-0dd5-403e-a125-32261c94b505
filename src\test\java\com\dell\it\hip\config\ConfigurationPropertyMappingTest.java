package com.dell.it.hip.config;

import com.dell.it.hip.config.Handlers.DynamicKafkaHandlerConfig;
import com.dell.it.hip.config.Handlers.DynamicIbmmqHandlerConfig;
import com.dell.it.hip.config.Handlers.DynamicRabbitMQHandlerConfig;
import com.dell.it.hip.config.adapters.DynamicKafkaAdapterConfig;
import com.dell.it.hip.config.adapters.DynamicIBMMQAdapterConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit tests to verify JSON property deserialization works correctly
 * for all configuration classes after the property naming refactoring.
 */
public class ConfigurationPropertyMappingTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testDynamicKafkaHandlerConfigPropertyMapping() throws Exception {
        // Test JSON with the new dot-separated lowercase naming convention
        String json = "{\n"
                + "  \"kafka.producer.bootstrap.servers\": \"localhost:9092\",\n"
                + "  \"kafka.producer.topic\": \"test-topic\",\n"
                + "  \"kafka.producer.client.id\": \"test-client\",\n"
                + "  \"kafka.producer.username\": \"testuser\",\n"
                + "  \"kafka.producer.password\": \"testpass\",\n"
                + "  \"kafka.producer.security.protocol\": \"SASL_SSL\",\n"
                + "  \"kafka.producer.sasl.mechanism\": \"PLAIN\",\n"
                + "  \"kafka.producer.sasl.jaas.config\": \"org.apache.kafka.common.security.plain.PlainLoginModule required username='testuser' password='testpass';\",\n"
                + "  \"kafka.producer.ssl.truststore.location\": \"/path/to/truststore.jks\",\n"
                + "  \"kafka.producer.ssl.truststore.password\": \"trustpass\",\n"
                + "  \"kafka.producer.ssl.keystore.location\": \"/path/to/keystore.jks\",\n"
                + "  \"kafka.producer.ssl.keystore.password\": \"keypass\",\n"
                + "  \"kafka.producer.ssl.key.password\": \"keypass\",\n"
                + "  \"kafka.producer.ssl.truststore.type\": \"JKS\",\n"
                + "  \"kafka.producer.ssl.protocols\": \"TLSv1.2\",\n"
                + "  \"kafka.producer.acks\": 1,\n"
                + "  \"kafka.producer.batch.size\": 16384,\n"
                + "  \"kafka.producer.linger.ms\": 5,\n"
                + "  \"kafka.producer.buffer.memory\": 33554432,\n"
                + "  \"kafka.producer.retries\": 3,\n"
                + "  \"kafka.producer.max.in.flight.requests.per.connection\": 5,\n"
                + "  \"kafka.producer.delivery.timeout.ms\": 120000,\n"
                + "  \"kafka.producer.request.timeout.ms\": 30000,\n"
                + "  \"kafka.producer.enable.idempotence\": true,\n"
                + "  \"kafka.producer.compression.type\": 1,\n"
                + "  \"kafka.producer.gzip.enabled\": true\n"
                + "}";

        // Test deserialization
        DynamicKafkaHandlerConfig config = objectMapper.readValue(json, DynamicKafkaHandlerConfig.class);

        // Verify the deserialization worked correctly
        assertNotNull(config);
        assertEquals("localhost:9092", config.getBootstrapServers());
        assertEquals("test-topic", config.getTopic());
        assertEquals("test-client", config.getClientId());
        assertEquals("testuser", config.getUsername());
        assertEquals("testpass", config.getPassword());
        assertEquals("SASL_SSL", config.getSecurityProtocol());
        assertEquals("PLAIN", config.getSaslMechanism());
        assertTrue(config.getSasljaasconfig().contains("testuser"));
        assertEquals("/path/to/truststore.jks", config.getSslTruststoreLocation());
        assertEquals("trustpass", config.getSslTruststorePassword());
        assertEquals("/path/to/keystore.jks", config.getSslKeystoreLocation());
        assertEquals("keypass", config.getSslKeystorePassword());
        assertEquals("keypass", config.getSslKeyPassword());
        assertEquals("JKS", config.getSsltruststoretype());
        assertEquals("TLSv1.2", config.getProtocols());
        assertEquals(Integer.valueOf(1), config.getAcks());
        assertEquals(Integer.valueOf(16384), config.getBatchSize());
        assertEquals(Integer.valueOf(5), config.getLingerMs());
        assertEquals(Integer.valueOf(33554432), config.getBufferMemory());
        assertEquals(Integer.valueOf(3), config.getRetries());
        assertEquals(Integer.valueOf(5), config.getMaxInFlightRequestsPerConnection());
        assertEquals(Integer.valueOf(120000), config.getDeliveryTimeoutMs());
        assertEquals(Integer.valueOf(30000), config.getRequestTimeoutMs());
        assertTrue(config.getEnableIdempotence());
        assertEquals(Integer.valueOf(1), config.getCompressionType());
        assertTrue(config.getGzipEnabled());

        System.out.println("Successfully deserialized DynamicKafkaHandlerConfig with new property names!");
    }

    @Test
    public void testDynamicIbmmqHandlerConfigPropertyMapping() throws Exception {
        // Test JSON with the new dot-separated lowercase naming convention
        String json = "{\n"
                + "  \"ibmmq.producer.queue.manager\": \"QM1\",\n"
                + "  \"ibmmq.producer.host\": \"localhost\",\n"
                + "  \"ibmmq.producer.port\": 1414,\n"
                + "  \"ibmmq.producer.channel\": \"DEV.APP.SVRCONN\",\n"
                + "  \"ibmmq.producer.queue\": \"DEV.QUEUE.1\",\n"
                + "  \"ibmmq.producer.username\": \"mquser\",\n"
                + "  \"ibmmq.producer.password\": \"mqpass\",\n"
                + "  \"ibmmq.producer.ccsid\": 1208,\n"
                + "  \"ibmmq.producer.encoding\": 273,\n"
                + "  \"ibmmq.producer.persistent\": true,\n"
                + "  \"ibmmq.producer.gzip.enabled\": false,\n"
                + "  \"ibmmq.producer.auth.type\": \"USER_PASSWORD\",\n"
                + "  \"ibmmq.producer.conn.name\": \"localhost(1414)\",\n"
                + "  \"ibmmq.producer.ssl.cipher.suite\": \"TLS_RSA_WITH_AES_256_CBC_SHA256\"\n"
                + "}";

        // Test deserialization
        DynamicIbmmqHandlerConfig config = objectMapper.readValue(json, DynamicIbmmqHandlerConfig.class);

        // Verify the deserialization worked correctly
        assertNotNull(config);
        assertEquals("QM1", config.getQueueManager());
        assertEquals("localhost", config.getHost());
        assertEquals(Integer.valueOf(1414), config.getPort());
        assertEquals("DEV.APP.SVRCONN", config.getChannel());
        assertEquals("DEV.QUEUE.1", config.getQueue());
        assertEquals("mquser", config.getUsername());
        assertEquals("mqpass", config.getPassword());
        assertEquals(Integer.valueOf(1208), config.getCcsid());
        assertEquals(Integer.valueOf(273), config.getEncoding());
        assertTrue(config.getPersistent());
        assertFalse(config.getGzipEnabled());
        assertEquals("USER_PASSWORD", config.getAuthenticationType());
        assertEquals("localhost(1414)", config.getConnName());
        assertEquals("TLS_RSA_WITH_AES_256_CBC_SHA256", config.getCipherSuite());

        System.out.println("Successfully deserialized DynamicIbmmqHandlerConfig with new property names!");
    }

    @Test
    public void testDynamicRabbitMQHandlerConfigPropertyMapping() throws Exception {
        // Test JSON with the new dot-separated lowercase naming convention
        String json = "{\n"
                + "  \"rabbitmq.producer.host\": \"localhost\",\n"
                + "  \"rabbitmq.producer.port\": 5672,\n"
                + "  \"rabbitmq.producer.username\": \"guest\",\n"
                + "  \"rabbitmq.producer.password\": \"guest\",\n"
                + "  \"rabbitmq.producer.vhost\": \"/\",\n"
                + "  \"rabbitmq.producer.exchange\": \"test.exchange\",\n"
                + "  \"rabbitmq.producer.routing.key\": \"test.routing.key\",\n"
                + "  \"rabbitmq.producer.mandatory\": true,\n"
                + "  \"rabbitmq.producer.persistent\": true,\n"
                + "  \"rabbitmq.producer.gzip.enabled\": false,\n"
                + "  \"rabbitmq.producer.ssl.enabled\": false\n"
                + "}";

        // Test deserialization
        DynamicRabbitMQHandlerConfig config = objectMapper.readValue(json, DynamicRabbitMQHandlerConfig.class);

        // Verify the deserialization worked correctly
        assertNotNull(config);
        assertEquals("localhost", config.getHost());
        assertEquals(Integer.valueOf(5672), config.getPort());
        assertEquals("guest", config.getUsername());
        assertEquals("guest", config.getPassword());
        assertEquals("/", config.getVirtualHost());
        assertEquals("test.exchange", config.getExchange());
        assertEquals("test.routing.key", config.getRoutingKey());
        assertTrue(config.getMandatory());
        assertTrue(config.getPersistent());
        assertFalse(config.getGzipEnabled());
        assertFalse(config.getSslEnabled());

        System.out.println("Successfully deserialized DynamicRabbitMQHandlerConfig with new property names!");
    }

    @Test
    public void testDynamicKafkaAdapterConfigPropertyMapping() throws Exception {
        // Test JSON with the correct dot-separated lowercase naming convention
        String json = "{\n"
                + "  \"kafka.consumer.bootstrap.servers\": \"localhost:9092\",\n"
                + "  \"kafka.consumer.topic.name\": \"test-topic\",\n"
                + "  \"kafka.consumer.client.id\": \"test-client\",\n"
                + "  \"kafka.consumer.group.id\": \"test-group\",\n"
                + "  \"kafka.consumer.concurrency\": 3,\n"
                + "  \"kafka.consumer.max.poll.records\": 500,\n"
                + "  \"kafka.consumer.auth.type\": \"SASL\",\n"
                + "  \"kafka.consumer.security.protocol\": \"SASL_SSL\",\n"
                + "  \"kafka.consumer.sasl.mechanism\": \"PLAIN\",\n"
                + "  \"kafka.consumer.username\": \"testuser\",\n"
                + "  \"kafka.consumer.password\": \"testpass\",\n"
                + "  \"kafka.consumer.compressed\": true\n"
                + "}";

        // Test deserialization
        DynamicKafkaAdapterConfig config = objectMapper.readValue(json, DynamicKafkaAdapterConfig.class);

        // Verify the deserialization worked correctly
        assertNotNull(config);
        assertEquals("localhost:9092", config.getBootstrapServers());
        assertEquals("test-topic", config.getTopic());
        assertEquals("test-client", config.getClientId());
        assertEquals("test-group", config.getGroupId());
        assertEquals(Integer.valueOf(3), config.getConcurrency());
        assertEquals(Integer.valueOf(500), config.getMaxPollRecords());
        assertEquals("SASL", config.getAuthenticationType());
        assertEquals("SASL_SSL", config.getSecurityProtocol());
        assertEquals("PLAIN", config.getSaslMechanism());
        assertEquals("testuser", config.getUsername());
        assertEquals("testpass", config.getPassword());
        assertTrue(config.isCompressed());

        System.out.println("Successfully deserialized DynamicKafkaAdapterConfig with correct property names!");
    }

    @Test
    public void testDynamicIBMMQAdapterConfigPropertyMapping() throws Exception {
        // Test JSON with the new dot-separated lowercase naming convention
        String json = "{\n"
                + "  \"ibmmq.consumer.queue.manager\": \"QM1\",\n"
                + "  \"ibmmq.consumer.queue\": \"DEV.QUEUE.1\",\n"
                + "  \"ibmmq.consumer.channel\": \"DEV.APP.SVRCONN\",\n"
                + "  \"ibmmq.consumer.conn.name\": \"localhost(1414)\",\n"
                + "  \"ibmmq.consumer.auth.type\": \"USER_PASSWORD\",\n"
                + "  \"ibmmq.consumer.username\": \"mquser\",\n"
                + "  \"ibmmq.consumer.password\": \"mqpass\",\n"
                + "  \"ibmmq.consumer.ssl.cipher.suite\": \"TLS_RSA_WITH_AES_256_CBC_SHA256\",\n"
                + "  \"ibmmq.consumer.ssl.peer.name\": \"CN=localhost\",\n"
                + "  \"ibmmq.consumer.ssl.keystore\": \"/path/to/keystore.jks\",\n"
                + "  \"ibmmq.consumer.ssl.keystore.password\": \"keypass\",\n"
                + "  \"ibmmq.consumer.ssl.truststore\": \"/path/to/truststore.jks\",\n"
                + "  \"ibmmq.consumer.ssl.truststore.password\": \"trustpass\"\n"
                + "}";

        // Test deserialization
        DynamicIBMMQAdapterConfig config = objectMapper.readValue(json, DynamicIBMMQAdapterConfig.class);

        // Verify the deserialization worked correctly
        assertNotNull(config);
        assertEquals("QM1", config.getQueueManager());
        assertEquals("DEV.QUEUE.1", config.getQueueName());
        assertEquals("DEV.APP.SVRCONN", config.getChannel());
        assertEquals("localhost(1414)", config.getConnName());
        assertEquals("USER_PASSWORD", config.getAuthenticationType());
        assertEquals("mquser", config.getUsername());
        assertEquals("mqpass", config.getPassword());
        assertEquals("TLS_RSA_WITH_AES_256_CBC_SHA256", config.getSslCipherSuite());
        assertEquals("CN=localhost", config.getSslPeerName());
        assertEquals("/path/to/keystore.jks", config.getSslKeystore());
        assertEquals("keypass", config.getSslKeystorePassword());
        assertEquals("/path/to/truststore.jks", config.getSslTruststore());
        assertEquals("trustpass", config.getSslTruststorePassword());

        System.out.println("Successfully deserialized DynamicIBMMQAdapterConfig with new property names!");
    }
}
