#configserver_uri=https://configserveruser:<EMAIL>
#configproperties_sheet_name=hip-services

# Disable Spring Cloud Config
#spring.cloud.config.enabled=false
#spring.cloud.config.import-check.enabled=false

# Redis configuration - external Redis server
#spring.data.redis.host=hip-gscm-redis-dev.rds-a2-np.kob.dell.com
#spring.data.redis.port=443
#spring.data.redis.password=sFjIvYHMJHcv
#spring.data.redis.ssl.enabled=true
spring.data.redis.url=rediss://:<EMAIL>:443
spring.data.redis.enabled=true
<<<<<<< HEAD
redis.cache.name==HIP-GSCM-DEV-REDISCACHE
=======
redis.cache.name=HIP-GSCM-DEV-REDISCACHE
>>>>>>> ec6c660e98e3a81ced2b44b6af3f7af48f4297f8

# Service configuration
service.manager.name=OrderIntegrationManager
service.concurrency.corePoolSize=16
service.concurrency.maxPoolSize=48
service.concurrency.queueCapacity=1000

# HIP configuration
hip.kafka.bootstrap-servers=kafka1:9092,kafka2:9092
hip.kafka.security-protocol=SASL_PLAINTEXT

management.endpoints.web.exposure.include=*
management.endpoint.prometheus.enabled=true
management.endpoint.health.show-details=always
management.endpoint.metrics.enabled=true

aic.oauth2.enabled=true

aic.security.oauth2.permitted.endpoints=/swagger-ui/**,/swagger,/v3/api-docs,/configuration/**,/swagger-resources/**,/swagger-ui.html,/webjars/**,/bus/v3/api-docs/**,/v3/api-docs/**,/actuator/**,/version.txt

aic.security.oauth2.restricted.endpoints=/api/auth/**,/hip/management/**

aic.security.oauth2.scopes=hipservice_api.hipservice_api


spring.security.oauth2.resourceserver.jwt.jwk-set-uri=https://www-sit-g1.dell.com/di/v3/fp/v3/keys/public

aic.security.oauth2.resourceId=HipService_API

