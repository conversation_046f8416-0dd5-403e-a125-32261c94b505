package com.dell.it.hip.util.contivoUtils;

public interface Constant {
	
	public String ERROR_CHANNEL = "errorChannel";
	public String LOGGER_CHANNEL = "loggerChannel";
	public String PRODUCER_CHANNEL = "producingChannel";
	public String RMQ_OUTBOUND = "rmqOutboundChannel";
	public String NULL_CHANNEL = "nullChannel";
	public String GATEWAY_CHANNEL = "integration.gateway.channel";
	public String GATEWAY_KAFKA = "integration.gateway.kafka";
	public String GATEWAY_MQ = "integration.gateway.mq";
	public String GATEWAY_RABBIT = "integration.gateway.rmq";
	public String GATEWAY_SFTP = "integration.gateway.sftp";
	public String GATEWAY_NAS = "integration.gateway.nas";
	public String FLOW_NAME = "flowName";
	public String RECEVER_NAME="receiverName";
	public String SENDER_NAME="senderName";
	public String ERROR_MSG = "errorMsg";
	public String DATA_TYPE = "dataType";
	public String FILE_NAME="file_name";
	public String API_KEY = "API_KEY";
	public String AS2_TO="AS2-TO";
    public String AS2_FROM="AS2-FROM";
	
	public String SUCCESS_MESSAGE = "Message Posted Successfully!!";
	public String ERROR_MESSAGE = "Error message posting with ------->";
	public String EDI_TYPE = "ediType";
	public String EDI_FACT="EDI-EDIFACT";
	public String EDI_X12="EDI-X12";
    public String RENAME="Rename";
    public String DELETE="Delete";
	
	public String KAFKA_PRODUCER = "kafkaProducer";
	public String KAFKA_CONSUMER = "kafkaConsumer";
	public String RABBIT_PRODUCER = "rabbitmqProducer";
	public String RABBIT_CONSUMER = "rabbitmqConsumer";
	public String IBM_PRODUCER = "ibmmqProducer";
	public String IBM_CONSUMER = "ibmmqConsumer";
	public String JDBC_PRODUCER = "jdbcProducer";
	public String HTTP_PRODUCER = "httpsProducer";
	public String SFTP_PRODUCER = "sftpProducer";
	public String NAS_PRODUCER = "nasProducer";
	public String SFTP_CONSUMER = "sftpConsumer";
	public String NAS_CONSUMER = "nasConsumer";
	
	public String CONT_TYPE = "Content-type";
	public String APPLICATION_XML = "application/xml";
	public String APPLICATION_JSON = "application/json";
	public String APPLICATION_TEXT = "text/plain";
	
	public String packageName = "com.dell.it.eis";
	public String path_download = "C:\\Users\\<USER>\\Downloads\\contivo2";
	public String CROSS_MAP_PATH_KEY = "fc.fs.path";
	public String temp_path = "/sftpDirectory";
	public String document_key = "ST";
	public String CR_CONDITION="criteriaJoinCondition";
	public String GR_CONDITION ="criteriaOperator";
	public String MAPPINGFIELDVALUE="mappingFieldValue";
	public String MAPPINGFIELDNAME="mappingFieldName";
	public String SERVICETYPE ="serviceType";
	public String MAPPING_SERVICE="FlowMapping";
	public String FLOW_TYPE="flowType";
	public String Fixed_Text="FixedText";
	public String DATE_TIME="DateandTime";
	public String RANDOM_NUMBER="RandomNumber";
	public String FILE_NAME_HEADER="FileNameAttribute";
	public String RANDOM_NUMBER_4_DIGIT="RandomNumber4digit";
	public String RANDOM_NUMBER_6_DIGIT="RandomNumber6digit";
	public String ELIMENT_IN_HEADER="ElementInHeader";
	public String SORCE="Source";
	public String TARGET="Target";
	
	public int BUFFER_SIZE = 8192;
    public String CLASS_SUFFIX = ".class";
    public String JAVA_SUFFIX = ".java";
    public String host = null;
    public int port = -1;
    public String baseURI = "/";
    public String DOT = ".";
    public String EMPTY = "";
    public String EDI ="EDI_";
    public String LOGGING_STATUS = "LOGGING_STATUS";
    public String FLOW_TRANSACTION_ROOT_ELEMENT = "TRANSACTION_ROOT_ELEMENT";
    public String FLOW_XPATH_EXPRESSION = "ELEMENT_IN_PAYLOAD";
    public String DOCTYPE ="docType";
    
    public String USERNAME_PASS ="Username/password";
     
    
    public enum DataType {
		XML,JSON,EDI,CSV,FLAT
	}
    public enum FlowIdentifier {
		TRANSACTION_ROOT_ELEMENT, ELEMENT_IN_PAYLOAD, DOCUMENT, FILENAME,ELEMENT_IN_DOCUMENT,FILECONTENT_STARTS_WITH
	}
	
	public enum Status {
		STARTED,COMPLETED,TERMINATED,FAILED,ERROR
	}


}
