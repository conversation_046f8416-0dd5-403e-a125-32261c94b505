{"ruleName": "DynamicTargetSelection", "ruleVersion": "1.0", "ruleDescription": "Route to handler targets dynamically.", "documentType": "xml", "ruleScope": "local", "ruleType": "flowTargetsRouting", "status": "ENABLED", "executeAlways": false, "executeActionWhen": "ALL", "ruleConditions": [{"propertyName": "rootElement", "operator": "EQUALS", "value": "Invoice"}], "actions": [{"name": "flowTargetsResponse1", "type": "FlowTargetsResponse", "params": {"primaryHandler": "kafkaHandlera", "fallbackHandler": "httpsHandlera"}}, {"name": "flowTargetsResponse2", "type": "FlowTargetsResponse", "params": {"primaryHandler": "kafkaHandlerb", "fallbackHandler": "httpsHandlerb"}}, {"name": "flowStopStep", "type": "stop-step"}]}