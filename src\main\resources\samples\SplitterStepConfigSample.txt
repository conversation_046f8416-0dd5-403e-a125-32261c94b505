splitterStep:
  docTypeSplitConfigs:
    - name: INVOICE
      version: "1.0"
      dataFormat: EDI_X12
      class: EdiX12SplitterConfig
      splitX12: true
      x12SplitLevel: "transaction"
      x12SegmentDelimiter: "~"
      x12ElementDelimiter: "*"
      x12SubElementDelimiter: ":"
      allowMultipleInterchanges: false

    - name: INVOICE
      version: "1.0"
      dataFormat: XML
      class: XmlSplitterConfig
      splitXml: true
      xmlXPathExpression: "/Invoices/Invoice"

    - name: CREDIT_NOTE
      version: "2.1"
      dataFormat: JSON
      class: JsonSplitterConfig
      splitJsonArray: true
      jsonPathExpression: "$.creditNotes[*]"

    - name: REPORT
      version: "1.0"
      dataFormat: CSV
      class: CsvSplitterConfig
      splitCsvLines: true

    - name: BANK_STATEMENT
      version: "any"
      dataFormat: FLATFILE
      class: FlatFileSplitterConfig
      splitFlatFile: true
      flatFileExpression: "^BANK"

  defaultConfig:
    class: DefaultSplitterConfig
    action: regex_split
    regexExpression: "\\n{2,}"
