# Spring Boot Configuration for Cloud Profile
# This configuration is used when SPRING_PROFILES_ACTIVE=cloud

spring:
  profiles:
    active: cloud
  
  # Configuration Server Settings
  cloud:
    config:
      uri: ${configserver_uri:https://configserveruser:<EMAIL>}
      enabled: true
      fail-fast: true
      retry:
        initial-interval: 1000
        max-attempts: 6
        max-interval: 2000
        multiplier: 1.1

# HIP Configuration Properties
hip:
  config:
    properties:
      sheet:
        name: ${configproperties_sheet_name:shared-service-manger}
  
  # Integration Platform Settings
  integration:
    platform:
      name: "HIP Services Cloud"
      environment: "cloud"
      cluster:
        enabled: true
        discovery:
          enabled: true

# Logging Configuration for Cloud Environment
logging:
  level:
    com.dell.it.hip: INFO
    org.springframework.cloud.config: DEBUG
    org.springframework.web: INFO
    org.springframework.security: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{36}] - %msg%n"

# Management and Actuator Settings
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,configprops,env
  endpoint:
    health:
      show-details: when-authorized
 
      

# Server Configuration
server:
  port: 8080
  servlet:
    context-path: /hip-services

# Security Configuration for Cloud
security:
  oauth2:
    resource:
      jwt:
        key-uri: ${configserver_uri}/oauth/token_key
