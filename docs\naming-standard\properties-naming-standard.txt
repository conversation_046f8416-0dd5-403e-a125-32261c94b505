# HIP Services JSON Property Naming Standard
# ==========================================
#
# This document provides a comprehensive reference of all JSON property names used across
# the HIP Services framework's adapter and handler configuration classes.
#
# NAMING CONVENTION:
# - Adapter configs (consumers) use: {technology}.consumer.{property.name}
# - Handler configs (producers) use: {technology}.producer.{property.name}
# - All property names follow dot-separated lowercase convention
# - Technology names: kafka, ibmmq, rabbitmq, sftp, nas, https
#
# Last Updated: 2025-07-01
# ===========================================

## ADAPTER CONFIGURATIONS (CONSUMERS)
## ===================================

### KAFKA ADAPTER (DynamicKafkaAdapterConfig)
### ------------------------------------------
kafka.consumer.bootstrap.servers
kafka.consumer.topic.name
kafka.consumer.client.id
kafka.consumer.group.id
kafka.consumer.concurrency
kafka.consumer.max.poll.records
kafka.consumer.auth.type
kafka.consumer.security.protocol
kafka.consumer.sasl.mechanism
kafka.consumer.username
kafka.consumer.password
kafka.consumer.auto.offset.reset
kafka.consumer.fetch.min.bytes
kafka.consumer.fetch.max.bytes
kafka.consumer.max.partition.fetch.bytes
kafka.consumer.session.timeout.ms
kafka.consumer.heartbeat.interval.ms
kafka.consumer.poll.timeout.ms
kafka.consumer.enable.auto.commit
kafka.consumer.auto.commit.interval.ms
kafka.consumer.max.poll.interval.ms
kafka.consumer.request.timeout.ms
kafka.consumer.retries
kafka.consumer.retry.backoff.ms
kafka.consumer.isolation.level
kafka.consumer.allow.auto.create.topics
kafka.consumer.ssl.truststore.location
kafka.consumer.ssl.truststore.password
kafka.consumer.ssl.keystore.location
kafka.consumer.ssl.keystore.password
kafka.consumer.ssl.key.password
kafka.consumer.enrich.headers
kafka.consumer.value.deserializer
kafka.consumer.key.deserializer
kafka.consumer.headers.to.extract
kafka.consumer.properties
kafka.consumer.compressed

### IBM MQ ADAPTER (DynamicIBMMQAdapterConfig)
### ------------------------------------------
ibmmq.consumer.queue.manager
ibmmq.consumer.queue
ibmmq.consumer.channel
ibmmq.consumer.conn.name
ibmmq.consumer.auth.type
ibmmq.consumer.username
ibmmq.consumer.password
ibmmq.consumer.ssl.cipher.suite
ibmmq.consumer.ssl.peer.name
ibmmq.consumer.ssl.keystore
ibmmq.consumer.ssl.keystore.password
ibmmq.consumer.ssl.truststore
ibmmq.consumer.ssl.truststore.password

### RABBITMQ ADAPTER (DynamicRabbitMQAdapterConfig)
### -----------------------------------------------
rabbitmq.consumer.host
rabbitmq.consumer.port
rabbitmq.consumer.vhost
rabbitmq.consumer.queueName
rabbitmq.consumer.auth.type
rabbitmq.consumer.username
rabbitmq.consumer.password
rabbitmq.consumer.ssl.truststore.location
rabbitmq.consumer.ssl.truststore.password
rabbitmq.consumer.ssl.keystore.location
rabbitmq.consumer.ssl.keystore.password
rabbitmq.consumer.ssl.key.password
rabbitmq.consumer.concurrency
rabbitmq.consumer.prefetchCount
rabbitmq.consumer.acknowledgeMode
rabbitmq.consumer.channelCacheSize
rabbitmq.consumer.compressed
rabbitmq.consumer.headersToExtract
rabbitmq.consumer.properties
rabbitmq.consumer.messageConverterClass

### SFTP ADAPTER (DynamicSFTPAdapterConfig)
### ---------------------------------------
sftp.consumer.host
sftp.consumer.port
sftp.consumer.username
sftp.consumer.password
sftp.consumer.private.key
sftp.consumer.private.key.passphrase
sftp.consumer.remote.directory
sftp.consumer.file.filter
sftp.consumer.polling.interval.ms
sftp.consumer.compressed
sftp.consumer.charset
sftp.consumer.headers.to.extract
sftp.consumer.post.process.action
sftp.consumer.rename.pattern

### NAS ADAPTER (DynamicNASAdapterConfig)
### ------------------------------------
nas.consumer.protocol
nas.consumer.host
nas.consumer.share.name
nas.consumer.domain
nas.consumer.username
nas.consumer.password
nas.consumer.mount.path
nas.consumer.remote.directory
nas.consumer.file.filter
nas.consumer.file.sort.order
nas.consumer.max.files.per.poll
nas.consumer.polling.interval.ms
nas.consumer.file.age.ms
nas.consumer.ignore.hidden.files
nas.consumer.charset
nas.consumer.post.process.action
nas.consumer.rename.pattern
nas.consumer.parameters

### HTTPS ADAPTER (DynamicHttpsAdapterConfig)
### -----------------------------------------
https.consumer.api.key.header
https.consumer.api.key.value
https.consumer.oauth.required
https.consumer.headers.to.extract
https.consumer.max.request.size.bytes
https.consumer.max.concurrency
https.consumer.request.timeout.ms
https.consumer.rate.limit.per.second
https.consumer.allowed.http.methods


## HANDLER CONFIGURATIONS (PRODUCERS)
## ===================================

### KAFKA HANDLER (DynamicKafkaHandlerConfig)
### ------------------------------------------
kafka.producer.bootstrap.servers
kafka.producer.topic
kafka.producer.client.id
kafka.producer.username
kafka.producer.password
kafka.producer.security.protocol
kafka.producer.sasl.mechanism
kafka.producer.sasl.jaas.config
kafka.producer.ssl.truststore.location
kafka.producer.ssl.truststore.password
kafka.producer.ssl.keystore.location
kafka.producer.ssl.keystore.password
kafka.producer.ssl.key.password
kafka.producer.ssl.truststore.type
kafka.producer.ssl.protocols
kafka.producer.acks
kafka.producer.batch.size
kafka.producer.linger.ms
kafka.producer.buffer.memory
kafka.producer.retries
kafka.producer.max.in.flight.requests.per.connection
kafka.producer.delivery.timeout.ms
kafka.producer.request.timeout.ms
kafka.producer.enable.idempotence
kafka.producer.compression.type
kafka.producer.gzip.enabled
kafka.producer.parameters

### IBM MQ HANDLER (DynamicIbmmqHandlerConfig)
### -------------------------------------------
ibmmq.producer.queue.manager
ibmmq.producer.host
ibmmq.producer.port
ibmmq.producer.channel
ibmmq.producer.queue
ibmmq.producer.username
ibmmq.producer.password
ibmmq.producer.ccsid
ibmmq.producer.encoding
ibmmq.producer.persistent
ibmmq.producer.gzip.enabled
ibmmq.producer.auth.type
ibmmq.producer.conn.name
ibmmq.producer.ssl.cipher.suite

### RABBITMQ HANDLER (DynamicRabbitMQHandlerConfig)
### -----------------------------------------------
rabbitmq.producer.host
rabbitmq.producer.port
rabbitmq.producer.username
rabbitmq.producer.password
rabbitmq.producer.vhost
rabbitmq.producer.exchange
rabbitmq.producer.routing.key
rabbitmq.producer.mandatory
rabbitmq.producer.persistent
rabbitmq.producer.gzip.enabled
rabbitmq.producer.ssl.enabled
rabbitmq.producer.parameters

### SFTP HANDLER (DynamicSftpHandlerConfig)
### ---------------------------------------
sftp.producer.host
sftp.producer.port
sftp.producer.username
sftp.producer.password
sftp.producer.private.key.path
sftp.producer.remote.directory
sftp.producer.timeout
sftp.producer.gzip.enabled

### NAS HANDLER (DynamicNasHandlerConfig)
### ------------------------------------
nas.producer.protocol
nas.producer.host
nas.producer.mount.path
nas.producer.file.separator
nas.producer.gzip.enabled
nas.producer.share.name
nas.producer.username
nas.producer.password
nas.producer.domain

### HTTPS HANDLER (DynamicHttpsHandlerConfig)
### -----------------------------------------
https.producer.endpoint.url
https.producer.http.method
https.producer.headers
https.producer.api.key.header
https.producer.api.key.value
https.producer.connect.timeout.ms
https.producer.read.timeout.ms
https.producer.max.in.memory.size
https.producer.retry.attempts
https.producer.retry.backoff.ms
https.producer.compressed
https.producer.oauth.enabled
https.producer.oauth.token.url
https.producer.oauth.client.id
https.producer.oauth.client.secret
https.producer.oauth.scope
https.producer.oauth.audience


## PROPERTY NAMING PATTERNS SUMMARY
## =================================

### Technology Prefixes:
- kafka.consumer.*     / kafka.producer.*
- ibmmq.consumer.*     / ibmmq.producer.*
- rabbitmq.consumer.*  / rabbitmq.producer.*
- sftp.consumer.*      / sftp.producer.*
- nas.consumer.*       / nas.producer.*
- https.consumer.*     / https.producer.*

### Common Property Categories:
- Connection: host, port, username, password
- SSL/TLS: ssl.truststore.location, ssl.keystore.location, ssl.cipher.suite
- Authentication: auth.type, oauth.enabled, api.key.header
- Performance: timeout.ms, concurrency, max.*, retry.*
- Compression: gzip.enabled, compressed
- Headers: headers.to.extract, enrich.headers

### Property Naming Rules:
1. Use lowercase letters only
2. Separate words with dots (.)
3. Use descriptive, full words (avoid abbreviations)
4. Follow hierarchical structure: technology.role.category.property
5. Use consistent naming across similar properties in different technologies

## USAGE EXAMPLES
## ==============

### Kafka Producer Configuration:
```json
{
  "kafka.producer.bootstrap.servers": "localhost:9092",
  "kafka.producer.topic": "orders",
  "kafka.producer.security.protocol": "SASL_SSL",
  "kafka.producer.sasl.mechanism": "PLAIN",
  "kafka.producer.gzip.enabled": true
}
```

### IBM MQ Consumer Configuration:
```json
{
  "ibmmq.consumer.queue.manager": "QM1",
  "ibmmq.consumer.queue": "ORDER.QUEUE",
  "ibmmq.consumer.conn.name": "localhost(1414)",
  "ibmmq.consumer.ssl.cipher.suite": "TLS_RSA_WITH_AES_256_CBC_SHA256"
}
```

### HTTPS Producer Configuration:
```json
{
  "https.producer.endpoint.url": "https://api.example.com/orders",
  "https.producer.http.method": "POST",
  "https.producer.api.key.header": "X-API-Key",
  "https.producer.oauth.enabled": true,
  "https.producer.connect.timeout.ms": 5000
}
```

### SFTP Consumer Configuration:
```json
{
  "sftp.consumer.host": "sftp.example.com",
  "sftp.consumer.port": 22,
  "sftp.consumer.username": "user",
  "sftp.consumer.remote.directory": "/incoming",
  "sftp.consumer.file.filter": "*.csv",
  "sftp.consumer.polling.interval.ms": 60000
}
```

## VALIDATION AND TESTING
## ======================

All property names in this document are validated through:
- Unit tests in PropertyNamingValidationTest.java
- Configuration mapping tests in ConfigurationPropertyMappingTest.java
- JSON deserialization tests for each configuration class
- Integration tests with actual property sheet configurations

## MAINTENANCE NOTES
## =================

When adding new properties:
1. Follow the established naming convention
2. Add @JsonProperty annotation with the correct property name
3. Update this documentation file
4. Add validation tests for the new properties
5. Ensure consistency with similar properties in other technologies

Total Properties Documented:
- Kafka: 36 consumer + 27 producer = 63 properties
- IBM MQ: 12 consumer + 14 producer = 26 properties
- RabbitMQ: 18 consumer + 12 producer = 30 properties
- SFTP: 14 consumer + 8 producer = 22 properties
- NAS: 18 consumer + 9 producer = 27 properties
- HTTPS: 10 consumer + 16 producer = 26 properties

GRAND TOTAL: 194 JSON properties documented
