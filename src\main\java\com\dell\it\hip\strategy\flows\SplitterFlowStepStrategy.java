package com.dell.it.hip.strategy.flows;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.DocTypeConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.FlowSteps.SplitterFlowStepConfig;
import com.dell.it.hip.util.dataformatUtils.CsvUtil;
import com.dell.it.hip.util.dataformatUtils.FlatFileUtil;
import com.dell.it.hip.util.dataformatUtils.JsonUtil;
import com.dell.it.hip.util.dataformatUtils.StaediUtil;
import com.dell.it.hip.util.dataformatUtils.XmlUtil;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;

@Component("splitter")
public class SplitterFlowStepStrategy extends AbstractFlowStepStrategy {

    @Autowired(required = false) private StaediUtil staediUtil;
    @Autowired(required = false) private XmlUtil xmlUtil;
    @Autowired(required = false) private CsvUtil csvUtil;
    @Autowired(required = false) private JsonUtil jsonUtil;
    @Autowired(required = false) private FlatFileUtil flatUtil;
    @Autowired private WiretapService wiretapService;

    @Override
    public String getType() {
        return "splitter";
    }

    @Override
    protected List<Message<?>> doExecute(
            Message<?> message,
            FlowStepConfigRef stepConfigRef,
            HIPIntegrationDefinition def
    ) throws Exception {
        SplitterFlowStepConfig config = (SplitterFlowStepConfig) def.getConfigMap().get(stepConfigRef.getPropertyRef());
        if (config == null) {
            emitWiretapError(message, def, stepConfigRef, "No Splitter config for: " + stepConfigRef.getPropertyRef());
            return Collections.emptyList();
        }

        String payload = Objects.toString(message.getPayload(), "");
        String docTypeHeader = (String) message.getHeaders().get("HIP.documentType");
        String dataFormatHeader = (String) message.getHeaders().get("hip.source.dataFormat");
        String docTypever = (String) message.getHeaders().get("HIP.documentTypeVersion");

        // Default/fallback config logic
        DocTypeConfig splitterDocTypeConfig = null;
        String docTypeName = null;
        String docTypeVersion = null;

        if (docTypeHeader != null && docTypeHeader.contains(":")) {
            String[] parts = docTypeHeader.split(":", 2);
            docTypeName = parts[0];
            docTypeVersion = parts[1];
        }

        // 1. Find matching config for docType+version+dataFormat, fallback to default
       splitterDocTypeConfig = config.findBestConfig(docTypeHeader, docTypever, dataFormatHeader);

        if (splitterDocTypeConfig == null) {
            emitWiretapError(message, def, stepConfigRef, String.format(
                    "No matching Splitter config for docType=%s, version=%s, dataFormat=%s",
                    docTypeName, docTypeVersion, dataFormatHeader
            ));
            return Collections.emptyList();
        }

        List<String> parts = null;
        String dataFormat = splitterDocTypeConfig.getDataFormat() != null
                ? splitterDocTypeConfig.getDataFormat()
                : dataFormatHeader;
        //List<String> parts = null;

        // ==== EDI X12 ====
        if ("EDI_X12".equalsIgnoreCase(dataFormat)) {
            if (staediUtil == null) staediUtil = new StaediUtil();
            String segDelim = Optional.ofNullable(splitterDocTypeConfig.getX12SegmentDelimiter())
                    .orElseGet(() -> StaediUtil.detectX12SegmentDelimiter(payload));
            String elemDelim = Optional.ofNullable(splitterDocTypeConfig.getX12ElementDelimiter())
                    .orElseGet(() -> StaediUtil.detectX12ElementDelimiter(payload));
            String subElemDelim = Optional.ofNullable(splitterDocTypeConfig.getX12SubElementDelimiter()).orElse(":");
            parts = staediUtil.splitX12(payload,
            		splitterDocTypeConfig.getX12SplitLevel(),
                    segDelim, elemDelim, subElemDelim,
                    splitterDocTypeConfig.isAllowMultipleInterchanges());
        }
        // ==== EDI EDIFACT ====
        else if ("EDI_EDIFACT".equalsIgnoreCase(dataFormat)) {
            if (staediUtil == null) staediUtil = new StaediUtil();
            String segDelim = Optional.ofNullable(splitterDocTypeConfig.getEdifactSegmentDelimiter()).orElse("'");
            String elemDelim = Optional.ofNullable(splitterDocTypeConfig.getEdifactElementDelimiter()).orElse("+");
            String subElemDelim = Optional.ofNullable(splitterDocTypeConfig.getEdifactSubElementDelimiter()).orElse(":");
            parts = staediUtil.splitEdifactMessages(payload,
            		splitterDocTypeConfig.getEdifactSplitLevel(),
                    segDelim, elemDelim, subElemDelim,
                    splitterDocTypeConfig.isAllowMultipleEdifactInterchanges());
        }
        // ==== XML ====
        else if ("XML".equalsIgnoreCase(dataFormat)) {
            if (xmlUtil == null) xmlUtil = new XmlUtil();
            parts = XmlUtil.splitByXPath(payload, splitterDocTypeConfig.getXmlXPathExpression());
        }
        // ==== CSV ====
        else if ("CSV".equalsIgnoreCase(dataFormat)) {
            if (csvUtil == null) csvUtil = new CsvUtil();
            parts = CsvUtil.splitLines(payload);
        }
        // ==== JSON ====
        else if ("JSON".equalsIgnoreCase(dataFormat)) {
            if (jsonUtil == null) jsonUtil = new JsonUtil();
            parts = JsonUtil.splitByJsonPath(payload, splitterDocTypeConfig.getJsonPathExpression());
        }
        // ==== FLAT FILE ====
        else if ("FLAT_FILE".equalsIgnoreCase(dataFormat)) {
            if (flatUtil == null) flatUtil = new FlatFileUtil();
            parts = FlatFileUtil.splitByRegexStart(payload, splitterDocTypeConfig.getFlatFileExpression());
        }
        // ==== Default: generic splitting or action ====
        else {
            switch (splitterDocTypeConfig.getAction()) {
                case "ignore":
                    // No splitting, just pass through
                    parts = Collections.singletonList(payload);
                    break;
                case "terminate":
                    emitWiretapError(message, def, stepConfigRef, "Splitter config action=terminate: message terminated");
                    return Collections.emptyList();
                case "regex_split":
                    if (splitterDocTypeConfig.getRegexExpression() != null) {
                        parts = Arrays.asList(payload.split(splitterDocTypeConfig.getRegexExpression()));
                    } else {
                        emitWiretapError(message, def, stepConfigRef, "No regexExpression specified for regex_split action");
                        return Collections.emptyList();
                    }
                    break;
                default:
                    emitWiretapError(message, def, stepConfigRef, "Unknown splitter action: " + splitterDocTypeConfig.getAction());
                    return Collections.emptyList();
            }
        }
        // Fallback: no splitting
       /* else {
            parts = Collections.singletonList(payload);
        }*/

        // ==== Error Handling ====
        if (parts == null || parts.isEmpty()) {
            emitWiretapError(message, def, stepConfigRef, "Splitter produced no parts for docType=" + docTypeName);
            return Collections.emptyList();
        }

        // ==== Build and wiretap result messages ====
        List<Message<?>> result = new ArrayList<>();
        for (int i = 0; i < parts.size(); i++) {
            String part = parts.get(i);
            MessageBuilder<String> builder = MessageBuilder.withPayload(part)
                    .copyHeaders(message.getHeaders())
                    .setHeader("splitter_original_format", dataFormat)
                    .setHeader("splitter_step_ref", stepConfigRef.getPropertyRef())
                    .setHeader("splitter_part_index", i + 1)
                    .setHeader("splitter_part_count", parts.size());
            Message<?> partMsg = builder.build();
            result.add(partMsg);

            wiretapService.tap(
                    partMsg, def, stepConfigRef,
                    "info",
                    String.format(
                            "Splitter produced part %d/%d for docType=%s, version=%s, dataFormat=%s, payload='%s'",
                            i + 1, parts.size(), docTypeName, docTypeVersion, dataFormat,
                            (part.length() > 200 ? part.substring(0, 200) + "..." : part)
                    )
            );
            TransactionLoggingUtil.logInfo(
                    partMsg, def, stepConfigRef,
                    String.format("Splitter produced part %d/%d for docType=%s, version=%s, dataFormat=%s", i + 1, parts.size(), docTypeName, docTypeVersion, dataFormat)
            );
        }

        // Emit summary
        wiretapService.tap(
                message, def, stepConfigRef,
                "info", String.format("Splitter produced %d part(s) for docType=%s, version=%s, dataFormat=%s", parts.size(), docTypeName, docTypeVersion, dataFormat)
        );

        return result;
    }

    private void emitWiretapError(Message<?> message, HIPIntegrationDefinition def, FlowStepConfigRef stepConfigRef, String errorMsg) {
        wiretapService.tap(message, def, stepConfigRef, "error", errorMsg);
        TransactionLoggingUtil.logError(message, def, stepConfigRef, "SplitterError", errorMsg);
    }
}
