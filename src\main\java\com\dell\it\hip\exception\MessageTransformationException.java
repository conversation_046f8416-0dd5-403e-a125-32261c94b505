package com.dell.it.hip.exception;

/**
 * Exception thrown when message transformation fails.
 */
public class MessageTransformationException extends RuntimeException {
    
    private final String transformationType;
    private final String sourceFormat;
    private final String targetFormat;
    
    public MessageTransformationException(String message, String transformationType, 
                                        String sourceFormat, String targetFormat) {
        super(message);
        this.transformationType = transformationType;
        this.sourceFormat = sourceFormat;
        this.targetFormat = targetFormat;
    }
    
    public MessageTransformationException(String message, String transformationType, 
                                        String sourceFormat, String targetFormat, Throwable cause) {
        super(message, cause);
        this.transformationType = transformationType;
        this.sourceFormat = sourceFormat;
        this.targetFormat = targetFormat;
    }
    
    public MessageTransformationException(String transformationType, String sourceFormat, 
                                        String targetFormat, Throwable cause) {
        super(String.format("Failed to transform message from %s to %s using %s transformation: %s", 
                          sourceFormat, targetFormat, transformationType, cause.getMessage()), cause);
        this.transformationType = transformationType;
        this.sourceFormat = sourceFormat;
        this.targetFormat = targetFormat;
    }
    
    public String getTransformationType() {
        return transformationType;
    }
    
    public String getSourceFormat() {
        return sourceFormat;
    }
    
    public String getTargetFormat() {
        return targetFormat;
    }
}
