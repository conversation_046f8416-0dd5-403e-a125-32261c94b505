package com.dell.it.hip.client;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit test class for NasClient that validates all NAS operations
 * including file upload (handler testing), file download (adapter testing),
 * file processing, connection management, and error scenarios for both NFS and SMB protocols.
 * 
 * This test class follows the same patterns and structure as existing client tests
 * (SftpClientTest, MQClientTest, RabbitMQClientTest, KafkaClientTest) in the HIP services framework.
 */
@Disabled("Requires actual SMB server - enable for integration testing")
class NasClientTest {

    private static final Logger logger = LoggerFactory.getLogger(NasClientTest.class);
    
    private NasClientConfig nfsConsumerConfig;
    private NasClientConfig nfsProducerConfig;
    private NasClientConfig smbConsumerConfig;
    private NasClientConfig smbProducerConfig;
    private NasClient nfsClient;
    private NasClient smbClient;
    
    // Test data
    private static final String TEST_FILE_PREFIX = "nas-test-";
    private static final String TEST_CONTENT = "Test content from NasClient - ";
    private static final String TEST_BINARY_CONTENT = "Binary test content with special chars: àáâãäåæçèéêë";
    
    // Test directories
    private Path testNfsDirectory;
    private Path testSmbDirectory;
    
    @BeforeEach
    void setUp() throws IOException {
        // Create test directories
        testNfsDirectory = Files.createTempDirectory("nas-nfs-test");
        testSmbDirectory = Files.createTempDirectory("nas-smb-test");
        
        // Create NFS configurations
        nfsConsumerConfig = new NasClientConfig.Builder()
            .protocol("nfs")
            .remoteDirectory(testNfsDirectory.toString())
            .fileSeparator("/")
            .fileNamePattern(".*")
            .pollingIntervalMs(5000L)
            .charset("UTF-8")
            .postProcessAction("delete")
            .build();
        
        nfsProducerConfig = new NasClientConfig.Builder()
            .protocol("nfs")
            .remoteDirectory(testNfsDirectory.toString())
            .fileSeparator("/")
            .gzipEnabled(false)
            .build();
        
        // Create SMB configurations (using local filesystem for testing)
        smbConsumerConfig = new NasClientConfig.Builder()
            .protocol("smb")
            .host("localhost")
            .shareName("test")
            .remoteDirectory(testSmbDirectory.toString())
            .mountPath(testSmbDirectory.toString()) // Use local mount for testing
            .fileSeparator("/")
            .username("testuser")
            .password("testpass")
            .fileNamePattern(".*")
            .pollingIntervalMs(5000L)
            .charset("UTF-8")
            .postProcessAction("delete")
            .build();
        
        smbProducerConfig = new NasClientConfig.Builder()
            .protocol("smb")
            .host("localhost")
            .shareName("test")
            .remoteDirectory(testSmbDirectory.toString())
            .fileSeparator("/")
            .username("testuser")
            .password("testpass")
            .gzipEnabled(false)
            .build();
        
        // Create NasClient instances
        nfsClient = new NasClient(nfsConsumerConfig, nfsProducerConfig);
        smbClient = new NasClient(smbConsumerConfig, smbProducerConfig);
        
        logger.info("NasClient test setup completed");
    }
    
    @AfterEach
    void tearDown() throws IOException {
        if (nfsClient != null) {
            nfsClient.close();
        }
        if (smbClient != null) {
            smbClient.close();
        }
        
        // Clean up test directories
        if (testNfsDirectory != null) {
            deleteDirectory(testNfsDirectory);
        }
        if (testSmbDirectory != null) {
            deleteDirectory(testSmbDirectory);
        }
        
        logger.info("NasClient test cleanup completed");
    }
    
    @Test
    void testNfsSendMessage() throws Exception {
        // Test sending a simple text message via NFS (handler functionality)
        String fileName = TEST_FILE_PREFIX + "nfs-send-" + System.currentTimeMillis() + ".txt";
        String testMessage = TEST_CONTENT + System.currentTimeMillis();
        
        assertDoesNotThrow(() -> {
            nfsClient.sendMessage(fileName, testMessage);
        });
        
        // Verify file was created
        Path expectedFile = testNfsDirectory.resolve(fileName);
        assertTrue(Files.exists(expectedFile), "File should have been created");
        
        String actualContent = Files.readString(expectedFile);
        assertEquals(testMessage, actualContent, "File content should match");
        
        logger.info("Successfully sent NFS file: {} with content: {}", fileName, testMessage);
    }
    
    @Test
    void testNfsSendByteArrayMessage() throws Exception {
        // Test sending a byte array message via NFS (handler functionality)
        String fileName = TEST_FILE_PREFIX + "nfs-binary-" + System.currentTimeMillis() + ".bin";
        byte[] testPayload = TEST_BINARY_CONTENT.getBytes("UTF-8");
        
        assertDoesNotThrow(() -> {
            nfsClient.sendMessage(fileName, testPayload);
        });
        
        // Verify file was created
        Path expectedFile = testNfsDirectory.resolve(fileName);
        assertTrue(Files.exists(expectedFile), "File should have been created");
        
        byte[] actualContent = Files.readAllBytes(expectedFile);
        assertArrayEquals(testPayload, actualContent, "File content should match");
        
        logger.info("Successfully sent NFS binary file: {} with {} bytes", fileName, testPayload.length);
    }
    
    @Test
    void testNfsReceiveMessage() throws Exception {
        // First create a file directly (simulating external file creation)
        String fileName = TEST_FILE_PREFIX + "nfs-receive-" + System.currentTimeMillis() + ".txt";
        String testMessage = TEST_CONTENT + "for receive test";
        Path testFile = testNfsDirectory.resolve(fileName);
        Files.writeString(testFile, testMessage);
        
        // Wait a bit for the file to be available
        Thread.sleep(1000);
        
        // Then try to receive it
        NasClient.NasMessage receivedMessage = nfsClient.receiveMessage();
        
        assertNotNull(receivedMessage, "Should have received a message");
        assertEquals(fileName, receivedMessage.getFileName(), "Filename should match");
        assertEquals(testMessage, receivedMessage.getContent(), "Content should match");
        assertTrue(receivedMessage.getFileSize() > 0, "File size should be greater than 0");
        
        logger.info("Successfully received NFS file: {} with content: {}", 
                   receivedMessage.getFileName(), receivedMessage.getContent());
    }
    
    @Test
    void testSmbSendMessageWithLocalMount() throws Exception {
        // Test SMB with local mount (handler functionality)
        String fileName = TEST_FILE_PREFIX + "smb-send-" + System.currentTimeMillis() + ".txt";
        String testMessage = TEST_CONTENT + System.currentTimeMillis();
        
        assertDoesNotThrow(() -> {
            smbClient.sendMessage(fileName, testMessage);
        });
        
        // Since we're using local filesystem for SMB testing, verify file was created
        Path expectedFile = testSmbDirectory.resolve(fileName);
        assertTrue(Files.exists(expectedFile), "File should have been created");
        
        String actualContent = Files.readString(expectedFile);
        assertEquals(testMessage, actualContent, "File content should match");
        
        logger.info("Successfully sent SMB file: {} with content: {}", fileName, testMessage);
    }
    
    @Test
    void testSmbReceiveMessageWithLocalMount() throws Exception {
        // Test SMB receive with local mount (adapter functionality)
        String fileName = TEST_FILE_PREFIX + "smb-receive-" + System.currentTimeMillis() + ".txt";
        String testMessage = TEST_CONTENT + "for SMB receive test";
        Path testFile = testSmbDirectory.resolve(fileName);
        Files.writeString(testFile, testMessage);
        
        // Wait a bit for the file to be available
        Thread.sleep(1000);
        
        // Then try to receive it
        NasClient.NasMessage receivedMessage = smbClient.receiveMessage();
        
        assertNotNull(receivedMessage, "Should have received a message");
        assertEquals(fileName, receivedMessage.getFileName(), "Filename should match");
        assertEquals(testMessage, receivedMessage.getContent(), "Content should match");
        assertTrue(receivedMessage.getFileSize() > 0, "File size should be greater than 0");
        
        logger.info("Successfully received SMB file: {} with content: {}", 
                   receivedMessage.getFileName(), receivedMessage.getContent());
    }
    
    @Test
    void testNfsFileFiltering() throws Exception {
        // Test file pattern filtering functionality for NFS
        String timestamp = String.valueOf(System.currentTimeMillis());
        
        // Create files with different extensions
        Files.writeString(testNfsDirectory.resolve(TEST_FILE_PREFIX + "filter-" + timestamp + ".txt"), "Text file content");
        Files.writeString(testNfsDirectory.resolve(TEST_FILE_PREFIX + "filter-" + timestamp + ".xml"), "<xml>XML content</xml>");
        Files.writeString(testNfsDirectory.resolve(TEST_FILE_PREFIX + "filter-" + timestamp + ".json"), "{\"json\": \"content\"}");
        
        Thread.sleep(1000);
        
        // Test filtering for XML files only
        NasClient.NasMessage xmlMessage = nfsClient.receiveMessage(".*\\.xml");
        
        assertNotNull(xmlMessage, "Should have received an XML file");
        assertTrue(xmlMessage.getFileName().endsWith(".xml"), "Filtered message should be an XML file");
        assertTrue(xmlMessage.getContent().contains("XML content"), "Content should match XML file");
        
        logger.info("Successfully filtered XML file: {}", xmlMessage.getFileName());
    }
    
    @Test
    void testNfsPostProcessing() throws Exception {
        // Test post-processing functionality (delete/rename) for NFS
        
        // Create a custom consumer config with rename post-processing
        NasClientConfig renameConfig = new NasClientConfig.Builder()
            .protocol("nfs")
            .remoteDirectory(testNfsDirectory.toString())
            .fileSeparator("/")
            .postProcessAction("rename")
            .renamePattern("{file}.processed")
            .build();
        
        try (NasClient renameClient = new NasClient(renameConfig, nfsProducerConfig)) {
            String fileName = TEST_FILE_PREFIX + "rename-" + System.currentTimeMillis() + ".txt";
            String testMessage = TEST_CONTENT + "for rename test";
            
            // Create file directly
            Path testFile = testNfsDirectory.resolve(fileName);
            Files.writeString(testFile, testMessage);
            Thread.sleep(500);
            
            // Receive file (should rename it)
            NasClient.NasMessage receivedMessage = renameClient.receiveMessage();
            
            assertNotNull(receivedMessage, "Should have received a message");
            assertEquals(fileName, receivedMessage.getFileName(), "Original filename should be returned");
            
            // Verify original file is gone and renamed file exists
            assertFalse(Files.exists(testFile), "Original file should be gone");
            assertTrue(Files.exists(testNfsDirectory.resolve(fileName + ".processed")), 
                      "Renamed file should exist");
            
            logger.info("Successfully processed and renamed file: {} -> {}.processed", fileName, fileName);
        }
    }

    @Test
    void testNfsCompressionHandling() throws Exception {
        // Test compression/decompression functionality for NFS

        // Create configs with compression enabled
        NasClientConfig compressedProducerConfig = new NasClientConfig.Builder()
            .protocol("nfs")
            .remoteDirectory(testNfsDirectory.toString())
            .fileSeparator("/")
            .gzipEnabled(true)
            .build();

        try (NasClient compressedClient = new NasClient(nfsConsumerConfig, compressedProducerConfig)) {
            String fileName = TEST_FILE_PREFIX + "compressed-" + System.currentTimeMillis() + ".txt";
            String testMessage = TEST_CONTENT + "for compression test - this is a longer message to test compression";

            assertDoesNotThrow(() -> {
                compressedClient.sendMessage(fileName, testMessage);
            });

            // Verify file was created and is compressed
            Path expectedFile = testNfsDirectory.resolve(fileName);
            assertTrue(Files.exists(expectedFile), "Compressed file should have been created");

            logger.info("Successfully tested NFS compression with file: {}", fileName);
        }
    }

    @Test
    void testNfsConnectionManagement() throws Exception {
        // Test connection lifecycle management for NFS

        // Test that client can handle multiple operations
        String fileName1 = TEST_FILE_PREFIX + "nfs-conn1-" + System.currentTimeMillis() + ".txt";
        String fileName2 = TEST_FILE_PREFIX + "nfs-conn2-" + System.currentTimeMillis() + ".txt";

        assertDoesNotThrow(() -> {
            nfsClient.sendMessage(fileName1, "First NFS connection test");
            nfsClient.sendMessage(fileName2, "Second NFS connection test");
        });

        // Test that connections are properly managed
        Thread.sleep(500);

        assertDoesNotThrow(() -> {
            NasClient.NasMessage message = nfsClient.receiveMessage();
            logger.info("NFS connection management test - received: {}",
                       message != null ? message.getFileName() : "null");
        });

        logger.info("Successfully tested NFS connection management");
    }

    @Test
    void testNfsMessageListener() throws Exception {
        AtomicInteger messageCount = new AtomicInteger(0);
        CountDownLatch latch = new CountDownLatch(2);

        // Start listener
        nfsClient.startListener(message -> {
            logger.info("Received NFS file via listener: {}", message.getFileName());
            messageCount.incrementAndGet();
            latch.countDown();
        });

        // Create multiple files directly
        for (int i = 1; i <= 2; i++) {
            String fileName = TEST_FILE_PREFIX + "nfs-listener-" + i + "-" + System.currentTimeMillis() + ".txt";
            String testMessage = "NFS listener test file " + i;
            Files.writeString(testNfsDirectory.resolve(fileName), testMessage);
            Thread.sleep(500); // Small delay between files
        }

        // Wait for messages to be processed (with timeout)
        boolean received = latch.await(15, TimeUnit.SECONDS);
        nfsClient.stopListener();

        assertTrue(received, "Should have processed files within timeout");
        assertTrue(messageCount.get() >= 1, "Should have processed at least 1 file");

        logger.info("Successfully processed {} NFS files via listener", messageCount.get());
    }

    @Test
    void testSmbMessageListener() throws Exception {
        AtomicInteger messageCount = new AtomicInteger(0);
        CountDownLatch latch = new CountDownLatch(2);

        // Start listener
        smbClient.startListener(message -> {
            logger.info("Received SMB file via listener: {}", message.getFileName());
            messageCount.incrementAndGet();
            latch.countDown();
        });

        // Create multiple files directly
        for (int i = 1; i <= 2; i++) {
            String fileName = TEST_FILE_PREFIX + "smb-listener-" + i + "-" + System.currentTimeMillis() + ".txt";
            String testMessage = "SMB listener test file " + i;
            Files.writeString(testSmbDirectory.resolve(fileName), testMessage);
            Thread.sleep(500); // Small delay between files
        }

        // Wait for messages to be processed (with timeout)
        boolean received = latch.await(15, TimeUnit.SECONDS);
        smbClient.stopListener();

        assertTrue(received, "Should have processed files within timeout");
        assertTrue(messageCount.get() >= 1, "Should have processed at least 1 file");

        logger.info("Successfully processed {} SMB files via listener", messageCount.get());
    }

    @Test
    void testNfsWaitForMessages() throws Exception {
        AtomicInteger processedCount = new AtomicInteger(0);

        // Create files first
        for (int i = 1; i <= 2; i++) {
            String fileName = TEST_FILE_PREFIX + "nfs-wait-" + i + "-" + System.currentTimeMillis() + ".txt";
            String testMessage = "NFS wait test file " + i;
            Files.writeString(testNfsDirectory.resolve(fileName), testMessage);
        }

        // Wait for files to be processed
        List<NasClient.NasMessage> messages = nfsClient.waitForMessages(2, 15000, message -> {
            logger.info("Processed NFS file: {}", message.getFileName());
            processedCount.incrementAndGet();
        });

        assertTrue(messages.size() >= 1, "Should have received at least 1 file");
        assertTrue(processedCount.get() >= 1, "Should have processed at least 1 file");

        logger.info("Successfully waited for and processed {} NFS files", processedCount.get());
    }

    @Test
    void testCustomNfsConfiguration() throws Exception {
        // Test with custom NFS configuration
        Path customTestDir = Files.createTempDirectory("nas-custom-test");

        try {
            NasClientConfig customConsumerConfig = new NasClientConfig.Builder()
                .protocol("nfs")
                .remoteDirectory(customTestDir.toString())
                .fileNamePattern("custom-.*\\.txt")
                .pollingIntervalMs(3000L)
                .charset("UTF-8")
                .postProcessAction("rename")
                .renamePattern("{file}.processed")
                .fileSortOrder("NEWEST")
                .maxFilesPerPoll(5)
                .fileAgeMs(1000L)
                .ignoreHiddenFiles(true)
                .build();

            NasClientConfig customProducerConfig = new NasClientConfig.Builder()
                .protocol("nfs")
                .remoteDirectory(customTestDir.toString())
                .fileSeparator("/")
                .gzipEnabled(false)
                .build();

            try (NasClient customClient = new NasClient(customConsumerConfig, customProducerConfig)) {
                String fileName = "custom-test-" + System.currentTimeMillis() + ".txt";
                String testMessage = "Custom NFS config test - " + System.currentTimeMillis();

                assertDoesNotThrow(() -> {
                    customClient.sendMessage(fileName, testMessage);
                });

                // Verify file was created
                Path expectedFile = customTestDir.resolve(fileName);
                assertTrue(Files.exists(expectedFile), "File should have been created");

                logger.info("Successfully tested custom NFS configuration with file: {}", fileName);
            }
        } finally {
            deleteDirectory(customTestDir);
        }
    }

    @Test
    void testErrorHandling() {
        // Test with invalid NFS configuration to verify error handling
        NasClientConfig invalidConfig = new NasClientConfig.Builder()
            .protocol("nfs")
            .remoteDirectory("/invalid/nonexistent/path")
            .fileSeparator("/")
            .build();

        try (NasClient invalidClient = new NasClient(invalidConfig, invalidConfig)) {
            // This should throw an exception due to invalid path
            assertThrows(Exception.class, () -> {
                invalidClient.sendMessage("test.txt", "This should fail");
            });

            logger.info("Correctly handled invalid NFS configuration");
        }
    }

    @Test
    void testNfsFileExistenceCheck() throws Exception {
        // Test file existence checking before upload for NFS
        String fileName = TEST_FILE_PREFIX + "nfs-exists-" + System.currentTimeMillis() + ".txt";
        String testMessage = TEST_CONTENT + "for existence test";

        // Send file first time - should succeed
        assertDoesNotThrow(() -> {
            nfsClient.sendMessage(fileName, testMessage);
        });

        // Try to send same file again - should fail due to existence check
        assertThrows(RuntimeException.class, () -> {
            nfsClient.sendMessage(fileName, testMessage + " - second attempt");
        });

        logger.info("Successfully tested NFS file existence checking for: {}", fileName);
    }

    @Test
    void testSmbFileExistenceCheck() throws Exception {
        // Test file existence checking before upload for SMB
        String fileName = TEST_FILE_PREFIX + "smb-exists-" + System.currentTimeMillis() + ".txt";
        String testMessage = TEST_CONTENT + "for SMB existence test";

        // Send file first time - should succeed
        assertDoesNotThrow(() -> {
            smbClient.sendMessage(fileName, testMessage);
        });

        // Try to send same file again - should fail due to existence check
        assertThrows(RuntimeException.class, () -> {
            smbClient.sendMessage(fileName, testMessage + " - second attempt");
        });

        logger.info("Successfully tested SMB file existence checking for: {}", fileName);
    }

    @Test
    void testLargeFileHandling() throws Exception {
        // Test handling of larger files for NFS
        String fileName = TEST_FILE_PREFIX + "nfs-large-" + System.currentTimeMillis() + ".txt";
        StringBuilder largeContent = new StringBuilder();

        // Create a larger test content (about 10KB)
        for (int i = 0; i < 1000; i++) {
            largeContent.append("This is line ").append(i).append(" of the large NFS test file content.\n");
        }

        String testMessage = largeContent.toString();

        assertDoesNotThrow(() -> {
            nfsClient.sendMessage(fileName, testMessage);
        });

        Thread.sleep(1000);

        // Try to receive the large file
        NasClient.NasMessage receivedMessage = nfsClient.receiveMessage();

        if (receivedMessage != null && receivedMessage.getFileName().equals(fileName)) {
            assertTrue(receivedMessage.getFileSize() > 5000, "Large file should be over 5KB");
            assertTrue(receivedMessage.getContent().contains("This is line"), "Content should match");
            logger.info("Successfully handled large NFS file: {} ({} bytes)",
                       fileName, receivedMessage.getFileSize());
        } else {
            logger.info("Large NFS file test completed - file may have been processed by another test");
        }
    }

    @Test
    void testSpecialCharacterHandling() throws Exception {
        // Test handling of files with special characters in content for NFS
        String fileName = TEST_FILE_PREFIX + "nfs-special-" + System.currentTimeMillis() + ".txt";
        String specialContent = "Special chars: àáâãäåæçèéêëìíîïðñòóôõö÷øùúûüýþÿ\n" +
                               "Symbols: !@#$%^&*()_+-=[]{}|;':\",./<>?\n" +
                               "Unicode: 你好世界 🌍 🚀 ⭐\n" +
                               "Newlines and tabs:\tTabbed\tContent\n";

        assertDoesNotThrow(() -> {
            nfsClient.sendMessage(fileName, specialContent);
        });

        Thread.sleep(1000);

        NasClient.NasMessage receivedMessage = nfsClient.receiveMessage();

        if (receivedMessage != null && receivedMessage.getFileName().equals(fileName)) {
            assertTrue(receivedMessage.getContent().contains("Special chars"),
                      "Should preserve special characters");
            assertTrue(receivedMessage.getContent().contains("你好世界"),
                      "Should preserve Unicode characters");
            logger.info("Successfully handled special characters in NFS file: {}", fileName);
        } else {
            logger.info("Special character NFS test completed - file may have been processed");
        }
    }

    @Test
    void testConcurrentNfsOperations() throws Exception {
        // Test concurrent file operations for NFS
        CountDownLatch latch = new CountDownLatch(3);
        AtomicInteger successCount = new AtomicInteger(0);

        // Start multiple threads sending files concurrently
        for (int i = 1; i <= 3; i++) {
            final int threadNum = i;
            new Thread(() -> {
                try {
                    String fileName = TEST_FILE_PREFIX + "nfs-concurrent-" + threadNum + "-" + System.currentTimeMillis() + ".txt";
                    String content = "NFS concurrent test content from thread " + threadNum;

                    nfsClient.sendMessage(fileName, content);
                    successCount.incrementAndGet();
                    logger.info("Thread {} successfully sent NFS file: {}", threadNum, fileName);
                } catch (Exception e) {
                    logger.error("Thread {} failed to send NFS file: {}", threadNum, e.getMessage());
                } finally {
                    latch.countDown();
                }
            }).start();
        }

        // Wait for all threads to complete
        boolean completed = latch.await(30, TimeUnit.SECONDS);

        assertTrue(completed, "All concurrent NFS operations should complete within timeout");
        assertTrue(successCount.get() >= 1, "At least one concurrent NFS operation should succeed");

        logger.info("Successfully tested concurrent NFS operations - {} out of 3 succeeded", successCount.get());
    }

    @Test
    void testUnsupportedProtocol() {
        // Test with unsupported protocol
        NasClientConfig invalidProtocolConfig = new NasClientConfig.Builder()
            .protocol("ftp")  // Unsupported protocol
            .remoteDirectory("/tmp")
            .build();

        try (NasClient invalidClient = new NasClient(invalidProtocolConfig, invalidProtocolConfig)) {
            assertThrows(IllegalArgumentException.class, () -> {
                invalidClient.sendMessage("test.txt", "This should fail");
            });

            assertThrows(IllegalArgumentException.class, () -> {
                invalidClient.receiveMessage();
            });

            logger.info("Correctly handled unsupported protocol");
        }
    }

    // Helper method to delete directories recursively
    private void deleteDirectory(Path directory) throws IOException {
        if (Files.exists(directory)) {
            Files.walk(directory)
                .sorted(Comparator.reverseOrder())
                .map(Path::toFile)
                .forEach(File::delete);
        }
    }

    /**
     * Manual test method for interactive testing - not run automatically
     * Run this method manually to test against actual NAS servers
     */
    public static void main(String[] args) {
        Logger mainLogger = LoggerFactory.getLogger("NasClientManualTest");

        try {
            mainLogger.info("Starting manual NAS client test...");

            // Test NFS
            NasClientConfig nfsConsumerConfig = NasClientConfig.createNfsConsumerConfig();
            NasClientConfig nfsProducerConfig = NasClientConfig.createNfsProducerConfig();

            try (NasClient nfsClient = new NasClient(nfsConsumerConfig, nfsProducerConfig)) {

                // Test 1: Send a file via NFS
                mainLogger.info("Test 1: Sending NFS file...");
                String fileName = "manual-nfs-test-" + System.currentTimeMillis() + ".txt";
                String testMessage = "Manual NFS test message - " + System.currentTimeMillis();
                nfsClient.sendMessage(fileName, testMessage);
                mainLogger.info("NFS file sent successfully: {}", fileName);

                // Test 2: Receive a file via NFS
                mainLogger.info("Test 2: Receiving NFS file...");
                NasClient.NasMessage received = nfsClient.receiveMessage();
                if (received != null) {
                    mainLogger.info("Received NFS file: {} with content: {}",
                                   received.getFileName(), received.getContent());
                } else {
                    mainLogger.info("No NFS file received");
                }

                // Test 3: Start listener for a short time
                mainLogger.info("Test 3: Starting NFS listener for 10 seconds...");
                nfsClient.startListener(message -> {
                    mainLogger.info("NFS listener received file: {} (size: {} bytes)",
                                   message.getFileName(), message.getFileSize());
                });

                Thread.sleep(10000);
                nfsClient.stopListener();
                mainLogger.info("NFS listener stopped");
            }

            // Test SMB
            NasClientConfig smbConsumerConfig = NasClientConfig.createSmbConsumerConfig();
            NasClientConfig smbProducerConfig = NasClientConfig.createSmbProducerConfig();

            try (NasClient smbClient = new NasClient(smbConsumerConfig, smbProducerConfig)) {

                // Test 4: Send a file via SMB
                mainLogger.info("Test 4: Sending SMB file...");
                String smbFileName = "manual-smb-test-" + System.currentTimeMillis() + ".txt";
                String smbTestMessage = "Manual SMB test message - " + System.currentTimeMillis();
                smbClient.sendMessage(smbFileName, smbTestMessage);
                mainLogger.info("SMB file sent successfully: {}", smbFileName);

                // Test 5: Receive a file via SMB
                mainLogger.info("Test 5: Receiving SMB file...");
                NasClient.NasMessage smbReceived = smbClient.receiveMessage();
                if (smbReceived != null) {
                    mainLogger.info("Received SMB file: {} with content: {}",
                                   smbReceived.getFileName(), smbReceived.getContent());
                } else {
                    mainLogger.info("No SMB file received");
                }
            }

            mainLogger.info("Manual test completed successfully");

        } catch (Exception e) {
            mainLogger.error("Manual test failed: {}", e.getMessage(), e);
        }
    }
}
