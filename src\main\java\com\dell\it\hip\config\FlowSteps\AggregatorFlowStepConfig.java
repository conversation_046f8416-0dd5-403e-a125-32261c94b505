package com.dell.it.hip.config.FlowSteps;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class AggregatorFlowStepConfig extends FlowStepConfig {
    private List<DocTypeAggregatorConfig> docTypeConfigs = new ArrayList<>();
    private DefaultAggregatorConfig defaultConfig;

    @Data
    public static class DocTypeAggregatorConfig extends DocTypeConfig {
        private AggregatorBehavior behavior = AggregatorBehavior.AGGREGATE;
        private int batchSize;
        private long batchTimeoutMs;
        private List<String> groupByHeaders;
        private List<String> preserveHeaders;
        private String aggregateTraceHeaderName;
        // + any format-specific params (e.g. for XML/EDI)
    }

    @Data
    public static class DefaultAggregatorConfig {
        private AggregatorBehavior behavior = AggregatorBehavior.AGGREGATE;
        private int batchSize = 10;
        private long batchTimeoutMs = 30000;
        private List<String> groupByHeaders;
        private List<String> preserveHeaders;
        private String aggregateTraceHeaderName;
    }

    public enum AggregatorBehavior { AGGREGATE, SKIP, TERMINATE }
}