package com.dell.it.hip.config.FlowSteps;

import java.util.List;

public class AggregatorFlowStepConfig extends FlowStepConfig {
    private int batchSize; // Minimum # of messages to trigger batch
    private long batchTimeoutMs; // Max ms to wait before forcing batch
    private List<String> groupByHeaders; // Optional: Group by these header fields
    // Optionally, user can add headers they *explicitly* want aggregated as lists
    private List<String> preserveHeaders;
    // Optional, for custom trace context aggregation header name
    private String aggregateTraceHeaderName;

    // --- Getters & setters ---
    public int getBatchSize() { return batchSize; }
    public void setBatchSize(int batchSize) { this.batchSize = batchSize; }

    public long getBatchTimeoutMs() { return batchTimeoutMs; }
    public void setBatchTimeoutMs(long batchTimeoutMs) { this.batchTimeoutMs = batchTimeoutMs; }

    public List<String> getGroupByHeaders() { return groupByHeaders; }
    public void setGroupByHeaders(List<String> groupByHeaders) { this.groupByHeaders = groupByHeaders; }

    public List<String> getPreserveHeaders() { return preserveHeaders; }
    public void setPreserveHeaders(List<String> preserveHeaders) { this.preserveHeaders = preserveHeaders; }

    public String getAggregateTraceHeaderName() { return aggregateTraceHeaderName; }
    public void setAggregateTraceHeaderName(String aggregateTraceHeaderName) { this.aggregateTraceHeaderName = aggregateTraceHeaderName; }
}