-- =====================================================
-- Oracle Views and Procedures for HIP Integration Requests
-- =====================================================
-- This script creates useful views and stored procedures
-- for working with the hip_integration_requests table
-- =====================================================

-- Create a view for active (non-deleted) integration requests
CREATE OR REPLACE VIEW v_active_hip_integration_requests AS
SELECT 
    id,
    service_manager_name,
    hip_integration_name,
    version,
    business_flow_name,
    tags,
    business_flow_type,
    hip_integration_type,
    business_flow_version,
    status,
    created_at,
    updated_at
FROM hip_integration_requests
WHERE deleted = 0;

-- Add comment to the view
COMMENT ON VIEW v_active_hip_integration_requests IS 'View showing only active (non-deleted) HIP integration requests';

-- Create a view for integration request summary
CREATE OR REPLACE VIEW v_hip_integration_request_summary AS
SELECT 
    service_manager_name,
    COUNT(*) as total_requests,
    COUNT(CASE WHEN status = 'ACTIVE' THEN 1 END) as active_requests,
    COUNT(CASE WHEN status = 'INACTIVE' THEN 1 END) as inactive_requests,
    COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pending_requests,
    COUNT(CASE WHEN deleted = 1 THEN 1 END) as deleted_requests,
    MIN(created_at) as first_created,
    MAX(updated_at) as last_updated
FROM hip_integration_requests
GROUP BY service_manager_name;

-- Add comment to the summary view
COMMENT ON VIEW v_hip_integration_request_summary IS 'Summary statistics of integration requests by service manager';

-- Create a stored procedure for soft delete
CREATE OR REPLACE PROCEDURE sp_soft_delete_hip_integration_request(
    p_id IN NUMBER,
    p_result OUT VARCHAR2
)
AS
    v_count NUMBER;
BEGIN
    -- Check if record exists and is not already deleted
    SELECT COUNT(*)
    INTO v_count
    FROM hip_integration_requests
    WHERE id = p_id AND deleted = 0;
    
    IF v_count = 0 THEN
        p_result := 'ERROR: Record not found or already deleted';
        RETURN;
    END IF;
    
    -- Perform soft delete
    UPDATE hip_integration_requests
    SET deleted = 1,
        updated_at = SYSTIMESTAMP
    WHERE id = p_id;
    
    COMMIT;
    p_result := 'SUCCESS: Record soft deleted';
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        p_result := 'ERROR: ' || SQLERRM;
END sp_soft_delete_hip_integration_request;
/

-- Create a stored procedure for restoring soft deleted records
CREATE OR REPLACE PROCEDURE sp_restore_hip_integration_request(
    p_id IN NUMBER,
    p_result OUT VARCHAR2
)
AS
    v_count NUMBER;
BEGIN
    -- Check if record exists and is deleted
    SELECT COUNT(*)
    INTO v_count
    FROM hip_integration_requests
    WHERE id = p_id AND deleted = 1;
    
    IF v_count = 0 THEN
        p_result := 'ERROR: Record not found or not deleted';
        RETURN;
    END IF;
    
    -- Restore the record
    UPDATE hip_integration_requests
    SET deleted = 0,
        updated_at = SYSTIMESTAMP
    WHERE id = p_id;
    
    COMMIT;
    p_result := 'SUCCESS: Record restored';
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        p_result := 'ERROR: ' || SQLERRM;
END sp_restore_hip_integration_request;
/

-- Create a function to get the latest version of an integration
CREATE OR REPLACE FUNCTION fn_get_latest_integration_version(
    p_service_manager_name IN VARCHAR2,
    p_hip_integration_name IN VARCHAR2
) RETURN VARCHAR2
AS
    v_latest_version VARCHAR2(100);
BEGIN
    SELECT version
    INTO v_latest_version
    FROM (
        SELECT version
        FROM hip_integration_requests
        WHERE service_manager_name = p_service_manager_name
          AND hip_integration_name = p_hip_integration_name
          AND deleted = 0
        ORDER BY created_at DESC
    )
    WHERE ROWNUM = 1;
    
    RETURN v_latest_version;
    
EXCEPTION
    WHEN NO_DATA_FOUND THEN
        RETURN NULL;
    WHEN OTHERS THEN
        RETURN NULL;
END fn_get_latest_integration_version;
/

-- Create a procedure for bulk status update
CREATE OR REPLACE PROCEDURE sp_bulk_update_status(
    p_service_manager_name IN VARCHAR2,
    p_old_status IN VARCHAR2,
    p_new_status IN VARCHAR2,
    p_updated_count OUT NUMBER
)
AS
BEGIN
    UPDATE hip_integration_requests
    SET status = p_new_status,
        updated_at = SYSTIMESTAMP
    WHERE service_manager_name = p_service_manager_name
      AND status = p_old_status
      AND deleted = 0;
    
    p_updated_count := SQL%ROWCOUNT;
    COMMIT;
    
EXCEPTION
    WHEN OTHERS THEN
        ROLLBACK;
        p_updated_count := -1;
END sp_bulk_update_status;
/

-- Create indexes on the views for better performance
-- Note: Oracle automatically creates indexes on materialized views, 
-- but these are regular views, so we rely on the base table indexes

-- Grant permissions on views and procedures (adjust as needed)
-- GRANT SELECT ON v_active_hip_integration_requests TO hip_app_user;
-- GRANT SELECT ON v_hip_integration_request_summary TO hip_app_user;
-- GRANT EXECUTE ON sp_soft_delete_hip_integration_request TO hip_app_user;
-- GRANT EXECUTE ON sp_restore_hip_integration_request TO hip_app_user;
-- GRANT EXECUTE ON fn_get_latest_integration_version TO hip_app_user;
-- GRANT EXECUTE ON sp_bulk_update_status TO hip_app_user;

-- =====================================================
-- End of Views and Procedures Script
-- =====================================================
