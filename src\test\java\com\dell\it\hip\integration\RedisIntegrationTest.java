package com.dell.it.hip.integration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * Integration tests for Redis functionality using TestContainers.
 * Extends BaseIntegrationTest for shared container setup and Docker validation.
 */
//@Disabled
class RedisIntegrationTest extends BaseIntegrationTest {

    private static final Logger logger = LoggerFactory.getLogger(RedisIntegrationTest.class);

    @Autowired
    private StringRedisTemplate redisTemplate;

    @BeforeEach
    void setUp() {    	
        // Clear Redis before each test
        try {
            redisTemplate.getConnectionFactory().getConnection().serverCommands().flushAll();
            logger.debug("Redis cleared successfully before test");
        } catch (Exception e) {
            logger.warn("Failed to clear Redis before test: {}", e.getMessage());
        }
    }

    @Test    
    void testRedisConnection() {
        // Test basic Redis connectivity
        String key = "test:connection";
        String value = "connected";

        redisTemplate.opsForValue().set(key, value);
        String retrieved = redisTemplate.opsForValue().get(key);

        assertEquals(value, retrieved);
    }

    @Test
    void testHashOperations() {
        // Test hash operations for complex data structures
        String hashKey = "hip:test-service:config:test-integration:1.0";

        redisTemplate.opsForHash().put(hashKey, "maxRequests", "100");
        redisTemplate.opsForHash().put(hashKey, "timeWindow", "60");
        redisTemplate.opsForHash().put(hashKey, "enabled", "true");

        assertEquals("100", redisTemplate.opsForHash().get(hashKey, "maxRequests"));
        assertEquals("60", redisTemplate.opsForHash().get(hashKey, "timeWindow"));
        assertEquals("true", redisTemplate.opsForHash().get(hashKey, "enabled"));

        // Test hash size
        assertEquals(3L, redisTemplate.opsForHash().size(hashKey));
    }

    @Test
    void testListOperations() {
        // Test list operations for message queues
        String listKey = "hip:test-service:queue:test-integration:1.0";

        // Push messages to queue
        redisTemplate.opsForList().leftPush(listKey, "message1");
        redisTemplate.opsForList().leftPush(listKey, "message2");
        redisTemplate.opsForList().leftPush(listKey, "message3");

        // Check queue size
        assertEquals(3L, redisTemplate.opsForList().size(listKey));

        // Pop messages from queue
        assertEquals("message3", redisTemplate.opsForList().rightPop(listKey));
        assertEquals("message2", redisTemplate.opsForList().rightPop(listKey));
        assertEquals("message1", redisTemplate.opsForList().rightPop(listKey));

        // Verify queue is empty
        assertEquals(0L, redisTemplate.opsForList().size(listKey));
    }

    @Test
    void testSetOperations() {
        // Test set operations for unique collections
        String setKey = "hip:test-service:active-integrations";

        redisTemplate.opsForSet().add(setKey, "integration1:1.0");
        redisTemplate.opsForSet().add(setKey, "integration2:1.0");
        redisTemplate.opsForSet().add(setKey, "integration1:1.0"); // Duplicate

        // Verify set size (duplicates not counted)
        assertEquals(2L, redisTemplate.opsForSet().size(setKey));

        // Test membership
        assertTrue(redisTemplate.opsForSet().isMember(setKey, "integration1:1.0"));
        assertFalse(redisTemplate.opsForSet().isMember(setKey, "integration3:1.0"));

        // Remove member
        redisTemplate.opsForSet().remove(setKey, "integration1:1.0");
        assertEquals(1L, redisTemplate.opsForSet().size(setKey));
    }
}
