package com.dell.it.hip.config.FlowSteps;

import com.dell.it.hip.config.rules.RuleEnabledStepConfig;
import com.dell.it.hip.config.rules.RuleRef;

import java.util.List;

public class StrictOrderConfig extends FlowStepConfig implements RuleEnabledStepConfig {
    private List<String> orderingKeys;        // Headers used for partitioning (e.g. ["customerId", "orderType"])
    private String sequenceHeader;            // Header field holding the sequence number (e.g. "HIP.seqNo")
    private Long maxHoldTimeoutMs;            // Optional: max time to buffer before alerting (for expiry handling)
    private List<RuleRef> ruleRefs;              // If you want to add rules for strict order (e.g. for dynamic partitioning)

    // getters/setters

    public List<String> getOrderingKeys() { return orderingKeys; }
    public void setOrderingKeys(List<String> orderingKeys) { this.orderingKeys = orderingKeys; }

    public String getSequenceHeader() { return sequenceHeader; }
    public void setSequenceHeader(String sequenceHeader) { this.sequenceHeader = sequenceHeader; }

    public Long getMaxHoldTimeoutMs() { return maxHoldTimeoutMs; }
    public void setMaxHoldTimeoutMs(Long maxHoldTimeoutMs) { this.maxHoldTimeoutMs = maxHoldTimeoutMs; }

    public void setRuleRefs(List<RuleRef> ruleRefs) { this.ruleRefs = ruleRefs; }

    @Override
    public List<RuleRef> getRuleRefs() {
        return ruleRefs;
    }
}