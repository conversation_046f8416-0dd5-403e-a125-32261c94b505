package com.dell.it.hip.config.FlowSteps;

import java.util.ArrayList;
import java.util.List;

import com.dell.it.hip.config.rules.RuleRef;

import lombok.Data;

@Data
public class StrictOrderConfig extends FlowStepConfig {
	
	private List<DocTypeStrictOrderConfig> docTypeConfigs = new ArrayList<>();
    private DefaultStrictOrderConfig defaultConfig;
    
    @Data
    public static class DocTypeStrictOrderConfig extends DocTypeConfig {
        private StrictOrderBehavior behavior = StrictOrderBehavior.STRICTORDER;
        private List<String> orderingKeys;        // Headers used for partitioning (e.g. ["customerId", "orderType"])
    	private String sequenceHeader;            // Header field holding the sequence number (e.g. "HIP.seqNo")
    	private Long maxHoldTimeoutMs;            // Optional: max time to buffer before alerting (for expiry handling)
    	private List<RuleRef> ruleRefs;           // If you want to add rules for strict order (e.g. for dynamic partitioning)
    }

    @Data
    public static class DefaultStrictOrderConfig {
        private StrictOrderBehavior behavior = StrictOrderBehavior.STRICTORDER;
        private List<String> orderingKeys;        // Headers used for partitioning (e.g. ["customerId", "orderType"])
    	private String sequenceHeader;            // Header field holding the sequence number (e.g. "HIP.seqNo")
    	private Long maxHoldTimeoutMs = 30000L;   // Optional: max time to buffer before alerting (for expiry handling)
    	private List<RuleRef> ruleRefs;           // If you want to add rules for strict order (e.g. for dynamic partitioning)
    }

    public enum StrictOrderBehavior { STRICTORDER, SKIP, TERMINATE }
}