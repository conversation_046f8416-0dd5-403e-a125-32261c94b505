# HIP Services Configuration Sequence Diagram

This sequence diagram shows the flow of configuration property deserialization and usage in the HIP Services framework, highlighting the new dot-separated lowercase naming convention.

```mermaid
sequenceDiagram
    participant Client as Integration Client
    participant SM as Service Manager
    participant PSF as PropertySheetFetcher
    participant CS as Config Server
    participant CCR as ConfigClassRegistry
    participant HID as HIPIntegrationDefinition
    participant OM as ObjectMapper
    participant DKA as DynamicKafkaInputAdapter
    participant DKH as DynamicKafkaOutputHandler
    participant KAFKA as Kafka Cluster

    Note over Client,KAFKA: Configuration Loading and Message Processing Flow

    %% Integration Registration
    Client->>SM: POST /integrations<br/>{hipIntegrationName, propertySheets, adapters, handlers}

    %% Property Sheet Fetching
    SM->>PSF: fetchAndMerge(propertySheets)
    PSF->>CS: GET /shared-kafka-properties/prod
    CS-->>PSF: JSON Properties
    PSF->>CS: GET /orderProcessing-adapter-kafka/prod
    CS-->>PSF: JSON Properties<br/>{"kafka.consumer.bootstrap.servers": "prod-kafka:9092",<br/>"kafka.consumer.security.protocol": "SASL_SSL",<br/>"kafka.consumer.sasl.mechanism": "PLAIN"}
    PSF-->>SM: Merged Properties Map

    %% Configuration Class Resolution
    SM->>CCR: resolveAdapterConfigClass("kafkaAdapter")
    CCR-->>SM: DynamicKafkaAdapterConfig.class
    SM->>CCR: resolveHandlerConfigClass("kafkaHandler")
    CCR-->>SM: DynamicKafkaHandlerConfig.class

    %% JSON Deserialization with New Property Names
    SM->>OM: readValue(properties, DynamicKafkaAdapterConfig.class)
    Note over OM: Deserializes using @JsonProperty annotations:<br/>kafka.consumer.bootstrap.servers → bootstrapServers<br/>kafka.consumer.security.protocol → securityProtocol<br/>kafka.consumer.sasl.mechanism → saslMechanism
    OM-->>SM: DynamicKafkaAdapterConfig instance

    SM->>OM: readValue(properties, DynamicKafkaHandlerConfig.class)
    Note over OM: Deserializes using @JsonProperty annotations:<br/>kafka.producer.bootstrap.servers → bootstrapServers<br/>kafka.producer.security.protocol → securityProtocol<br/>kafka.producer.sasl.jaas.config → sasljaasconfig
    OM-->>SM: DynamicKafkaHandlerConfig instance

    %% Integration Definition Creation
    SM->>HID: new HIPIntegrationDefinition()
    SM->>HID: setConfigMap(propertyRef, configInstance)
    SM->>HID: setMergedProperties(mergedProps)

    %% Adapter Initialization
    SM->>DKA: buildProducer(def, adapterConfigRef)
    DKA->>HID: getConfigMap().get(propertyRef)
    HID-->>DKA: DynamicKafkaAdapterConfig
    Note over DKA: Uses config properties:<br/>config.getBootstrapServers()<br/>config.getSecurityProtocol()<br/>config.getSaslMechanism()
    DKA->>KAFKA: Create Kafka Consumer<br/>bootstrap.servers=prod-kafka:9092<br/>security.protocol=SASL_SSL<br/>sasl.mechanism=PLAIN
    KAFKA-->>DKA: Consumer Created

    %% Message Processing Flow
    KAFKA->>DKA: Message Received
    DKA->>SM: processInboundMessage(message)
    SM->>SM: Flow Processing Steps
    SM->>DKH: handle(message, def, handlerConfigRef)

    %% Handler Configuration Usage
    DKH->>HID: getConfig(propertyRef, DynamicKafkaHandlerConfig.class)
    HID-->>DKH: DynamicKafkaHandlerConfig
    Note over DKH: Uses config properties:<br/>config.getBootstrapServers()<br/>config.getTopic()<br/>config.getSecurityProtocol()<br/>config.getSasljaasconfig()
    DKH->>KAFKA: Send Message<br/>Using producer properties from config
    KAFKA-->>DKH: Message Sent

    %% Error Handling for Invalid Properties
    Note over Client,KAFKA: Error Handling for Invalid Property Names
    Client->>SM: POST /integrations<br/>with old property names
    SM->>OM: readValue(invalidProperties, DynamicKafkaHandlerConfig.class)
    Note over OM: Old property names not recognized:<br/>kafka.producer.bootstrapServers (invalid)<br/>Should be: kafka.producer.bootstrap.servers
    OM-->>SM: UnrecognizedPropertyException
    SM-->>Client: 400 Bad Request<br/>Invalid property names

    %% Configuration Validation
    Note over Client,KAFKA: Configuration Validation Flow
    Client->>SM: POST /integrations<br/>with new property names
    SM->>OM: readValue(validProperties, DynamicKafkaHandlerConfig.class)
    Note over OM: New property names recognized:<br/>kafka.producer.bootstrap.servers ✓<br/>kafka.producer.security.protocol ✓<br/>kafka.producer.sasl.jaas.config ✓
    OM-->>SM: Valid Configuration Instance
    SM-->>Client: 201 Created<br/>Integration Registered Successfully
```

## Sequence Flow Overview

### 1. Integration Registration
- Client submits integration definition with property sheet references
- Service Manager coordinates the configuration loading process

### 2. Property Sheet Fetching
- PropertySheetFetcher retrieves configurations from Config Server
- Multiple property sheets are merged into a single configuration map
- Environment-specific properties override shared configurations

### 3. Configuration Class Resolution
- ConfigClassRegistry maps adapter/handler types to their configuration classes
- Type-safe configuration objects are resolved for each component

### 4. JSON Deserialization
- ObjectMapper deserializes JSON properties using @JsonProperty annotations
- New dot-separated lowercase naming convention is enforced
- Configuration instances are created with proper type safety

### 5. Runtime Configuration Usage
- Adapters and handlers retrieve their configurations from HIPIntegrationDefinition
- Type-safe property access through getter methods
- Configuration properties are used to initialize external system connections

### 6. Error Handling
- Invalid property names result in UnrecognizedPropertyException
- Clear error messages guide users to correct property naming
- Validation ensures only properly named properties are accepted

### Key Benefits
- **Type Safety**: Compile-time validation of configuration access
- **Consistency**: Uniform property naming across all technologies
- **Validation**: Runtime validation of property names and values
- **Flexibility**: Dynamic configuration loading and updates
