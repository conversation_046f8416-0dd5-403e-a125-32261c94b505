package com.dell.it.hip.config.Handlers;

public class KafkaHandlerConfig {
    private String topic;
    private String key;  // Optional partition key
    private boolean gzip;

    // Add more config fields as needed

    public String getTopic() {
        return topic;
    }
    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getKey() {
        return key;
    }
    public void setKey(String key) {
        this.key = key;
    }

    public boolean isGzip() {
        return gzip;
    }
    public void setGzip(boolean gzip) {
        this.gzip = gzip;
    }
}