package com.dell.it.hip.client;

/**
 * Configuration class for MQClient that mirrors the property structure
 * used in DynamicIBMMQAdapterConfig and DynamicIbmmqHandlerConfig.
 * 
 * This class can be used for both consumer (adapter testing) and producer (handler testing)
 * configurations by using the appropriate property prefixes.
 */
public class MQClientConfig {
    
    // Core MQ connection properties
    private String queueManager;
    private String queue;
    private String channel;
    private String connName;
    private String host;
    private Integer port;
    
    // Authentication
    private String authenticationType;
    private String username;
    private String password;
    
    // SSL/TLS properties
    private String sslCipherSuite;
    private String sslPeerName;
    private String sslKeystore;
    private String sslKeystorePassword;
    private String sslTruststore;
    private String sslTruststorePassword;
    
    // Message properties
    private Integer ccsid;      // Character set ID
    private Integer encoding;   // Message encoding
    private Boolean persistent; // Message persistence (producer only)
    private Boolean compressed; // Message compression
    
    // Consumer properties
    private Integer concurrency;
    private Long receiveTimeout;
    private Long recoveryInterval;
    private Boolean transacted;
    private String messageSelector;
    
    // Default constructor
    public MQClientConfig() {}
    
    // Builder pattern constructor
    private MQClientConfig(Builder builder) {
        this.queueManager = builder.queueManager;
        this.queue = builder.queue;
        this.channel = builder.channel;
        this.connName = builder.connName;
        this.host = builder.host;
        this.port = builder.port;
        this.authenticationType = builder.authenticationType;
        this.username = builder.username;
        this.password = builder.password;
        this.sslCipherSuite = builder.sslCipherSuite;
        this.sslPeerName = builder.sslPeerName;
        this.sslKeystore = builder.sslKeystore;
        this.sslKeystorePassword = builder.sslKeystorePassword;
        this.sslTruststore = builder.sslTruststore;
        this.sslTruststorePassword = builder.sslTruststorePassword;
        this.ccsid = builder.ccsid;
        this.encoding = builder.encoding;
        this.persistent = builder.persistent;
        this.compressed = builder.compressed;
        this.concurrency = builder.concurrency;
        this.receiveTimeout = builder.receiveTimeout;
        this.recoveryInterval = builder.recoveryInterval;
        this.transacted = builder.transacted;
        this.messageSelector = builder.messageSelector;
    }
    
    /**
     * Create a consumer configuration with the provided test properties
     */
    public static MQClientConfig createConsumerConfig() {
        return new Builder()
            .queueManager("BIEG4CU07")
            .queue("QA.D365.TEST_MAC_EMFP.SCG_TEST")
            .channel("BIE.GOSS.01.TLS")
            .connName("WMQNLG2A05.AMER.DELL.COM(2043)")
            .authenticationType("none")
            .username("channel.sender")
            .sslCipherSuite("TLS_RSA_WITH_AES_128_CBC_SHA256")
            .concurrency(1)
            .receiveTimeout(5000L)
            .recoveryInterval(5000L)
            .transacted(false)
            .build();
    }
    
    /**
     * Create a producer configuration with the provided test properties
     */
    public static MQClientConfig createProducerConfig() {
        return new Builder()
            .queueManager("BIEG4CU07")
            .queue("QA.D365.TEST_MAC_EMFP.SCG_TEST")
            .channel("BIE.GOSS.01.TLS")
            .connName("WMQNLG2A05.AMER.DELL.COM(2043)")
            .host("WMQNLG2A05.AMER.DELL.COM")
            .port(2043)
            .authenticationType("none")
            .username("channel.sender")
            .sslCipherSuite("TLS_RSA_WITH_AES_128_CBC_SHA256")
            .persistent(false)
            .compressed(false)
            .build();
    }
    
    // Getters and setters
    public String getQueueManager() { return queueManager; }
    public void setQueueManager(String queueManager) { this.queueManager = queueManager; }
    
    public String getQueue() { return queue; }
    public void setQueue(String queue) { this.queue = queue; }
    
    public String getChannel() { return channel; }
    public void setChannel(String channel) { this.channel = channel; }
    
    public String getConnName() { return connName; }
    public void setConnName(String connName) { this.connName = connName; }
    
    public String getHost() { return host; }
    public void setHost(String host) { this.host = host; }
    
    public Integer getPort() { return port; }
    public void setPort(Integer port) { this.port = port; }
    
    public String getAuthenticationType() { return authenticationType; }
    public void setAuthenticationType(String authenticationType) { this.authenticationType = authenticationType; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
    
    public String getSslCipherSuite() { return sslCipherSuite; }
    public void setSslCipherSuite(String sslCipherSuite) { this.sslCipherSuite = sslCipherSuite; }
    
    public String getSslPeerName() { return sslPeerName; }
    public void setSslPeerName(String sslPeerName) { this.sslPeerName = sslPeerName; }
    
    public String getSslKeystore() { return sslKeystore; }
    public void setSslKeystore(String sslKeystore) { this.sslKeystore = sslKeystore; }
    
    public String getSslKeystorePassword() { return sslKeystorePassword; }
    public void setSslKeystorePassword(String sslKeystorePassword) { this.sslKeystorePassword = sslKeystorePassword; }
    
    public String getSslTruststore() { return sslTruststore; }
    public void setSslTruststore(String sslTruststore) { this.sslTruststore = sslTruststore; }
    
    public String getSslTruststorePassword() { return sslTruststorePassword; }
    public void setSslTruststorePassword(String sslTruststorePassword) { this.sslTruststorePassword = sslTruststorePassword; }
    
    public Integer getCcsid() { return ccsid; }
    public void setCcsid(Integer ccsid) { this.ccsid = ccsid; }
    
    public Integer getEncoding() { return encoding; }
    public void setEncoding(Integer encoding) { this.encoding = encoding; }
    
    public Boolean getPersistent() { return persistent; }
    public void setPersistent(Boolean persistent) { this.persistent = persistent; }
    
    public Boolean getCompressed() { return compressed; }
    public void setCompressed(Boolean compressed) { this.compressed = compressed; }
    
    public Integer getConcurrency() { return concurrency; }
    public void setConcurrency(Integer concurrency) { this.concurrency = concurrency; }
    
    public Long getReceiveTimeout() { return receiveTimeout; }
    public void setReceiveTimeout(Long receiveTimeout) { this.receiveTimeout = receiveTimeout; }
    
    public Long getRecoveryInterval() { return recoveryInterval; }
    public void setRecoveryInterval(Long recoveryInterval) { this.recoveryInterval = recoveryInterval; }
    
    public Boolean getTransacted() { return transacted; }
    public void setTransacted(Boolean transacted) { this.transacted = transacted; }
    
    public String getMessageSelector() { return messageSelector; }
    public void setMessageSelector(String messageSelector) { this.messageSelector = messageSelector; }
    
    // Builder pattern for easy configuration
    public static class Builder {
        private String queueManager;
        private String queue;
        private String channel;
        private String connName;
        private String host;
        private Integer port;
        private String authenticationType;
        private String username;
        private String password;
        private String sslCipherSuite;
        private String sslPeerName;
        private String sslKeystore;
        private String sslKeystorePassword;
        private String sslTruststore;
        private String sslTruststorePassword;
        private Integer ccsid;
        private Integer encoding;
        private Boolean persistent;
        private Boolean compressed;
        private Integer concurrency;
        private Long receiveTimeout;
        private Long recoveryInterval;
        private Boolean transacted;
        private String messageSelector;
        
        public Builder queueManager(String queueManager) { this.queueManager = queueManager; return this; }
        public Builder queue(String queue) { this.queue = queue; return this; }
        public Builder channel(String channel) { this.channel = channel; return this; }
        public Builder connName(String connName) { this.connName = connName; return this; }
        public Builder host(String host) { this.host = host; return this; }
        public Builder port(Integer port) { this.port = port; return this; }
        public Builder authenticationType(String authenticationType) { this.authenticationType = authenticationType; return this; }
        public Builder username(String username) { this.username = username; return this; }
        public Builder password(String password) { this.password = password; return this; }
        public Builder sslCipherSuite(String sslCipherSuite) { this.sslCipherSuite = sslCipherSuite; return this; }
        public Builder sslPeerName(String sslPeerName) { this.sslPeerName = sslPeerName; return this; }
        public Builder sslKeystore(String sslKeystore) { this.sslKeystore = sslKeystore; return this; }
        public Builder sslKeystorePassword(String sslKeystorePassword) { this.sslKeystorePassword = sslKeystorePassword; return this; }
        public Builder sslTruststore(String sslTruststore) { this.sslTruststore = sslTruststore; return this; }
        public Builder sslTruststorePassword(String sslTruststorePassword) { this.sslTruststorePassword = sslTruststorePassword; return this; }
        public Builder ccsid(Integer ccsid) { this.ccsid = ccsid; return this; }
        public Builder encoding(Integer encoding) { this.encoding = encoding; return this; }
        public Builder persistent(Boolean persistent) { this.persistent = persistent; return this; }
        public Builder compressed(Boolean compressed) { this.compressed = compressed; return this; }
        public Builder concurrency(Integer concurrency) { this.concurrency = concurrency; return this; }
        public Builder receiveTimeout(Long receiveTimeout) { this.receiveTimeout = receiveTimeout; return this; }
        public Builder recoveryInterval(Long recoveryInterval) { this.recoveryInterval = recoveryInterval; return this; }
        public Builder transacted(Boolean transacted) { this.transacted = transacted; return this; }
        public Builder messageSelector(String messageSelector) { this.messageSelector = messageSelector; return this; }
        
        public MQClientConfig build() {
            return new MQClientConfig(this);
        }
    }
}
