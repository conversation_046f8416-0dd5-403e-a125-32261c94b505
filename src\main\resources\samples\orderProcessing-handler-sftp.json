{"orderSftpFallbackOutput.host": "sftp.company.com", "orderSftpFallbackOutput.port": 22, "orderSftpFallbackOutput.username": "sftpuser", "orderSftpFallbackOutput.privateKey": "/keys/id_rsa", "orderSftpFallbackOutput.remoteDir": "/orders/processed", "orderSftpFallbackOutput.filePattern": "*.json", "orderSftpFallbackOutput.postProcessAction": "rename", "orderSftpFallbackOutput.renamePattern": "archive_${timestamp}_${filename}", "orderSftpFallbackOutput.callbackEnabled": true, "orderSftpFallbackOutput.callbackUrl": "/api/orderprocessing/sftp-callback"}