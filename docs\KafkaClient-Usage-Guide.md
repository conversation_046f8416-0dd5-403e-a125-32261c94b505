# KafkaClient Usage Guide

## Overview

The KafkaClient utility class provides a comprehensive testing framework for Kafka message send and receive operations, specifically designed to validate the Kafka adapter and handler components in the HIP services framework.

## Features

- **Message Sending**: Implements message sending functionality using the same approach as DynamicKafkaOutputHandler
- **Message Receiving**: Implements message receiving functionality using the same approach as DynamicKafkaInputAdapter  
- **Configuration Compatibility**: Uses the same property structure and connection logic as existing adapter/handler implementations
- **Testing Support**: Serves as a standalone testing utility for validating Kafka adapter and handler functionality

## Configuration

### Consumer Properties (for receiving/adapter testing)
```properties
kafka.consumer.bootstrap.servers=kafnlprfgscm.us.dell.com:9094
kafka.consumer.topic.name=GOBIG.B2BFLOWMAC.E2EV.DIT
kafka.consumer.group.id=hip-test-consumer-group
kafka.consumer.client.id=hip-test-consumer
kafka.consumer.security.protocol=SASL_SSL
kafka.consumer.sasl.mechanism=PLAIN
kafka.consumer.username=svc_nphipkafkagscm
kafka.consumer.password=ji?zVD58h2WkfxmY0AL+N*PC
kafka.consumer.ssl.truststore.location=/etc/ssl/certs/kafka-truststore/kafka.client.truststore.jks
kafka.consumer.ssl.truststore.password=good8008
kafka.consumer.ssl.truststore.type=JKS
kafka.consumer.protocols=TLSv1.2
kafka.consumer.sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required
```

### Producer Properties (for sending/handler testing)
```properties
kafka.producer.bootstrap.servers=kafnlprfgscm.us.dell.com:9094
kafka.producer.topic=GOBIG.B2BFLOWMAC.E2EV.DIT
kafka.producer.client.id=hip-test-producer
kafka.producer.security.protocol=SASL_SSL
kafka.producer.sasl.mechanism=PLAIN
kafka.producer.username=svc_nphipkafkagscm
kafka.producer.password=ji?zVD58h2WkfxmY0AL+N*PC
kafka.producer.ssl.truststore.location=/etc/ssl/certs/kafka-truststore/kafka.client.truststore.jks
kafka.producer.ssl.truststore.password=good8008
kafka.producer.ssl.truststore.type=JKS
kafka.producer.protocols=TLSv1.2
kafka.producer.sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required
```

## Usage Examples

### Basic Usage

```java
import com.dell.it.hip.client.KafkaClient;
import com.dell.it.hip.client.KafkaClientConfig;

// Create configurations
KafkaClientConfig consumerConfig = KafkaClientConfig.createConsumerConfig();
KafkaClientConfig producerConfig = KafkaClientConfig.createProducerConfig();

// Create KafkaClient
try (KafkaClient kafkaClient = new KafkaClient(consumerConfig, producerConfig)) {
    
    // Send a message
    kafkaClient.sendMessage("Hello, Kafka!");
    
    // Receive a message
    KafkaClient.KafkaMessage received = kafkaClient.receiveMessage(5000); // 5 second timeout
    if (received != null) {
        System.out.println("Received: " + received.getContent());
        System.out.println("Topic: " + received.getTopic());
        System.out.println("Partition: " + received.getPartition());
        System.out.println("Offset: " + received.getOffset());
        System.out.println("Headers: " + received.getHeaders());
    }
}
```

### Custom Configuration

```java
KafkaClientConfig customConfig = new KafkaClientConfig.Builder()
    .bootstrapServers("kafnlprfgscm.us.dell.com:9094")
    .topic("GOBIG.B2BFLOWMAC.E2EV.DIT")
    .groupId("custom-test-group")
    .clientId("custom-test-client")
    .securityProtocol("SASL_SSL")
    .saslMechanism("PLAIN")
    .saslJaasConfig("org.apache.kafka.common.security.plain.PlainLoginModule required")
    .username("svc_nphipkafkagscm")
    .password("ji?zVD58h2WkfxmY0AL+N*PC")
    .sslTruststoreLocation("/etc/ssl/certs/kafka-truststore/kafka.client.truststore.jks")
    .sslTruststorePassword("good8008")
    .sslTruststoreType("JKS")
    .protocols("TLSv1.2")
    .autoOffsetReset("earliest")
    .maxPollRecords(100)
    .acks(1)
    .retries(3)
    .enableIdempotence(true)
    .build();
```

### Message Listener

```java
// Start continuous message listener
kafkaClient.startListener(message -> {
    System.out.println("Received: " + message.getContent());
    System.out.println("Headers: " + message.getHeaders());
    // Process message here
});

// Send some test messages
kafkaClient.sendMessage("Test message 1");
kafkaClient.sendMessage("Test message 2");

// Stop listener when done
kafkaClient.stopListener();
```

### Send Message with Headers

```java
// Send message with custom headers
Map<String, String> headers = new HashMap<>();
headers.put("messageId", "test-123");
headers.put("source", "TestApplication");
headers.put("timestamp", String.valueOf(System.currentTimeMillis()));

kafkaClient.sendMessage("Message with headers".getBytes(), headers);
```

### Wait for Specific Number of Messages

```java
// Wait for exactly 3 messages with 30 second timeout
List<KafkaClient.KafkaMessage> messages = kafkaClient.waitForMessages(3, 30000, message -> {
    System.out.println("Processing: " + message.getContent());
    // Handle each message
});

System.out.println("Received " + messages.size() + " messages");
```

## Testing Kafka Adapter and Handler Components

### Testing Handler (Message Sending)

```java
// Test the same functionality as DynamicKafkaOutputHandler
KafkaClientConfig producerConfig = KafkaClientConfig.createProducerConfig();
KafkaClient client = new KafkaClient(null, producerConfig);

// Test different message types
client.sendMessage("Text message");
client.sendMessage("JSON message".getBytes());

// Test with different configurations
producerConfig.setAcks(1);
producerConfig.setRetries(5);
client.sendMessage("Reliable message");
```

### Testing Adapter (Message Receiving)

```java
// Test the same functionality as DynamicKafkaInputAdapter
KafkaClientConfig consumerConfig = KafkaClientConfig.createConsumerConfig();
KafkaClient client = new KafkaClient(consumerConfig, null);

// Test single message receive
KafkaClient.KafkaMessage message = client.receiveMessage(10000);

// Test continuous listening
client.startListener(receivedMessage -> {
    // Validate message format, headers, etc.
    validateMessage(receivedMessage);
});
```

## Integration Testing Scenarios

### End-to-End Testing

```java
public void testEndToEndFlow() throws Exception {
    KafkaClientConfig consumerConfig = KafkaClientConfig.createConsumerConfig();
    KafkaClientConfig producerConfig = KafkaClientConfig.createProducerConfig();
    
    try (KafkaClient client = new KafkaClient(consumerConfig, producerConfig)) {
        
        // 1. Send test message
        String testMessage = "E2E Test - " + System.currentTimeMillis();
        client.sendMessage(testMessage);
        
        // 2. Receive and validate
        KafkaClient.KafkaMessage received = client.receiveMessage(10000);
        assert received.getContent().contains("E2E Test");
        
        // 3. Test listener functionality
        CountDownLatch latch = new CountDownLatch(1);
        client.startListener(message -> {
            System.out.println("Listener received: " + message);
            latch.countDown();
        });
        
        client.sendMessage("Listener test");
        latch.await(10, TimeUnit.SECONDS);
        client.stopListener();
    }
}
```

### Performance Testing

```java
public void testPerformance() throws Exception {
    try (KafkaClient client = new KafkaClient(consumerConfig, producerConfig)) {
        
        int messageCount = 1000;
        long startTime = System.currentTimeMillis();
        
        // Send messages
        for (int i = 0; i < messageCount; i++) {
            client.sendMessage("Performance test message " + i);
        }
        
        long sendTime = System.currentTimeMillis() - startTime;
        System.out.println("Sent " + messageCount + " messages in " + sendTime + "ms");
        
        // Receive messages
        startTime = System.currentTimeMillis();
        AtomicInteger receivedCount = new AtomicInteger(0);
        
        List<KafkaClient.KafkaMessage> messages = client.waitForMessages(messageCount, 60000, message -> {
            receivedCount.incrementAndGet();
        });
        
        long receiveTime = System.currentTimeMillis() - startTime;
        System.out.println("Received " + messages.size() + " messages in " + receiveTime + "ms");
    }
}
```

## Configuration Compatibility

### Idempotence and Acknowledgments

The KafkaClient automatically handles Kafka's idempotence requirements:

- **When `enableIdempotence=true`**: The client automatically sets `acks=all` regardless of the configured acks value, as required by Kafka for idempotent producers
- **When `enableIdempotence=false`**: The client uses the configured acks value or defaults to `all`

```java
// This configuration will automatically use acks=all
KafkaClientConfig config = new KafkaClientConfig.Builder()
    .acks(1)  // This will be overridden to "all"
    .enableIdempotence(true)  // Forces acks=all
    .build();
```

This ensures compatibility and prevents `ConfigException: Must set acks to all in order to use the idempotent producer`.

## Error Handling

The KafkaClient provides comprehensive error handling:

```java
try {
    kafkaClient.sendMessage("Test message");
} catch (Exception e) {
    System.err.println("Kafka Error: " + e.getMessage());
    // Handle specific Kafka exceptions
    if (e.getCause() instanceof org.apache.kafka.common.errors.TimeoutException) {
        System.err.println("Kafka timeout occurred");
    }
}
```

## Best Practices

1. **Resource Management**: Always use try-with-resources or explicitly call `close()`
2. **Timeout Configuration**: Set appropriate timeouts for receive operations
3. **Error Handling**: Implement proper exception handling for Kafka operations
4. **Configuration Validation**: Validate connection parameters before use
5. **Testing Isolation**: Use separate topics or consumer groups for different test scenarios

## Troubleshooting

### Common Issues

1. **Connection Failures**: Verify bootstrap servers and network connectivity
2. **Authentication Errors**: Check username/password and SASL configuration
3. **SSL/TLS Issues**: Verify truststore location, password, and certificate configuration
4. **Topic Access**: Ensure topic exists and user has appropriate permissions
5. **Consumer Group Issues**: Use unique group IDs for different test scenarios

### Debug Logging

Enable debug logging to troubleshoot issues:

```java
// Add to logback.xml or application.properties
<logger name="com.dell.it.hip.client" level="DEBUG"/>
<logger name="org.apache.kafka" level="DEBUG"/>
```

## Compatibility

The KafkaClient is designed to be compatible with:
- Apache Kafka 2.8+
- Java 11+
- Spring Framework 5.x+
- HIP Services Framework

## Dependencies

Required dependencies are already included in the HIP services project:
- Apache Kafka Clients
- SLF4J Logging
