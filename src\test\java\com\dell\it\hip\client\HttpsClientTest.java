package com.dell.it.hip.client;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive unit test class for HttpsClient that validates all HTTPS operations
 * including message sending (handler testing), message receiving (adapter testing),
 * authentication, OAuth2, compression, and error scenarios.
 * 
 * This test class follows the same patterns and structure as existing client tests
 * (SftpClientTest, NasClientTest, MQClientTest, etc.) in the HIP services framework.
 */
class HttpsClientTest {

    private static final Logger logger = LoggerFactory.getLogger(HttpsClientTest.class);
    
    private HttpsClientConfig consumerConfig;
    private HttpsClientConfig producerConfig;
    private HttpsClient httpsClient;
    
    // Test data
    private static final String TEST_MESSAGE = "Test HTTPS message - ";
    private static final String TEST_JSON_MESSAGE = "{\"message\": \"Test JSON content\", \"timestamp\": %d}";
    private static final String TEST_XML_MESSAGE = "<?xml version=\"1.0\"?><test><message>Test XML content</message><timestamp>%d</timestamp></test>";
    
    @BeforeEach
    void setUp() {
        // Create configurations using httpbin.org for testing
        consumerConfig = HttpsClientConfig.createConsumerConfig();
        producerConfig = HttpsClientConfig.createProducerConfig();
        
        // Create HttpsClient instance
        httpsClient = new HttpsClient(consumerConfig, producerConfig);
        
        logger.info("HttpsClient test setup completed");
    }
    
    @AfterEach
    void tearDown() {
        if (httpsClient != null) {
            httpsClient.close();
        }
        logger.info("HttpsClient test cleanup completed");
    }
    
    @Test
    void testSendMessage() throws Exception {
        // Test sending a simple text message (handler functionality)
        String testMessage = TEST_MESSAGE + System.currentTimeMillis();
        
        HttpsClient.HttpsResponse response = assertDoesNotThrow(() -> {
            return httpsClient.sendMessage(testMessage);
        });
        
        assertNotNull(response, "Response should not be null");
        assertTrue(response.isSuccessful(), "Response should be successful");
        assertEquals(200, response.getStatusCode(), "Status code should be 200");
        assertNotNull(response.getBody(), "Response body should not be null");
        
        logger.info("Successfully sent HTTPS message: {}, response: {}", testMessage, response.getStatusCode());
    }
    
    @Test
    void testSendByteArrayMessage() throws Exception {
        // Test sending a byte array message (handler functionality)
        String testMessage = String.format(TEST_JSON_MESSAGE, System.currentTimeMillis());
        byte[] testPayload = testMessage.getBytes("UTF-8");
        
        HttpsClient.HttpsResponse response = assertDoesNotThrow(() -> {
            return httpsClient.sendMessage(testPayload);
        });
        
        assertNotNull(response, "Response should not be null");
        assertTrue(response.isSuccessful(), "Response should be successful");
        assertEquals(200, response.getStatusCode(), "Status code should be 200");
        
        logger.info("Successfully sent HTTPS byte array message with {} bytes, response: {}", 
                   testPayload.length, response.getStatusCode());
    }
    
    @Test
    void testSendMessageWithCustomHeaders() throws Exception {
        // Test sending a message with custom headers
        String testMessage = String.format(TEST_XML_MESSAGE, System.currentTimeMillis());
        Map<String, String> customHeaders = new HashMap<>();
        customHeaders.put("X-Custom-Header", "test-value");
        customHeaders.put("X-Request-ID", "test-" + System.currentTimeMillis());
        
        HttpsClient.HttpsResponse response = assertDoesNotThrow(() -> {
            return httpsClient.sendMessage(testMessage.getBytes("UTF-8"), customHeaders);
        });
        
        assertNotNull(response, "Response should not be null");
        assertTrue(response.isSuccessful(), "Response should be successful");
        
        logger.info("Successfully sent HTTPS message with custom headers, response: {}", response.getStatusCode());
    }
    
    @Test
    void testSendMessageWithApiKey() throws Exception {
        // Test sending a message with API key authentication
        HttpsClientConfig apiKeyConfig = HttpsClientConfig.createApiKeyConfig(
            "https://httpbin.org/post", 
            "X-API-Key", 
            "test-api-key-123"
        );
        
        try (HttpsClient apiKeyClient = new HttpsClient(consumerConfig, apiKeyConfig)) {
            String testMessage = TEST_MESSAGE + "with API key";
            
            HttpsClient.HttpsResponse response = assertDoesNotThrow(() -> {
                return apiKeyClient.sendMessage(testMessage);
            });
            
            assertNotNull(response, "Response should not be null");
            assertTrue(response.isSuccessful(), "Response should be successful");
            
            logger.info("Successfully sent HTTPS message with API key authentication");
        }
    }
    
    @Test
    void testReceiveMessage() throws Exception {
        // Test receiving a message (adapter functionality)
        String requestBody = String.format(TEST_JSON_MESSAGE, System.currentTimeMillis());
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("X-Source", "test-client");
        
        HttpsClient.HttpsMessage receivedMessage = assertDoesNotThrow(() -> {
            return httpsClient.receiveMessage(requestBody, headers);
        });
        
        assertNotNull(receivedMessage, "Should have received a message");
        assertEquals(requestBody, receivedMessage.getContent(), "Content should match");
        assertNotNull(receivedMessage.getHeaders(), "Headers should not be null");
        assertTrue(receivedMessage.getHeaders().containsKey("Content-Type"), "Should contain Content-Type header");
        assertTrue(receivedMessage.getTimestamp() > 0, "Timestamp should be set");
        
        logger.info("Successfully received HTTPS message: {}", receivedMessage.getContent());
    }
    
    @Test
    void testReceiveMessageWithAuthentication() throws Exception {
        // Test receiving a message with API key authentication
        HttpsClientConfig authConsumerConfig = new HttpsClientConfig.Builder()
            .endpointUrl("https://httpbin.org/post")
            .apiKeyHeader("X-API-Key")
            .apiKeyValue("test-api-key-123")
            .build();
        
        try (HttpsClient authClient = new HttpsClient(authConsumerConfig, producerConfig)) {
            String requestBody = TEST_MESSAGE + "with auth";
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("X-API-Key", "test-api-key-123"); // Correct API key
            
            HttpsClient.HttpsMessage receivedMessage = assertDoesNotThrow(() -> {
                return authClient.receiveMessage(requestBody, headers);
            });
            
            assertNotNull(receivedMessage, "Should have received authenticated message");
            assertEquals(requestBody, receivedMessage.getContent(), "Content should match");
            
            logger.info("Successfully received authenticated HTTPS message");
        }
    }
    
    @Test
    void testReceiveMessageAuthenticationFailure() {
        // Test authentication failure
        HttpsClientConfig authConsumerConfig = new HttpsClientConfig.Builder()
            .endpointUrl("https://httpbin.org/post")
            .apiKeyHeader("X-API-Key")
            .apiKeyValue("correct-api-key")
            .build();
        
        try (HttpsClient authClient = new HttpsClient(authConsumerConfig, producerConfig)) {
            String requestBody = TEST_MESSAGE + "auth failure test";
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");
            headers.put("X-API-Key", "wrong-api-key"); // Wrong API key
            
            assertThrows(SecurityException.class, () -> {
                authClient.receiveMessage(requestBody, headers);
            });
            
            logger.info("Correctly handled authentication failure");
        }
    }
    
    @Test
    void testCompressionHandling() throws Exception {
        // Test compression functionality
        HttpsClientConfig compressedConfig = new HttpsClientConfig.Builder()
            .endpointUrl("https://httpbin.org/post")
            .httpMethod("POST")
            .compressed(true)
            .connectTimeoutMs(10000L)
            .readTimeoutMs(30000L)
            .build();
        
        try (HttpsClient compressedClient = new HttpsClient(consumerConfig, compressedConfig)) {
            String testMessage = TEST_MESSAGE + "for compression test - this is a longer message to test compression functionality";
            
            HttpsClient.HttpsResponse response = assertDoesNotThrow(() -> {
                return compressedClient.sendMessage(testMessage);
            });
            
            assertNotNull(response, "Response should not be null");
            assertTrue(response.isSuccessful(), "Compressed request should be successful");
            
            logger.info("Successfully tested compression with response: {}", response.getStatusCode());
        }
    }
    
    @Test
    void testRetryMechanism() throws Exception {
        // Test retry mechanism with invalid URL
        HttpsClientConfig retryConfig = new HttpsClientConfig.Builder()
            .endpointUrl("https://httpbin.org/status/500") // This will return 500 error
            .httpMethod("POST")
            .retryAttempts(3)
            .retryBackoffMs(100L) // Short backoff for testing
            .connectTimeoutMs(5000L)
            .readTimeoutMs(5000L)
            .build();
        
        try (HttpsClient retryClient = new HttpsClient(consumerConfig, retryConfig)) {
            String testMessage = TEST_MESSAGE + "retry test";
            
            HttpsClient.HttpsResponse response = assertDoesNotThrow(() -> {
                return retryClient.sendMessage(testMessage);
            });
            
            // Should get a response even if it's an error status
            assertNotNull(response, "Response should not be null");
            assertEquals(500, response.getStatusCode(), "Should receive 500 status code");
            
            logger.info("Successfully tested retry mechanism with status: {}", response.getStatusCode());
        }
    }
    
    @Test
    void testConnectionManagement() throws Exception {
        // Test connection lifecycle management
        
        // Test that client can handle multiple operations
        String message1 = TEST_MESSAGE + "conn1-" + System.currentTimeMillis();
        String message2 = TEST_MESSAGE + "conn2-" + System.currentTimeMillis();
        
        HttpsClient.HttpsResponse response1 = assertDoesNotThrow(() -> {
            return httpsClient.sendMessage(message1);
        });
        
        HttpsClient.HttpsResponse response2 = assertDoesNotThrow(() -> {
            return httpsClient.sendMessage(message2);
        });
        
        assertTrue(response1.isSuccessful(), "First request should be successful");
        assertTrue(response2.isSuccessful(), "Second request should be successful");
        
        logger.info("Successfully tested connection management");
    }
    
    @Test
    void testMessageListener() throws Exception {
        AtomicInteger messageCount = new AtomicInteger(0);
        CountDownLatch latch = new CountDownLatch(2);
        
        // Start listener
        httpsClient.startListener(message -> {
            logger.info("Received HTTPS message via listener: {}", message.getContent());
            messageCount.incrementAndGet();
            latch.countDown();
        });
        
        // Wait for simulated messages to be processed (with timeout)
        boolean received = latch.await(15, TimeUnit.SECONDS);
        httpsClient.stopListener();
        
        assertTrue(received, "Should have processed simulated messages within timeout");
        assertTrue(messageCount.get() >= 2, "Should have processed at least 2 simulated messages");
        
        logger.info("Successfully processed {} HTTPS messages via listener", messageCount.get());
    }

    @Test
    void testWaitForMessages() throws Exception {
        AtomicInteger processedCount = new AtomicInteger(0);

        // Wait for simulated messages to be processed
        List<HttpsClient.HttpsMessage> messages = httpsClient.waitForMessages(2, 15000, message -> {
            logger.info("Processed HTTPS message: {}", message.getContent());
            processedCount.incrementAndGet();
        });

        assertTrue(messages.size() >= 2, "Should have received at least 2 messages");
        assertTrue(processedCount.get() >= 2, "Should have processed at least 2 messages");

        logger.info("Successfully waited for and processed {} HTTPS messages", processedCount.get());
    }

    @Test
    void testCustomConfiguration() throws Exception {
        // Test with custom HTTPS configuration
        HttpsClientConfig customConsumerConfig = new HttpsClientConfig.Builder()
            .endpointUrl("https://httpbin.org/post")
            .maxConcurrency(50)
            .maxRequestSizeBytes(1024 * 1024) // 1MB
            .requestTimeoutMs(15000)
            .headersToExtract(List.of("Content-Type", "X-Custom-Header"))
            .build();

        HttpsClientConfig customProducerConfig = new HttpsClientConfig.Builder()
            .endpointUrl("https://httpbin.org/post")
            .httpMethod("POST")
            .connectTimeoutMs(15000L)
            .readTimeoutMs(45000L)
            .maxInMemorySize(1024 * 1024)
            .retryAttempts(2)
            .retryBackoffMs(500L)
            .compressed(false)
            .build();

        try (HttpsClient customClient = new HttpsClient(customConsumerConfig, customProducerConfig)) {
            String testMessage = "Custom HTTPS config test - " + System.currentTimeMillis();

            HttpsClient.HttpsResponse response = assertDoesNotThrow(() -> {
                return customClient.sendMessage(testMessage);
            });

            assertTrue(response.isSuccessful(), "Custom config request should be successful");

            logger.info("Successfully tested custom HTTPS configuration");
        }
    }

    @Test
    void testErrorHandling() {
        // Test with invalid URL to verify error handling
        HttpsClientConfig invalidConfig = new HttpsClientConfig.Builder()
            .endpointUrl("https://invalid.nonexistent.domain.example.com/test")
            .httpMethod("POST")
            .connectTimeoutMs(2000L) // Short timeout for faster test
            .readTimeoutMs(2000L)
            .retryAttempts(1) // Single attempt for faster test
            .build();

        try (HttpsClient invalidClient = new HttpsClient(consumerConfig, invalidConfig)) {
            // This should throw an exception due to invalid URL
            assertThrows(Exception.class, () -> {
                invalidClient.sendMessage("This should fail");
            });

            logger.info("Correctly handled invalid HTTPS configuration");
        }
    }

    @Test
    void testLargeMessageHandling() throws Exception {
        // Test handling of larger messages
        StringBuilder largeContent = new StringBuilder();

        // Create a larger test content (about 10KB)
        for (int i = 0; i < 1000; i++) {
            largeContent.append("This is line ").append(i).append(" of the large HTTPS test message content.\n");
        }

        String testMessage = largeContent.toString();

        HttpsClient.HttpsResponse response = assertDoesNotThrow(() -> {
            return httpsClient.sendMessage(testMessage);
        });

        assertNotNull(response, "Response should not be null");
        assertTrue(response.isSuccessful(), "Large message request should be successful");

        logger.info("Successfully handled large HTTPS message ({} bytes), response: {}",
                   testMessage.getBytes().length, response.getStatusCode());
    }

    @Test
    void testSpecialCharacterHandling() throws Exception {
        // Test handling of messages with special characters
        String specialContent = "Special chars: àáâãäåæçèéêëìíîïðñòóôõö÷øùúûüýþÿ\n" +
                               "Symbols: !@#$%^&*()_+-=[]{}|;':\",./<>?\n" +
                               "Unicode: 你好世界 🌍 🚀 ⭐\n" +
                               "JSON: {\"message\": \"test\", \"value\": 123}\n";

        HttpsClient.HttpsResponse response = assertDoesNotThrow(() -> {
            return httpsClient.sendMessage(specialContent);
        });

        assertNotNull(response, "Response should not be null");
        assertTrue(response.isSuccessful(), "Special character request should be successful");

        logger.info("Successfully handled special characters in HTTPS message");
    }

    @Test
    void testConcurrentOperations() throws Exception {
        // Test concurrent HTTPS operations
        CountDownLatch latch = new CountDownLatch(3);
        AtomicInteger successCount = new AtomicInteger(0);

        // Start multiple threads sending requests concurrently
        for (int i = 1; i <= 3; i++) {
            final int threadNum = i;
            new Thread(() -> {
                try {
                    String message = "HTTPS concurrent test content from thread " + threadNum;

                    HttpsClient.HttpsResponse response = httpsClient.sendMessage(message);
                    if (response.isSuccessful()) {
                        successCount.incrementAndGet();
                    }
                    logger.info("Thread {} completed HTTPS request with status: {}", threadNum, response.getStatusCode());
                } catch (Exception e) {
                    logger.error("Thread {} failed HTTPS request: {}", threadNum, e.getMessage());
                } finally {
                    latch.countDown();
                }
            }).start();
        }

        // Wait for all threads to complete
        boolean completed = latch.await(30, TimeUnit.SECONDS);

        assertTrue(completed, "All concurrent HTTPS operations should complete within timeout");
        assertTrue(successCount.get() >= 1, "At least one concurrent HTTPS operation should succeed");

        logger.info("Successfully tested concurrent HTTPS operations - {} out of 3 succeeded", successCount.get());
    }

    @Test
    void testHttpMethods() throws Exception {
        // Test different HTTP methods
        String[] methods = {"POST", "PUT", "PATCH"};

        for (String method : methods) {
            HttpsClientConfig methodConfig = new HttpsClientConfig.Builder()
                .endpointUrl("https://httpbin.org/" + method.toLowerCase())
                .httpMethod(method)
                .connectTimeoutMs(10000L)
                .readTimeoutMs(30000L)
                .build();

            try (HttpsClient methodClient = new HttpsClient(consumerConfig, methodConfig)) {
                String testMessage = String.format("{\"method\": \"%s\", \"timestamp\": %d}", method, System.currentTimeMillis());

                HttpsClient.HttpsResponse response = assertDoesNotThrow(() -> {
                    return methodClient.sendMessage(testMessage);
                });

                assertNotNull(response, "Response should not be null for " + method);
                assertTrue(response.isSuccessful(), method + " request should be successful");

                logger.info("Successfully tested {} method with status: {}", method, response.getStatusCode());
            }
        }
    }

    @Test
    void testRequestSizeLimit() {
        // Test request size limit enforcement
        HttpsClientConfig limitConfig = new HttpsClientConfig.Builder()
            .endpointUrl("https://httpbin.org/post")
            .maxRequestSizeBytes(100) // Very small limit
            .build();

        try (HttpsClient limitClient = new HttpsClient(limitConfig, producerConfig)) {
            String largeMessage = "This message is definitely longer than 100 bytes and should trigger the size limit check in the receive method";
            Map<String, String> headers = new HashMap<>();
            headers.put("Content-Type", "application/json");

            assertThrows(IllegalArgumentException.class, () -> {
                limitClient.receiveMessage(largeMessage, headers);
            });

            logger.info("Correctly enforced request size limit");
        }
    }

    /**
     * Manual test method for interactive testing - not run automatically
     * Run this method manually to test against actual HTTPS endpoints
     */
    public static void main(String[] args) {
        Logger mainLogger = LoggerFactory.getLogger("HttpsClientManualTest");

        try {
            mainLogger.info("Starting manual HTTPS client test...");

            HttpsClientConfig consumerConfig = HttpsClientConfig.createConsumerConfig();
            HttpsClientConfig producerConfig = HttpsClientConfig.createProducerConfig();

            try (HttpsClient client = new HttpsClient(consumerConfig, producerConfig)) {

                // Test 1: Send a simple message
                mainLogger.info("Test 1: Sending HTTPS message...");
                String testMessage = "Manual HTTPS test message - " + System.currentTimeMillis();
                HttpsClient.HttpsResponse response = client.sendMessage(testMessage);
                mainLogger.info("HTTPS message sent successfully, status: {}, body: {}",
                               response.getStatusCode(), response.getBody());

                // Test 2: Send with custom headers
                mainLogger.info("Test 2: Sending HTTPS message with custom headers...");
                Map<String, String> headers = new HashMap<>();
                headers.put("X-Test-Header", "manual-test");
                headers.put("X-Timestamp", String.valueOf(System.currentTimeMillis()));

                HttpsClient.HttpsResponse response2 = client.sendMessage(testMessage.getBytes(), headers);
                mainLogger.info("HTTPS message with headers sent successfully, status: {}", response2.getStatusCode());

                // Test 3: Test authentication simulation
                mainLogger.info("Test 3: Testing authentication simulation...");
                Map<String, String> authHeaders = new HashMap<>();
                authHeaders.put("Content-Type", "application/json");
                authHeaders.put("Authorization", "Bearer test-token");

                HttpsClient.HttpsMessage received = client.receiveMessage(testMessage, authHeaders);
                mainLogger.info("Received HTTPS message: {}", received.getContent());

                // Test 4: Start listener for a short time
                mainLogger.info("Test 4: Starting HTTPS listener for 10 seconds...");
                client.startListener(message -> {
                    mainLogger.info("HTTPS listener received message: {}", message.getContent());
                });

                Thread.sleep(10000);
                client.stopListener();
                mainLogger.info("HTTPS listener stopped");

            }

            mainLogger.info("Manual test completed successfully");

        } catch (Exception e) {
            mainLogger.error("Manual test failed: {}", e.getMessage(), e);
        }
    }
}
