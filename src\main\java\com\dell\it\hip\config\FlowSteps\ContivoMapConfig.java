package com.dell.it.hip.config.FlowSteps;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;
@Data
public class ContivoMapConfig extends FlowStepConfig {
    private String mapIdentifier;
    private String mapName;
    private String mapClass;
    private String contivoVersion;
    private CrossReferenceData crossReferenceData;
    private String redisMapDataKey;
    private String mapIdentifierVersion;
    
    @JsonProperty("configurationList")
   	private List<DocTypeConfig> docTypeConfigs = new ArrayList<>();
    
    public List<DocTypeConfig> getDocTypeConfigs() {
		return docTypeConfigs;
	}
	public void setDocTypeConfigs(List<DocTypeConfig> docTypeConfigs) {
		this.docTypeConfigs = docTypeConfigs;
	}

	public DocTypeConfig findBestConfig(String docTypeName, String docTypeVersion, String dataFormat) {
		// First: try to find exact match
		for (DocTypeConfig config : docTypeConfigs) {
			if (config.getName().equalsIgnoreCase(docTypeName)
					&& config.getVersion().equalsIgnoreCase(docTypeVersion)
					&& (dataFormat == null || dataFormat.equalsIgnoreCase(config.getDataFormat()))) {
				return config;
			}
		}
		// Second: try to find by docType (ignore version)
		for (DocTypeConfig config : docTypeConfigs) {
			if (config.getName().equalsIgnoreCase(docTypeName)
					&& (dataFormat == null || dataFormat.equalsIgnoreCase(config.getDataFormat()))) {
				return config;
			}
		}
		// Fallback: return default if present
		return null;
	}

    // Getters and Setters
    // toString, equals, hashCode (optional)
}
