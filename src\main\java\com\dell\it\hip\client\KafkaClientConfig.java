package com.dell.it.hip.client;

import java.util.Map;

/**
 * Configuration class for KafkaClient that mirrors the property structure
 * used in DynamicKafkaAdapterConfig and DynamicKafkaHandlerConfig.
 * 
 * This class can be used for both consumer (adapter testing) and producer (handler testing)
 * configurations by using the appropriate property prefixes.
 */
public class KafkaClientConfig {
    
    // Core Kafka connection properties
    private String bootstrapServers;
    private String topic;
    private String clientId;
    private String groupId; // Consumer only
    
    // Authentication
    private String authenticationType;
    private String username;
    private String password;
    
    // Security properties
    private String securityProtocol;
    private String saslMechanism;
    private String saslJaasConfig;
    private String protocols;
    
    // SSL/TLS properties
    private String sslTruststoreLocation;
    private String sslTruststorePassword;
    private String sslTruststoreType;
    private String sslKeystoreLocation;
    private String sslKeystorePassword;
    private String sslKeyPassword;
    
    // Consumer properties
    private String autoOffsetReset;
    private Integer maxPollRecords;
    private Integer fetchMinBytes;
    private Integer fetchMaxBytes;
    private Integer maxPartitionFetchBytes;
    private Integer sessionTimeoutMs;
    private Integer heartbeatIntervalMs;
    private Integer pollTimeoutMs;
    private Boolean enableAutoCommit;
    private Integer autoCommitIntervalMs;
    private Integer maxPollIntervalMs;
    private Integer requestTimeoutMs;
    private Integer retries;
    private Integer retryBackoffMs;
    private String isolationLevel;
    private Boolean allowAutoCreateTopics;
    private Integer concurrency;
    
    // Producer properties
    private Integer acks;
    private Integer batchSize;
    private Integer lingerMs;
    private Integer bufferMemory;
    private Integer maxInFlightRequestsPerConnection;
    private Integer deliveryTimeoutMs;
    private Boolean enableIdempotence;
    private Integer compressionType;
    private Boolean gzipEnabled;
    
    // Application-level
    private Boolean compressed;
    private Map<String, Object> parameters;
    
    // Default constructor
    public KafkaClientConfig() {}
    
    // Builder pattern constructor
    private KafkaClientConfig(Builder builder) {
        this.bootstrapServers = builder.bootstrapServers;
        this.topic = builder.topic;
        this.clientId = builder.clientId;
        this.groupId = builder.groupId;
        this.authenticationType = builder.authenticationType;
        this.username = builder.username;
        this.password = builder.password;
        this.securityProtocol = builder.securityProtocol;
        this.saslMechanism = builder.saslMechanism;
        this.saslJaasConfig = builder.saslJaasConfig;
        this.protocols = builder.protocols;
        this.sslTruststoreLocation = builder.sslTruststoreLocation;
        this.sslTruststorePassword = builder.sslTruststorePassword;
        this.sslTruststoreType = builder.sslTruststoreType;
        this.sslKeystoreLocation = builder.sslKeystoreLocation;
        this.sslKeystorePassword = builder.sslKeystorePassword;
        this.sslKeyPassword = builder.sslKeyPassword;
        this.autoOffsetReset = builder.autoOffsetReset;
        this.maxPollRecords = builder.maxPollRecords;
        this.fetchMinBytes = builder.fetchMinBytes;
        this.fetchMaxBytes = builder.fetchMaxBytes;
        this.maxPartitionFetchBytes = builder.maxPartitionFetchBytes;
        this.sessionTimeoutMs = builder.sessionTimeoutMs;
        this.heartbeatIntervalMs = builder.heartbeatIntervalMs;
        this.pollTimeoutMs = builder.pollTimeoutMs;
        this.enableAutoCommit = builder.enableAutoCommit;
        this.autoCommitIntervalMs = builder.autoCommitIntervalMs;
        this.maxPollIntervalMs = builder.maxPollIntervalMs;
        this.requestTimeoutMs = builder.requestTimeoutMs;
        this.retries = builder.retries;
        this.retryBackoffMs = builder.retryBackoffMs;
        this.isolationLevel = builder.isolationLevel;
        this.allowAutoCreateTopics = builder.allowAutoCreateTopics;
        this.concurrency = builder.concurrency;
        this.acks = builder.acks;
        this.batchSize = builder.batchSize;
        this.lingerMs = builder.lingerMs;
        this.bufferMemory = builder.bufferMemory;
        this.maxInFlightRequestsPerConnection = builder.maxInFlightRequestsPerConnection;
        this.deliveryTimeoutMs = builder.deliveryTimeoutMs;
        this.enableIdempotence = builder.enableIdempotence;
        this.compressionType = builder.compressionType;
        this.gzipEnabled = builder.gzipEnabled;
        this.compressed = builder.compressed;
        this.parameters = builder.parameters;
    }
    
    /**
     * Create a consumer configuration with the provided test properties
     */
    public static KafkaClientConfig createConsumerConfig() {
        return new Builder()
            .bootstrapServers("kafnlprfgscm.us.dell.com:9094")
            .topic("GOBIG.B2BFLOWMAC.E2EV.DIT")
            .groupId("hip-test-consumer-group")
            .clientId("hip-test-consumer")
            .securityProtocol("SASL_SSL")
            .saslMechanism("PLAIN")
            .saslJaasConfig("org.apache.kafka.common.security.plain.PlainLoginModule required")
            .username("svc_nphipkafkagscm")
            .password("ji?zVD58h2WkfxmY0AL+N*PC")
            .sslTruststoreLocation("/etc/ssl/certs/kafka-truststore/kafka.client.truststore.jks")
            .sslTruststorePassword("good8008")
            .sslTruststoreType("JKS")
            .protocols("TLSv1.2")
            .autoOffsetReset("latest")
            .enableAutoCommit(true)
            .maxPollRecords(500)
            .build();
    }
    
    /**
     * Create a producer configuration with the provided test properties
     */
    public static KafkaClientConfig createProducerConfig() {
        return new Builder()
            .bootstrapServers("kafnlprfgscm.us.dell.com:9094")
            .topic("GOBIG.B2BFLOWMAC.E2EV.DIT")
            .clientId("hip-test-producer")
            .securityProtocol("SASL_SSL")
            .saslMechanism("PLAIN")
            .saslJaasConfig("org.apache.kafka.common.security.plain.PlainLoginModule required")
            .username("svc_nphipkafkagscm")
            .password("ji?zVD58h2WkfxmY0AL+N*PC")
            .sslTruststoreLocation("/etc/ssl/certs/kafka-truststore/kafka.client.truststore.jks")
            .sslTruststorePassword("good8008")
            .sslTruststoreType("JKS")
            .protocols("TLSv1.2")
            .acks(1)
            .retries(3)
            .enableIdempotence(false)
            .build();
    }
    
    // Getters and setters
    public String getBootstrapServers() { return bootstrapServers; }
    public void setBootstrapServers(String bootstrapServers) { this.bootstrapServers = bootstrapServers; }
    
    public String getTopic() { return topic; }
    public void setTopic(String topic) { this.topic = topic; }
    
    public String getClientId() { return clientId; }
    public void setClientId(String clientId) { this.clientId = clientId; }
    
    public String getGroupId() { return groupId; }
    public void setGroupId(String groupId) { this.groupId = groupId; }
    
    public String getAuthenticationType() { return authenticationType; }
    public void setAuthenticationType(String authenticationType) { this.authenticationType = authenticationType; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
    
    public String getSecurityProtocol() { return securityProtocol; }
    public void setSecurityProtocol(String securityProtocol) { this.securityProtocol = securityProtocol; }
    
    public String getSaslMechanism() { return saslMechanism; }
    public void setSaslMechanism(String saslMechanism) { this.saslMechanism = saslMechanism; }
    
    public String getSaslJaasConfig() { return saslJaasConfig; }
    public void setSaslJaasConfig(String saslJaasConfig) { this.saslJaasConfig = saslJaasConfig; }
    
    public String getProtocols() { return protocols; }
    public void setProtocols(String protocols) { this.protocols = protocols; }
    
    public String getSslTruststoreLocation() { return sslTruststoreLocation; }
    public void setSslTruststoreLocation(String sslTruststoreLocation) { this.sslTruststoreLocation = sslTruststoreLocation; }
    
    public String getSslTruststorePassword() { return sslTruststorePassword; }
    public void setSslTruststorePassword(String sslTruststorePassword) { this.sslTruststorePassword = sslTruststorePassword; }
    
    public String getSslTruststoreType() { return sslTruststoreType; }
    public void setSslTruststoreType(String sslTruststoreType) { this.sslTruststoreType = sslTruststoreType; }
    
    public String getSslKeystoreLocation() { return sslKeystoreLocation; }
    public void setSslKeystoreLocation(String sslKeystoreLocation) { this.sslKeystoreLocation = sslKeystoreLocation; }
    
    public String getSslKeystorePassword() { return sslKeystorePassword; }
    public void setSslKeystorePassword(String sslKeystorePassword) { this.sslKeystorePassword = sslKeystorePassword; }
    
    public String getSslKeyPassword() { return sslKeyPassword; }
    public void setSslKeyPassword(String sslKeyPassword) { this.sslKeyPassword = sslKeyPassword; }
    
    public String getAutoOffsetReset() { return autoOffsetReset; }
    public void setAutoOffsetReset(String autoOffsetReset) { this.autoOffsetReset = autoOffsetReset; }
    
    public Integer getMaxPollRecords() { return maxPollRecords; }
    public void setMaxPollRecords(Integer maxPollRecords) { this.maxPollRecords = maxPollRecords; }
    
    public Integer getFetchMinBytes() { return fetchMinBytes; }
    public void setFetchMinBytes(Integer fetchMinBytes) { this.fetchMinBytes = fetchMinBytes; }
    
    public Integer getFetchMaxBytes() { return fetchMaxBytes; }
    public void setFetchMaxBytes(Integer fetchMaxBytes) { this.fetchMaxBytes = fetchMaxBytes; }
    
    public Integer getMaxPartitionFetchBytes() { return maxPartitionFetchBytes; }
    public void setMaxPartitionFetchBytes(Integer maxPartitionFetchBytes) { this.maxPartitionFetchBytes = maxPartitionFetchBytes; }
    
    public Integer getSessionTimeoutMs() { return sessionTimeoutMs; }
    public void setSessionTimeoutMs(Integer sessionTimeoutMs) { this.sessionTimeoutMs = sessionTimeoutMs; }
    
    public Integer getHeartbeatIntervalMs() { return heartbeatIntervalMs; }
    public void setHeartbeatIntervalMs(Integer heartbeatIntervalMs) { this.heartbeatIntervalMs = heartbeatIntervalMs; }
    
    public Integer getPollTimeoutMs() { return pollTimeoutMs; }
    public void setPollTimeoutMs(Integer pollTimeoutMs) { this.pollTimeoutMs = pollTimeoutMs; }
    
    public Boolean getEnableAutoCommit() { return enableAutoCommit; }
    public void setEnableAutoCommit(Boolean enableAutoCommit) { this.enableAutoCommit = enableAutoCommit; }
    
    public Integer getAutoCommitIntervalMs() { return autoCommitIntervalMs; }
    public void setAutoCommitIntervalMs(Integer autoCommitIntervalMs) { this.autoCommitIntervalMs = autoCommitIntervalMs; }
    
    public Integer getMaxPollIntervalMs() { return maxPollIntervalMs; }
    public void setMaxPollIntervalMs(Integer maxPollIntervalMs) { this.maxPollIntervalMs = maxPollIntervalMs; }
    
    public Integer getRequestTimeoutMs() { return requestTimeoutMs; }
    public void setRequestTimeoutMs(Integer requestTimeoutMs) { this.requestTimeoutMs = requestTimeoutMs; }
    
    public Integer getRetries() { return retries; }
    public void setRetries(Integer retries) { this.retries = retries; }
    
    public Integer getRetryBackoffMs() { return retryBackoffMs; }
    public void setRetryBackoffMs(Integer retryBackoffMs) { this.retryBackoffMs = retryBackoffMs; }
    
    public String getIsolationLevel() { return isolationLevel; }
    public void setIsolationLevel(String isolationLevel) { this.isolationLevel = isolationLevel; }
    
    public Boolean getAllowAutoCreateTopics() { return allowAutoCreateTopics; }
    public void setAllowAutoCreateTopics(Boolean allowAutoCreateTopics) { this.allowAutoCreateTopics = allowAutoCreateTopics; }
    
    public Integer getConcurrency() { return concurrency; }
    public void setConcurrency(Integer concurrency) { this.concurrency = concurrency; }
    
    public Integer getAcks() { return acks; }
    public void setAcks(Integer acks) { this.acks = acks; }
    
    public Integer getBatchSize() { return batchSize; }
    public void setBatchSize(Integer batchSize) { this.batchSize = batchSize; }
    
    public Integer getLingerMs() { return lingerMs; }
    public void setLingerMs(Integer lingerMs) { this.lingerMs = lingerMs; }
    
    public Integer getBufferMemory() { return bufferMemory; }
    public void setBufferMemory(Integer bufferMemory) { this.bufferMemory = bufferMemory; }
    
    public Integer getMaxInFlightRequestsPerConnection() { return maxInFlightRequestsPerConnection; }
    public void setMaxInFlightRequestsPerConnection(Integer maxInFlightRequestsPerConnection) { this.maxInFlightRequestsPerConnection = maxInFlightRequestsPerConnection; }
    
    public Integer getDeliveryTimeoutMs() { return deliveryTimeoutMs; }
    public void setDeliveryTimeoutMs(Integer deliveryTimeoutMs) { this.deliveryTimeoutMs = deliveryTimeoutMs; }
    
    public Boolean getEnableIdempotence() { return enableIdempotence; }
    public void setEnableIdempotence(Boolean enableIdempotence) { this.enableIdempotence = enableIdempotence; }
    
    public Integer getCompressionType() { return compressionType; }
    public void setCompressionType(Integer compressionType) { this.compressionType = compressionType; }
    
    public Boolean getGzipEnabled() { return gzipEnabled; }
    public void setGzipEnabled(Boolean gzipEnabled) { this.gzipEnabled = gzipEnabled; }
    
    public Boolean getCompressed() { return compressed; }
    public void setCompressed(Boolean compressed) { this.compressed = compressed; }
    
    public Map<String, Object> getParameters() { return parameters; }
    public void setParameters(Map<String, Object> parameters) { this.parameters = parameters; }

    // Builder pattern for easy configuration
    public static class Builder {
        private String bootstrapServers;
        private String topic;
        private String clientId;
        private String groupId;
        private String authenticationType;
        private String username;
        private String password;
        private String securityProtocol;
        private String saslMechanism;
        private String saslJaasConfig;
        private String protocols;
        private String sslTruststoreLocation;
        private String sslTruststorePassword;
        private String sslTruststoreType;
        private String sslKeystoreLocation;
        private String sslKeystorePassword;
        private String sslKeyPassword;
        private String autoOffsetReset;
        private Integer maxPollRecords;
        private Integer fetchMinBytes;
        private Integer fetchMaxBytes;
        private Integer maxPartitionFetchBytes;
        private Integer sessionTimeoutMs;
        private Integer heartbeatIntervalMs;
        private Integer pollTimeoutMs;
        private Boolean enableAutoCommit;
        private Integer autoCommitIntervalMs;
        private Integer maxPollIntervalMs;
        private Integer requestTimeoutMs;
        private Integer retries;
        private Integer retryBackoffMs;
        private String isolationLevel;
        private Boolean allowAutoCreateTopics;
        private Integer concurrency;
        private Integer acks;
        private Integer batchSize;
        private Integer lingerMs;
        private Integer bufferMemory;
        private Integer maxInFlightRequestsPerConnection;
        private Integer deliveryTimeoutMs;
        private Boolean enableIdempotence;
        private Integer compressionType;
        private Boolean gzipEnabled;
        private Boolean compressed;
        private Map<String, Object> parameters;

        public Builder bootstrapServers(String bootstrapServers) { this.bootstrapServers = bootstrapServers; return this; }
        public Builder topic(String topic) { this.topic = topic; return this; }
        public Builder clientId(String clientId) { this.clientId = clientId; return this; }
        public Builder groupId(String groupId) { this.groupId = groupId; return this; }
        public Builder authenticationType(String authenticationType) { this.authenticationType = authenticationType; return this; }
        public Builder username(String username) { this.username = username; return this; }
        public Builder password(String password) { this.password = password; return this; }
        public Builder securityProtocol(String securityProtocol) { this.securityProtocol = securityProtocol; return this; }
        public Builder saslMechanism(String saslMechanism) { this.saslMechanism = saslMechanism; return this; }
        public Builder saslJaasConfig(String saslJaasConfig) { this.saslJaasConfig = saslJaasConfig; return this; }
        public Builder protocols(String protocols) { this.protocols = protocols; return this; }
        public Builder sslTruststoreLocation(String sslTruststoreLocation) { this.sslTruststoreLocation = sslTruststoreLocation; return this; }
        public Builder sslTruststorePassword(String sslTruststorePassword) { this.sslTruststorePassword = sslTruststorePassword; return this; }
        public Builder sslTruststoreType(String sslTruststoreType) { this.sslTruststoreType = sslTruststoreType; return this; }
        public Builder sslKeystoreLocation(String sslKeystoreLocation) { this.sslKeystoreLocation = sslKeystoreLocation; return this; }
        public Builder sslKeystorePassword(String sslKeystorePassword) { this.sslKeystorePassword = sslKeystorePassword; return this; }
        public Builder sslKeyPassword(String sslKeyPassword) { this.sslKeyPassword = sslKeyPassword; return this; }
        public Builder autoOffsetReset(String autoOffsetReset) { this.autoOffsetReset = autoOffsetReset; return this; }
        public Builder maxPollRecords(Integer maxPollRecords) { this.maxPollRecords = maxPollRecords; return this; }
        public Builder fetchMinBytes(Integer fetchMinBytes) { this.fetchMinBytes = fetchMinBytes; return this; }
        public Builder fetchMaxBytes(Integer fetchMaxBytes) { this.fetchMaxBytes = fetchMaxBytes; return this; }
        public Builder maxPartitionFetchBytes(Integer maxPartitionFetchBytes) { this.maxPartitionFetchBytes = maxPartitionFetchBytes; return this; }
        public Builder sessionTimeoutMs(Integer sessionTimeoutMs) { this.sessionTimeoutMs = sessionTimeoutMs; return this; }
        public Builder heartbeatIntervalMs(Integer heartbeatIntervalMs) { this.heartbeatIntervalMs = heartbeatIntervalMs; return this; }
        public Builder pollTimeoutMs(Integer pollTimeoutMs) { this.pollTimeoutMs = pollTimeoutMs; return this; }
        public Builder enableAutoCommit(Boolean enableAutoCommit) { this.enableAutoCommit = enableAutoCommit; return this; }
        public Builder autoCommitIntervalMs(Integer autoCommitIntervalMs) { this.autoCommitIntervalMs = autoCommitIntervalMs; return this; }
        public Builder maxPollIntervalMs(Integer maxPollIntervalMs) { this.maxPollIntervalMs = maxPollIntervalMs; return this; }
        public Builder requestTimeoutMs(Integer requestTimeoutMs) { this.requestTimeoutMs = requestTimeoutMs; return this; }
        public Builder retries(Integer retries) { this.retries = retries; return this; }
        public Builder retryBackoffMs(Integer retryBackoffMs) { this.retryBackoffMs = retryBackoffMs; return this; }
        public Builder isolationLevel(String isolationLevel) { this.isolationLevel = isolationLevel; return this; }
        public Builder allowAutoCreateTopics(Boolean allowAutoCreateTopics) { this.allowAutoCreateTopics = allowAutoCreateTopics; return this; }
        public Builder concurrency(Integer concurrency) { this.concurrency = concurrency; return this; }
        public Builder acks(Integer acks) { this.acks = acks; return this; }
        public Builder batchSize(Integer batchSize) { this.batchSize = batchSize; return this; }
        public Builder lingerMs(Integer lingerMs) { this.lingerMs = lingerMs; return this; }
        public Builder bufferMemory(Integer bufferMemory) { this.bufferMemory = bufferMemory; return this; }
        public Builder maxInFlightRequestsPerConnection(Integer maxInFlightRequestsPerConnection) { this.maxInFlightRequestsPerConnection = maxInFlightRequestsPerConnection; return this; }
        public Builder deliveryTimeoutMs(Integer deliveryTimeoutMs) { this.deliveryTimeoutMs = deliveryTimeoutMs; return this; }
        public Builder enableIdempotence(Boolean enableIdempotence) { this.enableIdempotence = enableIdempotence; return this; }
        public Builder compressionType(Integer compressionType) { this.compressionType = compressionType; return this; }
        public Builder gzipEnabled(Boolean gzipEnabled) { this.gzipEnabled = gzipEnabled; return this; }
        public Builder compressed(Boolean compressed) { this.compressed = compressed; return this; }
        public Builder parameters(Map<String, Object> parameters) { this.parameters = parameters; return this; }

        public KafkaClientConfig build() {
            return new KafkaClientConfig(this);
        }
    }
}
