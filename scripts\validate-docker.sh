#!/bin/bash
# Docker Environment Validation Script for TestContainers
# This script validates that <PERSON><PERSON> is properly configured for running integration tests

echo "=== Docker Environment Validation for TestContainers ==="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Check if Docker is installed
echo -e "\n${YELLOW}Checking Docker installation...${NC}"
if command -v docker &> /dev/null; then
    DOCKER_VERSION=$(docker --version)
    echo -e "${GREEN}✓ Docker is installed: $DOCKER_VERSION${NC}"
else
    echo -e "${RED}✗ Docker is not installed or not in PATH${NC}"
    echo -e "${YELLOW}Please install Docker from: https://docs.docker.com/get-docker/${NC}"
    exit 1
fi

# Check if Docker daemon is running
echo -e "\n${YELLOW}Checking Docker daemon status...${NC}"
if docker info &> /dev/null; then
    echo -e "${GREEN}✓ Docker daemon is running${NC}"
else
    echo -e "${RED}✗ Docker daemon is not running${NC}"
    echo -e "${YELLOW}Please start Docker daemon${NC}"
    exit 1
fi

# Check Docker connectivity
echo -e "\n${YELLOW}Testing Docker connectivity...${NC}"
if docker ps &> /dev/null; then
    echo -e "${GREEN}✓ Docker connectivity test passed${NC}"
else
    echo -e "${RED}✗ Docker connectivity test failed${NC}"
    exit 1
fi

# Test pulling a small image (for TestContainers)
echo -e "\n${YELLOW}Testing Docker image pull capability...${NC}"
if docker pull hello-world:latest &> /dev/null; then
    echo -e "${GREEN}✓ Docker image pull test passed${NC}"
    # Clean up
    docker rmi hello-world:latest &> /dev/null
else
    echo -e "${RED}✗ Docker image pull test failed${NC}"
    echo -e "${YELLOW}This might indicate network connectivity issues${NC}"
fi

# Check available resources
echo -e "\n${YELLOW}Checking Docker resources...${NC}"
if docker system df &> /dev/null; then
    echo -e "${GREEN}✓ Docker system resources:${NC}"
    docker system df
else
    echo -e "${YELLOW}⚠ Could not retrieve Docker system information${NC}"
fi

echo -e "\n${CYAN}=== Docker Environment Validation Complete ===${NC}"
echo -e "${GREEN}✓ Docker environment is ready for TestContainers integration tests${NC}"
echo -e "\nYou can now run integration tests with:"
echo -e "${CYAN}  mvn test -Dtest=RedisIntegrationTest${NC}"
echo -e "${CYAN}  mvn test -Dtest=MessageFlowIntegrationTest${NC}"
echo -e "${CYAN}  mvn verify  # Run all integration tests${NC}"
