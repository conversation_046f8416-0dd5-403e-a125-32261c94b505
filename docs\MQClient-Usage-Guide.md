# MQClient Usage Guide

## Overview

The MQClient utility class provides a comprehensive testing framework for IBM MQ message send and receive operations, specifically designed to validate the MQ adapter and handler components in the HIP services framework.

## Features

- **Message Sending**: Implements message sending functionality using the same approach as DynamicIbmmqOutputHandler
- **Message Receiving**: Implements message receiving functionality using the same approach as DynamicIbmmqInputAdapter  
- **Configuration Compatibility**: Uses the same property structure and connection logic as existing adapter/handler implementations
- **Testing Support**: Serves as a standalone testing utility for validating MQ adapter and handler functionality

## Configuration

### Consumer Properties (for receiving/adapter testing)
```properties
ibmmq.consumer.connName=WMQNLG2A05.AMER.DELL.COM(2043)
ibmmq.consumer.auth.type=none
ibmmq.consumer.queueManager=BIEG4CU07
ibmmq.consumer.queue=QA.D365.TEST_MAC_EMFP.SCG_TEST
ibmmq.consumer.channel=BIE.GOSS.01.TLS
ibmmq.consumer.username=channel.sender
ibmmq.consumer.sslCipherSuite=TLS_RSA_WITH_AES_128_CBC_SHA256
```

### Producer Properties (for sending/handler testing)
```properties
ibmmq.producer.conn.name=WMQNLG2A05.AMER.DELL.COM(2043)
ibmmq.producer.auth.type=none
ibmmq.producer.queue.manager=BIEG4CU07
ibmmq.producer.queue=QA.D365.TEST_MAC_EMFP.SCG_TEST
ibmmq.producer.channel=BIE.GOSS.01.TLS
ibmmq.producer.username=channel.sender
ibmmq.producer.ssl.cipher.suite=TLS_RSA_WITH_AES_128_CBC_SHA256
```

## Usage Examples

### Basic Usage

```java
import com.dell.it.hip.client.MQClient;
import com.dell.it.hip.client.MQClientConfig;

// Create configurations
MQClientConfig consumerConfig = MQClientConfig.createConsumerConfig();
MQClientConfig producerConfig = MQClientConfig.createProducerConfig();

// Create MQClient
try (MQClient mqClient = new MQClient(consumerConfig, producerConfig)) {
    
    // Send a message
    mqClient.sendMessage("Hello, IBM MQ!");
    
    // Receive a message
    String received = mqClient.receiveMessage(5000); // 5 second timeout
    System.out.println("Received: " + received);
}
```

### Custom Configuration

```java
MQClientConfig customConfig = new MQClientConfig.Builder()
    .queueManager("BIEG4CU07")
    .queue("QA.D365.TEST_MAC_EMFP.SCG_TEST")
    .channel("BIE.GOSS.01.TLS")
    .connName("WMQNLG2A05.AMER.DELL.COM(2043)")
    .authenticationType("none")
    .username("channel.sender")
    .sslCipherSuite("TLS_RSA_WITH_AES_128_CBC_SHA256")
    .receiveTimeout(10000L)
    .persistent(true)
    .build();
```

### Message Listener

```java
// Start continuous message listener
mqClient.startListener(message -> {
    System.out.println("Received: " + message);
    // Process message here
});

// Send some test messages
mqClient.sendMessage("Test message 1");
mqClient.sendMessage("Test message 2");

// Stop listener when done
mqClient.stopListener();
```

### Wait for Specific Number of Messages

```java
// Wait for exactly 3 messages with 30 second timeout
mqClient.waitForMessages(3, 30000, message -> {
    System.out.println("Processing: " + message);
    // Handle each message
});
```

## Testing MQ Adapter and Handler Components

### Testing Handler (Message Sending)

```java
// Test the same functionality as DynamicIbmmqOutputHandler
MQClientConfig producerConfig = MQClientConfig.createProducerConfig();
MQClient client = new MQClient(null, producerConfig);

// Test different message types
client.sendMessage("Text message");
client.sendMessage("JSON message".getBytes());

// Test with different configurations
producerConfig.setPersistent(true);
client.sendMessage("Persistent message");
```

### Testing Adapter (Message Receiving)

```java
// Test the same functionality as DynamicIbmmqInputAdapter
MQClientConfig consumerConfig = MQClientConfig.createConsumerConfig();
MQClient client = new MQClient(consumerConfig, null);

// Test single message receive
String message = client.receiveMessage(10000);

// Test continuous listening
client.startListener(receivedMessage -> {
    // Validate message format, headers, etc.
    validateMessage(receivedMessage);
});
```

## Integration Testing Scenarios

### End-to-End Testing

```java
public void testEndToEndFlow() throws Exception {
    MQClientConfig consumerConfig = MQClientConfig.createConsumerConfig();
    MQClientConfig producerConfig = MQClientConfig.createProducerConfig();
    
    try (MQClient client = new MQClient(consumerConfig, producerConfig)) {
        
        // 1. Send test message
        String testMessage = "E2E Test - " + System.currentTimeMillis();
        client.sendMessage(testMessage);
        
        // 2. Receive and validate
        String received = client.receiveMessage(10000);
        assert received.contains("E2E Test");
        
        // 3. Test listener functionality
        CountDownLatch latch = new CountDownLatch(1);
        client.startListener(message -> {
            System.out.println("Listener received: " + message);
            latch.countDown();
        });
        
        client.sendMessage("Listener test");
        latch.await(10, TimeUnit.SECONDS);
        client.stopListener();
    }
}
```

### Performance Testing

```java
public void testPerformance() throws Exception {
    try (MQClient client = new MQClient(consumerConfig, producerConfig)) {
        
        int messageCount = 1000;
        long startTime = System.currentTimeMillis();
        
        // Send messages
        for (int i = 0; i < messageCount; i++) {
            client.sendMessage("Performance test message " + i);
        }
        
        long sendTime = System.currentTimeMillis() - startTime;
        System.out.println("Sent " + messageCount + " messages in " + sendTime + "ms");
        
        // Receive messages
        startTime = System.currentTimeMillis();
        AtomicInteger receivedCount = new AtomicInteger(0);
        
        client.waitForMessages(messageCount, 60000, message -> {
            receivedCount.incrementAndGet();
        });
        
        long receiveTime = System.currentTimeMillis() - startTime;
        System.out.println("Received " + receivedCount.get() + " messages in " + receiveTime + "ms");
    }
}
```

## Error Handling

The MQClient provides comprehensive error handling:

```java
try {
    mqClient.sendMessage("Test message");
} catch (MQException e) {
    System.err.println("MQ Error: " + e.getMessage());
    System.err.println("Reason Code: " + e.reasonCode);
} catch (Exception e) {
    System.err.println("General error: " + e.getMessage());
}
```

## Best Practices

1. **Resource Management**: Always use try-with-resources or explicitly call `close()`
2. **Timeout Configuration**: Set appropriate timeouts for receive operations
3. **Error Handling**: Implement proper exception handling for MQ operations
4. **Configuration Validation**: Validate connection parameters before use
5. **Testing Isolation**: Use separate queues for different test scenarios

## Troubleshooting

### Common Issues

1. **Connection Failures**: Verify host, port, and channel configuration
2. **Authentication Errors**: Check username/password and authentication type
3. **SSL/TLS Issues**: Verify cipher suite and certificate configuration
4. **Queue Access**: Ensure queue exists and user has appropriate permissions
5. **Timeout Issues**: Adjust receive timeout based on expected message frequency

### Debug Logging

Enable debug logging to troubleshoot issues:

```java
// Add to logback.xml or application.properties
<logger name="com.dell.it.hip.client" level="DEBUG"/>
```

## Compatibility

The MQClient is designed to be compatible with:
- IBM MQ 8.0+
- Java 11+
- Spring Framework 5.x+
- HIP Services Framework

## Dependencies

Required dependencies are already included in the HIP services project:
- IBM MQ JMS Client
- Spring JMS
- SLF4J Logging
