adapters:
  - type: ibmmq
    propertyRef: ibmmq1
    queueManager: MQ1
    queueName: ORDERS.IN
    channel: SYSTEM.ADMIN.SVRCONN
    connName: mqhost1(1414)
    authenticationType: TLS
    sslKeystore: /etc/mq/client.keystore
    sslKeystorePassword: keystorepwd
    sslTruststore: /etc/mq/client.truststore
    sslTruststorePassword: truststorepwd
    sslCipherSuite: TLS_RSA_WITH_AES_256_CBC_SHA256
    # username/password omitted
    ccsid: 1208
    concurrency: 4