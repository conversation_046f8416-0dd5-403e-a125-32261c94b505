package com.dell.it.hip.util.ediUtils;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.redisson.api.RAtomicLong;
import org.redisson.api.RLock;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;

public class RangeAwareEdiControlManager {
    private final RedissonClient redisson;
    private final String controlNumberPrefix;
    private final Map<String, TransactionRange> transactionRanges;
    private final int defaultIdLength;
    private final long lockWaitTime;
    private final long leaseTime;

    public static class TransactionRange {
        private final long minValue;
        private final long maxValue;
        private final long resetThreshold;
        
        public TransactionRange(long minValue, long maxValue, int resetThresholdPercentage) {
            this.minValue = minValue;
            this.maxValue = maxValue;
            // Calculate reset threshold based on percentage
            long range = maxValue - minValue;
            this.resetThreshold = minValue + (int)(range * (resetThresholdPercentage / 100.0));
        }
        
        public long getMinValue() { return minValue; }
        public long getMaxValue() { return maxValue; }
        public long getResetThreshold() { return resetThreshold; }
    }

    /**
     * @param redisson Redisson client
     * @param controlNumberPrefix Redis key prefix
     * @param defaultIdLength Default formatting length
     * @param lockWaitTime Max wait for lock (seconds)
     * @param leaseTime Lock lease time (seconds)
     */
    public RangeAwareEdiControlManager(RedissonClient redisson, String controlNumberPrefix,
                                     int defaultIdLength, long lockWaitTime, long leaseTime) {
        this.redisson = redisson;
        this.controlNumberPrefix = controlNumberPrefix;
        this.defaultIdLength = defaultIdLength;
        this.lockWaitTime = lockWaitTime;
        this.leaseTime = leaseTime;
        this.transactionRanges = new HashMap<>();
    }

    /**
     * Register a transaction type with custom range
     * @param transactionType EDI transaction type (e.g., "850", "855")
     * @param minValue Starting control number
     * @param maxValue Maximum control number
     * @param resetThresholdPercentage When to reset (percentage of range)
     */
    public void registerTransactionType(String transactionType, long minValue, 
                                      long maxValue, int resetThresholdPercentage) {
        if (minValue >= maxValue) {
            throw new IllegalArgumentException("Min value must be less than max value");
        }
        transactionRanges.put(transactionType, 
            new TransactionRange(minValue, maxValue, resetThresholdPercentage));
    }

    public EdiControlRecord generateControlId(String transactionType, 
                                            String senderId, 
                                            String receiverId) throws InterruptedException {
        // Validate transaction type
        TransactionRange range = transactionRanges.get(transactionType);
        if (range == null) {
            throw new IllegalArgumentException("Transaction type not registered: " + transactionType);
        }

        String lockKey = controlNumberPrefix + ":lock:" + transactionType;
        RLock lock = redisson.getLock(lockKey);
        
        try {
            // Acquire lock with timeout
            if (!lock.tryLock(lockWaitTime, leaseTime, TimeUnit.SECONDS)) {
                throw new RuntimeException("Could not acquire lock for transaction type: " + transactionType);
            }
            
            // Generate the control number
            String controlNumber = generateNextControlNumber(transactionType, range);
            
            // Create and store the record
            EdiControlRecord record = createControlRecord(controlNumber, transactionType, 
                                                        senderId, receiverId);
            storeControlRecord(record);
            
            return record;
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private String generateNextControlNumber(String transactionType, TransactionRange range) {
        String counterKey = controlNumberPrefix + ":" + transactionType;
        RAtomicLong counter = redisson.getAtomicLong(counterKey);
        
        // Initialize if not exists (atomic operation)
        counter.compareAndSet(0, range.getMinValue());
        
        // Get and increment atomically
        long nextValue = counter.getAndIncrement();
        
        // Handle reset logic
        if (nextValue >= range.getResetThreshold()) {
            counter.set(range.getMinValue());
            nextValue = range.getMinValue();
        }
        
        // Validate against max value
        if (nextValue > range.getMaxValue()) {
            throw new IllegalStateException(String.format(
                "Control number exceeded maximum value for %s: %d (generated: %d)",
                transactionType, range.getMaxValue(), nextValue));
        }
        
        return formatControlNumber(nextValue, defaultIdLength);
    }

    private String formatControlNumber(long value, int length) {
        return String.format("%0" + length + "d", value);
    }

    private EdiControlRecord createControlRecord(String controlNumber, String transactionType,
                                               String senderId, String receiverId) {
        String timestamp = LocalDateTime.now()
                .format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        return new EdiControlRecord(controlNumber, transactionType, 
                                  senderId, receiverId, timestamp);
    }

    private void storeControlRecord(EdiControlRecord record) {
        RMap<String, EdiControlRecord> controlRecords = 
            redisson.getMap(controlNumberPrefix + ":records");
        controlRecords.fastPutAsync(record.getControlNumber(), record);
    }

    public static class EdiControlRecord {
        private final String controlNumber;
        private final String transactionType;
        private final String senderId;
        private final String receiverId;
        private final String creationTimestamp;
        
        // Constructor and getters
        public EdiControlRecord(String controlNumber, String transactionType,
                               String senderId, String receiverId, String creationTimestamp) {
            this.controlNumber = controlNumber;
            this.transactionType = transactionType;
            this.senderId = senderId;
            this.receiverId = receiverId;
            this.creationTimestamp = creationTimestamp;
        }
        
        // Getters
        public String getControlNumber() { return controlNumber; }
        public String getTransactionType() { return transactionType; }
        public String getSenderId() { return senderId; }
        public String getReceiverId() { return receiverId; }
        public String getCreationTimestamp() { return creationTimestamp; }
    }
}