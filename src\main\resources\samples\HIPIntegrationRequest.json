{"hipIntegrationName": "OrderProcessingService", "version": "1.2.0", "owner": "supply-chain-team", "tags": ["orders", "processing", "v1.2"], "propertySheets": ["shared-kafka-properties", "shared-enterprise-security", "orderProcessing-adapter-kafka", "orderProcessing-adapter-ibmmq", "orderProcessing-handler-sftp"], "throttleSettings": {"enabled": true, "maxMessagesPerPeriod": 1000, "periodSeconds": 60}, "adapters": [{"type": "kafka", "propertyRef": "orderKafkaInput", "description": "Input from Orders topic in Kafka"}, {"type": "ibmmq", "propertyRef": "orderIbmmqInput", "description": "Input from external OrderQueue in IBM MQ"}], "flowSteps": [{"type": "validation", "propertyRef": "orderValidationStep", "description": "Validate order payload structure"}, {"type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "propertyRef": "orderHeaderEnricherStep", "description": "Extract and enrich order headers"}], "handlers": [{"type": "kafka", "propertyRef": "orderKafkaOutput", "role": "primary", "description": "Primary output to Kafka (OrderProcessed)"}, {"type": "sftp", "propertyRef": "orderSftpFallbackOutput", "role": "fallback", "description": "Fallback output to SFTP for processed orders"}], "callbackConfig": {"type": "sftp", "propertyRef": "orderSftpCallback", "callbackUrl": "/api/orderprocessing/sftp-callback", "enabled": true}}