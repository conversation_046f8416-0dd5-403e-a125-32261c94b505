package com.dell.it.hip.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.messaging.MessageChannel;

@Configuration
public class HIPIntegrationChannelsConfig {

    @Bean("hip.error")
    public MessageChannel hipErrorChannel() {
        return new DirectChannel();
    }

    // You can define more well-known channels here if desired
}