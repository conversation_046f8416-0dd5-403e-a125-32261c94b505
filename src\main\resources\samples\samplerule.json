{"ruleName": "RouteToPaymentsAndAudit", "ruleDescription": "Routes invoices with payment type to Payments and Audit flows", "documentType": "invoice", "ruleScope": "local", "ruleType": "flowRouting", "ruleVersion": "1.0", "executeAlways": false, "status": "ENABLED", "executeActionWhen": "ALL", "ruleConditions": [{"propertyName": "HIP.documentType", "operator": "EQUALS", "value": "invoice"}, {"propertyName": "HIP.flowIdentifier", "operator": "EQUALS", "value": "payment"}], "actions": [{"name": "flowResponse", "type": "FlowResponse", "params": {"channels": ["PaymentsFlow.input", "AuditFlow.input"]}}]}