package com.dell.it.hip.config.adapters;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DynamicNASAdapterConfig extends AdapterConfig {

    // --- Protocol ---
	@JsonProperty("nas.consumer.protocol")
    private String protocol;           // "nfs" or "smb"

    // --- SMB-specific fields ---
    @<PERSON>son<PERSON>roper<PERSON>("nas.consumer.host")
    private String host;               // SMB hostname/IP

    @JsonProperty("nas.consumer.share.name")
    private String shareName;          // SMB share name

    @JsonProperty("nas.consumer.domain")
    private String domain;             // AD domain, optional

    @JsonProperty("nas.consumer.username")
    private String username;

    @JsonProperty("nas.consumer.password")
    private String password;

    @JsonProperty("nas.consumer.mount.path")
    private String mountPath;          // OS-mounted path, e.g. /mnt/smbshare

    // --- Common fields ---
    @JsonProperty("nas.consumer.remote.directory")
    private String remoteDirectory;    // Directory inside share or NFS mount

    // --- Polling and file selection ---
    @JsonProperty("nas.consumer.file.filter")
    private String fileNamePattern;    // Regex for files, e.g. ".*\\.csv"

    @JsonProperty("nas.consumer.file.sort.order")
    private String fileSortOrder;      // "OLDEST", "NEWEST", or null

    @JsonProperty("nas.consumer.max.files.per.poll")
    private Integer maxFilesPerPoll;   // Limit per poll cycle

    @JsonProperty("nas.consumer.polling.interval.ms")
    private Long pollingIntervalMs;    // How often to poll (ms), default 60000

    @JsonProperty("nas.consumer.file.age.ms")
    private Long fileAgeMs;            // Minimum file age before pickup (ms)

    @JsonProperty("nas.consumer.ignore.hidden.files")
    private Boolean ignoreHiddenFiles; // Skip hidden files

    // --- Content handling ---
    @JsonProperty("nas.consumer.charset")
    private String charset;            // e.g., "UTF-8"

    // --- Post-processing ---
    @JsonProperty("nas.consumer.post.process.action")
    private String postProcessAction;  // "delete", "rename", or "move"

    @JsonProperty("nas.consumer.rename.pattern")
    private String renamePattern;      // e.g. "{file}.processed"

    // --- Extensibility/future options ---
    @JsonProperty("nas.consumer.parameters")
    private Map<String, Object> parameters; // Extra options as needed
}