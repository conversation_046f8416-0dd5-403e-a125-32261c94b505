package com.dell.it.hip.config.FlowSteps;
import java.util.List;

import lombok.Data;
@Data
public class DedupFlowStepConfig extends FlowStepConfig {
    private String propertyRef;
    private List<String> headersForKey;
    private int dedupExpirySeconds = 900; // default 15min
    private String onDuplicate = "DROP"; // DROP | FLAG | CONTINUE | DEADLETTER
    private String wiretapEventLevel = "warn";
    private boolean enableMetrics = true;  // enterprise
    private boolean logOnInsert = false;   // log when new key is created

    // getters and setters

    @Override
    public String getPropertyRef() { return propertyRef; }
    @Override
    public void setPropertyRef(String ref) { this.propertyRef = ref; }




}