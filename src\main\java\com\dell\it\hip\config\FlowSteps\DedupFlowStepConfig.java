package com.dell.it.hip.config.FlowSteps;
import java.util.ArrayList;
import java.util.List;

import lombok.Data;

@Data
public class DedupFlowStepConfig extends FlowStepConfig {
	
	private List<DocTypeDedupConfig> docTypeConfigs = new ArrayList<>();
    private DefaultDedupConfig defaultConfig;
    
    @Data
    public static class DocTypeDedupConfig extends DocTypeConfig {
        private DedupBehavior behavior = DedupBehavior.DEDUP;
        private List<String> headersForKey;
        private int dedupExpirySeconds = 900; // default 15min
        private String onDuplicate = "DROP"; // DROP | FLAG | CONTINUE | DEADLETTER
        private String wiretapEventLevel = "warn";
        private boolean enableMetrics = true;  // enterprise
        private boolean logOnInsert = false;   // log when new key is created
    }

    @Data
    public static class DefaultDedupConfig {
        private DedupBehavior behavior = DedupBehavior.DEDUP;
        private List<String> headersForKey;
        private int dedupExpirySeconds = 900; // default 15min
        private String onDuplicate = "DROP"; // DROP | FLAG | CONTINUE | DEADLETTER
        private String wiretapEventLevel = "warn";
        private boolean enableMetrics = true;  // enterprise
        private boolean logOnInsert = false;   // log when new key is created
    }

    public enum DedupBehavior { DEDUP, SKIP, TERMINATE }
    

}