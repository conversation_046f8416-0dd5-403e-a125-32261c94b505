package com.dell.it.hip.util;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.messaging.converter.AbstractMessageConverter;
import org.springframework.stereotype.Service;

/**
 * Decompresses GZIP-encoded payloads.
 */
@Service
public class CompressionUtil extends AbstractMessageConverter {

    @Override
    protected boolean supports(Class<?> clazz) {
        return false;
    }



    /**
     * Returns true if the record's payload appears to be GZIP-compressed.
     */
    public boolean shouldDecompress(ConsumerRecord<Object, Object> record) {
        Object val = record.value();
        if (!(val instanceof byte[])) {
            return false;
        }
        byte[] payload = (byte[]) val;
        // GZIP magic numbers: 0x1f, 0x8b
        return payload.length > 2 && (payload[0] == (byte)0x1f) && (payload[1] == (byte)0x8b);
    }

    /**
     * Decompresses the GZIP payload into a byte[].
     */
    public static byte[] decompress(Object payload) {
        if (!(payload instanceof byte[])) {
            throw new IllegalArgumentException("Payload is not a byte[]");
        }
        byte[] data = (byte[]) payload;
        try (GZIPInputStream gis = new GZIPInputStream(new ByteArrayInputStream(data))) {
            byte[] buffer = new byte[1024];
            int len;
            try (java.io.ByteArrayOutputStream out = new java.io.ByteArrayOutputStream()) {
                while ((len = gis.read(buffer)) > 0) {
                    out.write(buffer, 0, len);
                }
                return out.toByteArray();
            }
        } catch (IOException e) {
            throw new IllegalStateException("Failed to decompress GZIP payload", e);
        }
    }

    /**
     * Compresses the given payload to a GZIP byte array.
     * Supports String and byte[] directly. Others use toString().
     */
    public static byte[] compress(Object payload) {
        byte[] data;
        if (payload == null) {
            return new byte[0];
        } else if (payload instanceof byte[] bytes) {
            data = bytes;
        } else if (payload instanceof String str) {
            data = str.getBytes(StandardCharsets.UTF_8);
        } else {
            data = String.valueOf(payload).getBytes(StandardCharsets.UTF_8);
        }
        return compressBytes(data);
    }

    private static byte[] compressBytes(byte[] input) {
        if (input == null || input.length == 0) {
            return new byte[0];
        }
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
             GZIPOutputStream gzip = new GZIPOutputStream(bos)) {
            gzip.write(input);
            gzip.finish();
            return bos.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException("GZIP compression failed", e);
        }
    }

}