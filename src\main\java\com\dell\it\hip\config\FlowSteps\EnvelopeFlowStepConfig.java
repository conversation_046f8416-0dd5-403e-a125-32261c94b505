package com.dell.it.hip.config.FlowSteps;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class EnvelopeFlowStepConfig extends FlowStepConfig {
    @JsonProperty("startTag")
	private String envelopeHeader;    // e.g. "ISA*00*...~" or "<root>" or CSV header
    @JsonProperty("endTag")
    private String envelopeFooter;    // e.g. "IEA*1*000000001~" or "</root>" or null
    @JsonProperty("messageSeperator")
    private String separator;         // e.g. "~" or "\n" (defaults per format if not set)
    private String dataFormat;        // Optionally override, else detected from payload
    @JsonProperty("validation")
    private boolean validateEnvelope = true; // Run structural validation after enveloping
    private Map<String, String> dynamicHeaders; // Optional map for dynamic envelope values (placeholders)
}