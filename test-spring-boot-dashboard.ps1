# Test Script for Spring Boot Dashboard Configuration
# This script helps verify that the Spring Boot Dashboard is properly configured

Write-Host "🚀 Testing Spring Boot Dashboard Configuration..." -ForegroundColor Green
Write-Host ""

# Check if VS Code is installed
Write-Host "📋 Checking VS Code installation..." -ForegroundColor Yellow
try {
    $vscodeVersion = code --version 2>&1 | Select-Object -First 1
    Write-Host "✅ VS Code found: $vscodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ VS Code not found or not in PATH" -ForegroundColor Red
    exit 1
}

# Check if Spring Boot Dashboard extension is installed
Write-Host "🔌 Checking Spring Boot Dashboard extension..." -ForegroundColor Yellow
$extensions = code --list-extensions 2>&1
if ($extensions -match "vmware.vscode-spring-boot") {
    Write-Host "✅ Spring Boot Dashboard extension is installed" -ForegroundColor Green
} else {
    Write-Host "❌ Spring Boot Dashboard extension not found" -ForegroundColor Red
    Write-Host "   Install with: code --install-extension vmware.vscode-spring-boot" -ForegroundColor Cyan
}

# Check launch.json configuration
Write-Host "📁 Checking launch.json configuration..." -ForegroundColor Yellow
if (Test-Path ".vscode/launch.json") {
    $launchConfig = Get-Content ".vscode/launch.json" -Raw
    if ($launchConfig -match "Launch HipServicesApplication \(Cloud Profile\)") {
        Write-Host "✅ Cloud Profile launch configuration found" -ForegroundColor Green
        
        # Check for required environment variables
        if ($launchConfig -match "configproperties_sheet_name") {
            Write-Host "✅ configproperties_sheet_name environment variable configured" -ForegroundColor Green
        } else {
            Write-Host "❌ configproperties_sheet_name environment variable missing" -ForegroundColor Red
        }
        
        if ($launchConfig -match "configserver_uri") {
            Write-Host "✅ configserver_uri environment variable configured" -ForegroundColor Green
        } else {
            Write-Host "❌ configserver_uri environment variable missing" -ForegroundColor Red
        }
        
        if ($launchConfig -match "SPRING_PROFILES_ACTIVE.*cloud") {
            Write-Host "✅ SPRING_PROFILES_ACTIVE=cloud configured" -ForegroundColor Green
        } else {
            Write-Host "❌ SPRING_PROFILES_ACTIVE=cloud missing" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Cloud Profile launch configuration not found" -ForegroundColor Red
    }
} else {
    Write-Host "❌ .vscode/launch.json not found" -ForegroundColor Red
}

# Check application-cloud.yaml
Write-Host "📄 Checking application-cloud.yaml..." -ForegroundColor Yellow
if (Test-Path "src/main/resources/application-cloud.yaml") {
    Write-Host "✅ application-cloud.yaml configuration file found" -ForegroundColor Green
} else {
    Write-Host "❌ application-cloud.yaml configuration file missing" -ForegroundColor Red
}

# Check main application class
Write-Host "☕ Checking main application class..." -ForegroundColor Yellow
if (Test-Path "src/main/java/com/dell/it/hip/HipServicesApplication.java") {
    $mainClass = Get-Content "src/main/java/com/dell/it/hip/HipServicesApplication.java" -Raw
    if ($mainClass -match "@SpringBootApplication") {
        Write-Host "✅ Spring Boot main class found with @SpringBootApplication annotation" -ForegroundColor Green
    } else {
        Write-Host "❌ @SpringBootApplication annotation not found" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Main application class not found" -ForegroundColor Red
}

# Check Java and Maven
Write-Host "🔧 Checking Java and Maven..." -ForegroundColor Yellow
try {
    $javaVersion = java -version 2>&1 | Select-String "version" | Select-Object -First 1
    Write-Host "✅ Java found: $javaVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Java not found or not in PATH" -ForegroundColor Red
}

try {
    $mavenVersion = mvn -version 2>&1 | Select-String "Apache Maven" | Select-Object -First 1
    Write-Host "✅ Maven found: $mavenVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Maven not found or not in PATH" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎯 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Open VS Code: code ." -ForegroundColor White
Write-Host "2. Open Spring Boot Dashboard (Activity Bar → Spring Boot icon)" -ForegroundColor White
Write-Host "3. Look for 'HipServicesApplication' in the dashboard" -ForegroundColor White
Write-Host "4. Right-click and select 'Start' or use Run and Debug view" -ForegroundColor White
Write-Host "5. Select 'Launch HipServicesApplication (Cloud Profile)'" -ForegroundColor White
Write-Host ""
Write-Host "📚 For detailed instructions, see: SPRING_BOOT_DASHBOARD_SETUP.md" -ForegroundColor Cyan
Write-Host ""
Write-Host "🎉 Configuration check complete!" -ForegroundColor Green
