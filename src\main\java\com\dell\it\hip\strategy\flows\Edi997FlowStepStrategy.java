package com.dell.it.hip.strategy.flows;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.Edi997FlowStepConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.strategy.edi.AcknowledgeObect;
import com.dell.it.hip.strategy.edi.EDIWriter;
import com.dell.it.hip.strategy.edi.Edi997AcknowledgeService;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.dell.it.hip.util.validation.MessageFormatDetector;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;
@Slf4j
@Component("edi997ack")
public class Edi997FlowStepStrategy extends AbstractFlowStepStrategy {
	@Autowired
	private ObjectMapper objectMapper;
	@Autowired
	private Edi997AcknowledgeService ediackservice;
	@Autowired
	private EDIWriter ediWriter;


	@Override
	public String getType() {
		// TODO Auto-generated method stub
		return "edi997ack";
	}

	@Override
	protected List<Message<?>> doExecute(Message<?> message, FlowStepConfigRef ref, HIPIntegrationDefinition def)
			throws Exception {
		Edi997FlowStepConfig config = (Edi997FlowStepConfig) def.getConfigMap().get(ref.getPropertyRef());
		if (config == null) {
			throw new IllegalStateException("No ediack config for: " + ref.getPropertyRef());
		}
		try {
			String payload = Objects.toString(message.getPayload(), "");
			String format = (String) message.getHeaders().get("HIP.payload.dataformat");
			if (format == null) {
				format = MessageFormatDetector.detect(payload);
			}
			AcknowledgeObect ackObj = ediackservice.ediX12AckObj(message);
			// to see the json in logs..to be delete later
			String json = objectMapper.writeValueAsString(ackObj);
			log.info("ediackobj " + json);
			String ediMsg = ediWriter.ediStreamWriter(ackObj);
			log.info("997 ediMsg----------> " + ediMsg);
			List<Message<?>> result = new ArrayList<>();
			result.add(MessageBuilder.withPayload(message).build());
			// Log successful split
			wiretapService.tap(message, def, ref, "info", "Splitter produced " + result.size() + " messages");
			result.add(MessageBuilder.withPayload(message).build());
			return result;
		} catch (Exception e) {
			e.printStackTrace();
			String errMsg = "error while generating edi 997 ack: " + e.getMessage();
			wiretapService.tap(message, def, ref, "error", errMsg);
			TransactionLoggingUtil.logError(message, def, ref, "Edi997Failure", errMsg);
			return Collections.emptyList();

		}
	}

}
