package com.dell.it.hip.util;

import java.util.*;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.client.support.BasicAuthenticationInterceptor;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class PropertySheetFetcher {

    @Value("${configserver_uri}")
    private String configServerUri; // e.g., http://config-server:8888

    @Value("${SPRING_PROFILES_ACTIVE}")
    private String defaultProfile;
    
    @Value("${configproperties_sheet_name}")
    private String configpropertiesSheetName;

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final RestTemplate restTemplate = new RestTemplate();

    // ... existing code ...

    public Map<String, Object> fetchSheet(String sheetName) {
        Map<String, Object> flatProps = new HashMap<>();
       
        try {
            String url = String.format("%s/%s/%s", configServerUri, sheetName+","+configpropertiesSheetName, defaultProfile);

            String userPassword = StringUtils.substringBetween(url, "//", "@");
            log.debug("userPassword=" + userPassword);

            String[] tokens = userPassword.split(":");
            String userName = tokens[0];
            String pwd = tokens[1];
            log.debug("userName=" + userName + " and pwd=" + pwd);

            restTemplate.getInterceptors().add(
                    new BasicAuthenticationInterceptor(userName, pwd)
            );

            String json = restTemplate.getForObject(url, String.class);

            JsonNode root = objectMapper.readTree(json);
            JsonNode sources = root.get("propertySources");

            if (sources != null && sources.isArray()) {
                for (JsonNode src : sources) {
                    JsonNode sourceProps = src.get("source");
                    if (sourceProps != null) {
                        // Iterate through all fields in this source and add them to the combined properties
                        Iterator<Map.Entry<String, JsonNode>> fields = sourceProps.fields();
                        while (fields.hasNext()) {
                            Map.Entry<String, JsonNode> entry = fields.next();
                            // Convert JsonNode to appropriate Object type for the combined map
                            Object value;
                            if (entry.getValue().isTextual()) {
                                value = entry.getValue().asText();
                            } else if (entry.getValue().isNumber()) {
                                value = entry.getValue().numberValue();
                            } else if (entry.getValue().isBoolean()) {
                                value = entry.getValue().asBoolean();
                            } else {
                                // For complex objects, keep as JsonNode or convert to string
                                value = entry.getValue().toString();
                            }
                            flatProps.put(entry.getKey(), value);
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to fetch property sheet from config server: " + String.format("%s/%s/%s", configServerUri, sheetName, defaultProfile), e);
        }

        // Convert the combined properties Map to a JsonNode
        JsonNode combinedJsonNode = objectMapper.valueToTree(flatProps);

        Map<String, Object> result = new LinkedHashMap<>();
        result.put(sheetName, combinedJsonNode);
        return result;
    }

    // --- Production-ready unflattener ---
    public static Map<String, Object> unflatten(Map<String, String> flatMap) {
    	  Map<String, Object> result = new LinkedHashMap<>();
        for (Map.Entry<String, String> entry : flatMap.entrySet()) {
            String key = entry.getKey();
            List<String> parts = new ArrayList<>();
            // Support both dot and bracket notation for arrays
            int idx = 0;
            while (idx < key.length()) {
                int dotIdx = key.indexOf('.', idx);
                int bracketIdx = key.indexOf('[', idx);
                if (bracketIdx != -1 && (dotIdx == -1 || bracketIdx < dotIdx)) {
                    parts.add(key.substring(idx, bracketIdx));
                    int endBracket = key.indexOf(']', bracketIdx);
                    if (endBracket == -1) throw new IllegalArgumentException("Unmatched [ in key: " + key);
                    parts.add(key.substring(bracketIdx + 1, endBracket));
                    idx = endBracket + 1;
                    if (idx < key.length() && key.charAt(idx) == '.') idx++;
                } else if (dotIdx != -1) {
                    parts.add(key.substring(idx, dotIdx));
                    idx = dotIdx + 1;
                } else {
                    parts.add(key.substring(idx));
                    break;
                }
            }
            insertNested(result, parts, entry.getValue());
        }
        return result;
    }

    // Helper method to build nested maps/lists
    @SuppressWarnings("unchecked")
    private static void insertNested(Map<String, Object> map, List<String> parts, String value) {
        Object current = map;
        for (int i = 0; i < parts.size(); i++) {
            String part = parts.get(i);
            boolean isLast = (i == parts.size() - 1);
            boolean isArrayIndex = part.matches("\\d+");
            if (isLast) {
                if (current instanceof Map) {
                    ((Map<String, Object>) current).put(part, value);
                } else if (current instanceof List && isArrayIndex) {
                    int idx = Integer.parseInt(part);
                    List<Object> list = (List<Object>) current;
                    while (list.size() <= idx) list.add(null);
                    list.set(idx, value);
                }
            } else {
                Object next;
                boolean nextIsIndex = (i + 1 < parts.size()) && parts.get(i + 1).matches("\\d+");
                if (current instanceof Map) {
                    Map<String, Object> currentMap = (Map<String, Object>) current;
                    if (nextIsIndex) {
                        // Prepare list
                        next = currentMap.get(part);
                        if (!(next instanceof List)) {
                            next = new ArrayList<>();
                            currentMap.put(part, next);
                        }
                    } else {
                        // Prepare map
                        next = currentMap.get(part);
                        if (!(next instanceof Map)) {
                            next = new LinkedHashMap<>();
                            currentMap.put(part, next);
                        }
                    }
                    current = next;
                } else if (current instanceof List && isArrayIndex) {
                    int idx = Integer.parseInt(part);
                    List<Object> list = (List<Object>) current;
                    while (list.size() <= idx) list.add(null);
                    next = list.get(idx);
                    if (next == null) {
                        if ((i + 1 < parts.size()) && parts.get(i + 1).matches("\\d+")) {
                            next = new ArrayList<>();
                        } else {
                            next = new LinkedHashMap<>();
                        }
                        list.set(idx, next);
                    }
                    current = next;
                }
            }
        }
    }

    // Overload for when there are no direct overrides
    public Map<String, Object> fetchAndMerge(List<String> propertySheets) {
        return fetchAndMerge(propertySheets, null);
    }
    
    /**
     * Fetches and merges property sheets for a given HIPIntegration at runtime.
     * The last sheet or map in the list will override earlier values.
     */
    public Map<String, Object> fetchAndMerge(List<String> propertySheets, Map<String, Object> directOverrides) {
        Map<String, Object> merged = new LinkedHashMap<>();
        if (propertySheets != null) {
            for (String sheet : propertySheets) {
                if (sheet != null && !sheet.isEmpty()) {
                    merged.putAll(fetchSheet(sheet));
                }
            }
        }
        // Overlay direct overrides
        if (directOverrides != null && !directOverrides.isEmpty()) {
            merged.putAll(directOverrides);
        }
        return merged;
    }
    
    public static void main(String[] args) {

    	ObjectMapper objectMapper = new ObjectMapper();
        JsonNode combinedJsonNode = null;
        try {


            String json = "{\"name\":\"shared-ibmmq-producer,shared-inter-comm-kafka-consumer,shared-inter-comm-kafka-producer,shared-kafka-consumer,shared-kafka-producer\",\"profiles\":[\"cloud\"],\"label\":null,\"version\":null,\"state\":null,\"propertySources\":[{\"name\":\"vault:shared-kafka-producer\",\"source\":{\"kafka.producer.sasl.jaas.config.password\":\"ji?zVD58h2WkfxmY0AL+N*PC\",\"kafka.producer.sasl.jaas.config.username\":\"svc_nphipkafkagscm\",\"kafka.producer.ssl.truststore.password\":\"good8008\"}},{\"name\":\"vault:shared-kafka-consumer\",\"source\":{\"kafka.consumer.sasl.jaas.config.password\":\"ji?zVD58h2WkfxmY0AL+N*PC\",\"kafka.consumer.sasl.jaas.config.username\":\"svc_nphipkafkagscm\",\"kafka.consumer.ssl.truststore.password\":\"good8008\"}},{\"name\":\"vault:shared-ibmmq-producer\",\"source\":{\"ibm.mq.producer.password\":\"rzqS*nTA?GUcw1Pv+Bef2oxY\",\"ibm.mq.producer.user\":\"svc_nphipmqgscm\"}},{\"name\":\"https://gitlab.dell.com/des/eis/gobig/b2b-hip/configuration-management/properties/non-prod/hip-dev-properties.git/shared/common/shared-kafka-producer.yaml\",\"source\":{\"kafka.producer.security.protocol\":\"SASL_SSL\",\"kafka.producer.sasl.mechanism\":\"PLAIN\",\"kafka.producer.ssl.enabled.protocols\":\"TLSv1.2\",\"kafka.producer.ssl.truststore.location\":\"/etc/ssl/certs/kafka-truststore/kafka.client.truststore.jks\",\"kafka.producer.ssl.truststore.type\":\"JKS\",\"kafka.producer.sasl.jaas.config\":\"org.apache.kafka.common.security.plain.PlainLoginModule required\"}},{\"name\":\"https://gitlab.dell.com/des/eis/gobig/b2b-hip/configuration-management/properties/non-prod/hip-dev-properties.git/shared/common/shared-kafka-consumer.yaml\",\"source\":{\"kafka.consumer.security.protocol\":\"SASL_SSL\",\"kafka.consumer.sasl.mechanism\":\"PLAIN\",\"kafka.consumer.ssl.enabled.protocols\":\"TLSv1.2\",\"kafka.consumer.ssl.truststore.location\":\"/etc/ssl/certs/kafka-truststore/kafka.client.truststore.jks\",\"kafka.consumer.ssl.truststore.type\":\"JKS\",\"kafka.consumer.sasl.jaas.config\":\"org.apache.kafka.common.security.plain.PlainLoginModule required\",\"kafka.consumer.offset\":\"latest\",\"kafka.consumer.group.id\":\"B2B_HIP_GSCM_DEV\",\"max.poll.records\":100}},{\"name\":\"https://gitlab.dell.com/des/eis/gobig/b2b-hip/configuration-management/properties/non-prod/hip-dev-properties.git/shared/common/shared-inter-comm-kafka-producer.yaml\",\"source\":{\"kafka.producer.bootstrap.servers\":\"kafnlprfgscm.us.dell.com\",\"kafka.producer.bootstrap.servers.port\":9094}},{\"name\":\"https://gitlab.dell.com/des/eis/gobig/b2b-hip/configuration-management/properties/non-prod/hip-dev-properties.git/shared/common/shared-inter-comm-kafka-consumer.yaml\",\"source\":{\"document\":\"kafka.consumer.bootstrap.servers = kafnlprfgscm.us.dell.com kafka.consumer.bootstrap.servers.port = 9094\"}},{\"name\":\"https://gitlab.dell.com/des/eis/gobig/b2b-hip/configuration-management/properties/non-prod/hip-dev-properties.git/shared/common/shared-ibmmq-producer.yaml\",\"source\":{\"ibm.mq.producer.cipher.suite\":\"TLS_RSA_WITH_AES_128_CBC_SHA256\"}}]}";

            JsonNode root = objectMapper.readTree(json);
            JsonNode sources = root.get("propertySources");

            // Create a Map to accumulate all properties, then convert to JsonNode
            Map<String, Object> combinedProps = new LinkedHashMap<>();

            if (sources != null && sources.isArray()) {
                for (JsonNode src : sources) {
                    JsonNode sourceProps = src.get("source");
                    if (sourceProps != null) {
                        // Iterate through all fields in this source and add them to the combined properties
                        Iterator<Map.Entry<String, JsonNode>> fields = sourceProps.fields();
                        while (fields.hasNext()) {
                            Map.Entry<String, JsonNode> entry = fields.next();
                            // Convert JsonNode to appropriate Object type for the combined map
                            Object value;
                            if (entry.getValue().isTextual()) {
                                value = entry.getValue().asText();
                            } else if (entry.getValue().isNumber()) {
                                value = entry.getValue().numberValue();
                            } else if (entry.getValue().isBoolean()) {
                                value = entry.getValue().asBoolean();
                            } else {
                                // For complex objects, keep as JsonNode or convert to string
                                value = entry.getValue().toString();
                            }
                            combinedProps.put(entry.getKey(), value);
                        }
                    }
                }
            }

            // Convert the combined properties Map to a JsonNode
            combinedJsonNode = objectMapper.valueToTree(combinedProps);

        } catch (Exception e) {
            e.printStackTrace();
        }

        Map<String, Object> result = new LinkedHashMap<>();
        result.put("mysheet", combinedJsonNode);
        System.out.println(result.toString());
	}


}