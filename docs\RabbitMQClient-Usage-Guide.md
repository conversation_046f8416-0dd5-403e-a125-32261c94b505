# RabbitMQClient Usage Guide

## Overview

The RabbitMQClient utility class provides a comprehensive testing framework for RabbitMQ message send and receive operations, specifically designed to validate the RabbitMQ adapter and handler components in the HIP services framework.

## Features

- **Message Sending**: Implements message sending functionality using the same approach as DynamicRabbitMQOutputHandler
- **Message Receiving**: Implements message receiving functionality using the same approach as DynamicRabbitMQInputAdapter  
- **Configuration Compatibility**: Uses the same property structure and connection logic as existing adapter/handler implementations
- **Testing Support**: Serves as a standalone testing utility for validating RabbitMQ adapter and handler functionality

## Configuration

### Consumer Properties (for receiving/adapter testing)
```properties
rabbitmq.consumer.host=paas-rmq-aic-dev01.us.dell.com
rabbitmq.consumer.port=8071
rabbitmq.consumer.vhost=AIC_ACTMON_DEV
rabbitmq.consumer.username=AMERICAS\svc_npaicdvgsmrmqv1
rabbitmq.consumer.password=~UfIBrgxqj%2BVp%3FSTK69L178H
rabbitmq.consumer.queueName=test.queue
rabbitmq.consumer.auth.type=TLS
rabbitmq.consumer.acknowledgeMode=AUTO
rabbitmq.consumer.prefetchCount=10
rabbitmq.consumer.concurrency=1
rabbitmq.consumer.compressed=false
```

### Producer Properties (for sending/handler testing)
```properties
rabbitmq.producer.host=paas-rmq-aic-dev01.us.dell.com
rabbitmq.producer.port=8071
rabbitmq.producer.vhost=AIC_ACTMON_DEV
rabbitmq.producer.username=AMERICAS\svc_npaicdvgsmrmqv1
rabbitmq.producer.password=~UfIBrgxqj%2BVp%3FSTK69L178H
rabbitmq.producer.exchange=test.exchange
rabbitmq.producer.routing.key=test.routing.key
rabbitmq.producer.mandatory=false
rabbitmq.producer.persistent=false
rabbitmq.producer.gzip.enabled=false
rabbitmq.producer.ssl.enabled=true
```

## Usage Examples

### Basic Usage

```java
import com.dell.it.hip.client.RabbitMQClient;
import com.dell.it.hip.client.RabbitMQClientConfig;

// Create configurations
RabbitMQClientConfig consumerConfig = RabbitMQClientConfig.createConsumerConfig();
RabbitMQClientConfig producerConfig = RabbitMQClientConfig.createProducerConfig();

// Create RabbitMQClient
try (RabbitMQClient rabbitMQClient = new RabbitMQClient(consumerConfig, producerConfig)) {
    
    // Send a message
    rabbitMQClient.sendMessage("Hello, RabbitMQ!");
    
    // Receive a message
    RabbitMQClient.RabbitMQMessage received = rabbitMQClient.receiveMessage(5000); // 5 second timeout
    if (received != null) {
        System.out.println("Received: " + received.getContent());
        System.out.println("Queue: " + received.getQueue());
        System.out.println("Exchange: " + received.getExchange());
        System.out.println("Routing Key: " + received.getRoutingKey());
        System.out.println("Headers: " + received.getHeaders());
    }
}
```

### Custom Configuration

```java
RabbitMQClientConfig customConfig = new RabbitMQClientConfig.Builder()
    .host("paas-rmq-aic-dev01.us.dell.com")
    .port(8071)
    .virtualHost("AIC_ACTMON_DEV")
    .username("AMERICAS\\svc_npaicdvgsmrmqv1")
    .password("~UfIBrgxqj%2BVp%3FSTK69L178H")
    .queueName("custom.test.queue")
    .exchange("custom.test.exchange")
    .routingKey("custom.test.routing.key")
    .authenticationType("TLS")
    .acknowledgeMode(AcknowledgeMode.MANUAL)
    .prefetchCount(5)
    .persistent(true)
    .mandatory(true)
    .gzipEnabled(true)
    .build();
```

### Message Listener

```java
// Start continuous message listener
rabbitMQClient.startListener(message -> {
    System.out.println("Received: " + message.getContent());
    System.out.println("Headers: " + message.getHeaders());
    // Process message here
});

// Send some test messages
rabbitMQClient.sendMessage("Test message 1");
rabbitMQClient.sendMessage("Test message 2");

// Stop listener when done
rabbitMQClient.stopListener();
```

### Send Message with Headers

```java
// Send message with custom headers
Map<String, Object> headers = new HashMap<>();
headers.put("messageId", "test-123");
headers.put("source", "TestApplication");
headers.put("timestamp", String.valueOf(System.currentTimeMillis()));

rabbitMQClient.sendMessage("Message with headers".getBytes(), headers);
```

### Wait for Specific Number of Messages

```java
// Wait for exactly 3 messages with 30 second timeout
List<RabbitMQClient.RabbitMQMessage> messages = rabbitMQClient.waitForMessages(3, 30000, message -> {
    System.out.println("Processing: " + message.getContent());
    // Handle each message
});

System.out.println("Received " + messages.size() + " messages");
```

## Testing RabbitMQ Adapter and Handler Components

### Testing Handler (Message Sending)

```java
// Test the same functionality as DynamicRabbitMQOutputHandler
RabbitMQClientConfig producerConfig = RabbitMQClientConfig.createProducerConfig();
RabbitMQClient client = new RabbitMQClient(null, producerConfig);

// Test different message types
client.sendMessage("Text message");
client.sendMessage("JSON message".getBytes());

// Test with different configurations
producerConfig.setPersistent(true);
producerConfig.setMandatory(true);
client.sendMessage("Reliable message");
```

### Testing Adapter (Message Receiving)

```java
// Test the same functionality as DynamicRabbitMQInputAdapter
RabbitMQClientConfig consumerConfig = RabbitMQClientConfig.createConsumerConfig();
RabbitMQClient client = new RabbitMQClient(consumerConfig, null);

// Test single message receive
RabbitMQClient.RabbitMQMessage message = client.receiveMessage(10000);

// Test continuous listening
client.startListener(receivedMessage -> {
    // Validate message format, headers, etc.
    validateMessage(receivedMessage);
});
```

## Integration Testing Scenarios

### End-to-End Testing

```java
public void testEndToEndFlow() throws Exception {
    RabbitMQClientConfig consumerConfig = RabbitMQClientConfig.createConsumerConfig();
    RabbitMQClientConfig producerConfig = RabbitMQClientConfig.createProducerConfig();
    
    try (RabbitMQClient client = new RabbitMQClient(consumerConfig, producerConfig)) {
        
        // 1. Send test message
        String testMessage = "E2E Test - " + System.currentTimeMillis();
        client.sendMessage(testMessage);
        
        // 2. Receive and validate
        RabbitMQClient.RabbitMQMessage received = client.receiveMessage(10000);
        assert received.getContent().contains("E2E Test");
        
        // 3. Test listener functionality
        CountDownLatch latch = new CountDownLatch(1);
        client.startListener(message -> {
            System.out.println("Listener received: " + message);
            latch.countDown();
        });
        
        client.sendMessage("Listener test");
        latch.await(10, TimeUnit.SECONDS);
        client.stopListener();
    }
}
```

### Performance Testing

```java
public void testPerformance() throws Exception {
    try (RabbitMQClient client = new RabbitMQClient(consumerConfig, producerConfig)) {
        
        int messageCount = 1000;
        long startTime = System.currentTimeMillis();
        
        // Send messages
        for (int i = 0; i < messageCount; i++) {
            client.sendMessage("Performance test message " + i);
        }
        
        long sendTime = System.currentTimeMillis() - startTime;
        System.out.println("Sent " + messageCount + " messages in " + sendTime + "ms");
        
        // Receive messages
        startTime = System.currentTimeMillis();
        AtomicInteger receivedCount = new AtomicInteger(0);
        
        List<RabbitMQClient.RabbitMQMessage> messages = client.waitForMessages(messageCount, 60000, message -> {
            receivedCount.incrementAndGet();
        });
        
        long receiveTime = System.currentTimeMillis() - startTime;
        System.out.println("Received " + messages.size() + " messages in " + receiveTime + "ms");
    }
}
```

## Error Handling

The RabbitMQClient provides comprehensive error handling:

```java
try {
    rabbitMQClient.sendMessage("Test message");
} catch (Exception e) {
    System.err.println("RabbitMQ Error: " + e.getMessage());
    // Handle specific RabbitMQ exceptions
    if (e.getCause() instanceof java.net.ConnectException) {
        System.err.println("Connection failed - check RabbitMQ server");
    }
}
```

## Best Practices

1. **Resource Management**: Always use try-with-resources or explicitly call `close()`
2. **Timeout Configuration**: Set appropriate timeouts for receive operations
3. **Error Handling**: Implement proper exception handling for RabbitMQ operations
4. **Configuration Validation**: Validate connection parameters before use
5. **Testing Isolation**: Use separate exchanges/queues for different test scenarios

## Troubleshooting

### Common Issues

1. **Connection Failures**: Verify host, port, and virtual host configuration
2. **Authentication Errors**: Check username/password and authentication type
3. **SSL/TLS Issues**: Verify SSL configuration and certificate setup
4. **Queue/Exchange Access**: Ensure queues and exchanges exist and user has appropriate permissions
5. **Message Acknowledgment**: Configure appropriate acknowledge mode for your use case

### Debug Logging

Enable debug logging to troubleshoot issues:

```java
// Add to logback.xml or application.properties
<logger name="com.dell.it.hip.client" level="DEBUG"/>
<logger name="com.rabbitmq" level="DEBUG"/>
```

## Compatibility

The RabbitMQClient is designed to be compatible with:
- RabbitMQ 3.8+
- Java 11+
- Spring AMQP 2.x+
- HIP Services Framework

## Dependencies

Required dependencies are already included in the HIP services project:
- RabbitMQ Java Client
- Spring AMQP
- SLF4J Logging
