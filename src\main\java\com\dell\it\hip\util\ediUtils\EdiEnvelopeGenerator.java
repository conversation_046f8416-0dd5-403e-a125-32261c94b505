package com.dell.it.hip.util.ediUtils;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.StringReader;
import java.io.StringWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

import org.redisson.api.RAtomicLong;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import io.xlate.edi.schema.Schema;
import io.xlate.edi.schema.SchemaFactory;
import io.xlate.edi.stream.EDIInputFactory;
import io.xlate.edi.stream.EDIOutputFactory;
import io.xlate.edi.stream.EDIStreamEvent;
import io.xlate.edi.stream.EDIStreamException;
import io.xlate.edi.stream.EDIStreamReader;
import io.xlate.edi.stream.EDIStreamWriter;

public class EdiEnvelopeGenerator {
	private final RedissonClient redisson;
	private final String baseKey;

	// Configuration
	private final String isaSenderId;
	private final String isaReceiverId;
	private final String isaQualifier;
	private final String gsFunctionalCode;
	private final int controlNumberLength;

	public EdiEnvelopeGenerator(RedissonClient redisson, String baseKey,
			String isaSenderId, String isaReceiverId,
			String isaQualifier, String gsFunctionalCode) throws Exception {
		this.redisson = redisson;
		this.baseKey = baseKey;
		this.isaSenderId = isaSenderId;
		this.isaReceiverId = isaReceiverId;
		this.isaQualifier = isaQualifier;
		this.gsFunctionalCode = gsFunctionalCode;
		this.controlNumberLength = 9;
	}

	/**
	 * Wraps raw EDI content with ISA/GS envelopes if missing
	 */
	public String ensureEnvelope(String ediContent) throws Exception {
		if (hasCompleteEnvelope(ediContent)) {
			return ediContent; // Return as-is if already has envelope
		}

		// Generate control numbers
		ControlNumbers controls = generateControlNumbers();
		EDIOutputFactory outputFactory = EDIOutputFactory.newFactory();
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		EDIStreamWriter ediWriter = outputFactory.createEDIStreamWriter(stream);
		// Write ISA Segment
		writeIsaSegment(ediWriter, controls);
		// Write GS Segment
		writeGsSegment(ediWriter, controls);
		// Write the original content (should be ST through SE segments)
		writeContent(ediWriter, ediContent, controls);
		// Write GE Segment
		writeGeSegment(ediWriter, controls, 1); // Assuming 1 transaction
		// Write IEA Segment
		writeIeaSegment(ediWriter, controls);

		return stream.toString();
	}

	private boolean hasCompleteEnvelope(String ediContent) {
		String trimmed = ediContent.trim();
		return trimmed.startsWith("ISA") && trimmed.contains("IEA");
	}

	private void writeIsaSegment(EDIStreamWriter writer, ControlNumbers controls) 
			throws EDIStreamException {

		writer.writeStartSegment("ISA")
		.writeElement("00") // Authorization qualifier
		.writeElement("")   // Authorization info
		.writeElement("00") // Security qualifier
		.writeElement("")   // Security info
		.writeElement(isaSenderId)
		.writeElement(isaQualifier)
		.writeElement(isaReceiverId)
		.writeElement(isaQualifier)
		.writeElement(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMdd")))
		.writeElement(LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmm")))
		.writeElement("U")  // Standards ID
		.writeElement("00401") // Version
		.writeElement(controls.isaControlNumber)
		.writeElement("0")  // Ack request
		.writeElement("P")  // Usage indicator
		.writeElement(">")  // Component separator
		.writeEndSegment();
	}

	private void writeGsSegment(EDIStreamWriter writer, ControlNumbers controls) 
			throws EDIStreamException {

		writer.writeStartSegment("GS")
		.writeElement(gsFunctionalCode)
		.writeElement(isaSenderId)
		.writeElement(isaReceiverId)
		.writeElement(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")))
		.writeElement(LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmm")))
		.writeElement(controls.gsControlNumber)
		.writeElement("X")  // Responsible agency
		.writeElement("004010") // Version
		.writeEndSegment();
	}

	private void writeContent(EDIStreamWriter writer, String content, ControlNumbers controls) 
			throws Exception {

		// Parse the original content to ensure it's valid
			//StringReader reader = new StringReader(content);
			EDIInputFactory inputFactory = EDIInputFactory.newFactory();
			ByteArrayInputStream reader = new ByteArrayInputStream(content.getBytes());
			EDIStreamReader contentReader = inputFactory.createEDIStreamReader(reader); 

			while (contentReader.hasNext()) {
				EDIStreamEvent event = contentReader.next();
				switch (event) {
				case START_SEGMENT:
					writer.writeStartSegment(contentReader.getText());
					break;
				case END_SEGMENT:
					writer.writeEndSegment();
					break;
				case START_COMPOSITE:
					writer.writeStartElement();
					break;
				case END_COMPOSITE:
					writer.writeEndSegment();
					break;
				case ELEMENT_DATA:
					writer.writeElement(contentReader.getText());
					break;
				}
			}
	}

	private void writeGeSegment(EDIStreamWriter writer, ControlNumbers controls, int transactionCount) 
			throws EDIStreamException {

		writer.writeStartSegment("GE")
		.writeElement(String.valueOf(transactionCount))
		.writeElement(controls.gsControlNumber)
		.writeEndSegment();
	}

	private void writeIeaSegment(EDIStreamWriter writer, ControlNumbers controls) 
			throws EDIStreamException {

		writer.writeStartSegment("IEA")
		.writeElement("1")  // Number of groups
		.writeElement(controls.isaControlNumber)
		.writeEndSegment();
	}

	private ControlNumbers generateControlNumbers() throws InterruptedException {
		String isaControlNumber = generateNumber("isa", controlNumberLength);
		String gsControlNumber = generateNumber("gs:" + isaControlNumber, controlNumberLength);

		return new ControlNumbers(
				isaControlNumber,
				gsControlNumber,
				null, // ST control number not needed at envelope level
				isaSenderId,
				isaReceiverId,
				LocalDateTime.now()
				);
	}

	private String generateNumber(String keySuffix, int length) throws InterruptedException {
		String lockKey = baseKey + ":lock:" + keySuffix;
		RLock lock = redisson.getLock(lockKey);

		try {
			if (!lock.tryLock(5, 30, TimeUnit.SECONDS)) {
				throw new RuntimeException("Failed to acquire lock for: " + keySuffix);
			}

			RAtomicLong counter = redisson.getAtomicLong(baseKey + ":" + keySuffix);
			return String.format("%0" + length + "d", counter.getAndIncrement());
		} finally {
			lock.unlock();
		}
	}

	private static class ControlNumbers {
		final String isaControlNumber;
		final String gsControlNumber;
		final String stControlNumber;
		final String senderId;
		final String receiverId;
		final LocalDateTime timestamp;

		ControlNumbers(String isa, String gs, String st, 
				String sender, String receiver, LocalDateTime ts) {
			this.isaControlNumber = isa;
			this.gsControlNumber = gs;
			this.stControlNumber = st;
			this.senderId = sender;
			this.receiverId = receiver;
			this.timestamp = ts;
		}
	}
}