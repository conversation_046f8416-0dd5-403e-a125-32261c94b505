package com.dell.it.hip.util.logging;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

@Configuration
public class HIPAsyncConfig {

    @Value("${hip.wiretap.threadpool.core-size:2}")
    private int corePoolSize;

    @Value("${hip.wiretap.threadpool.max-size:4}")
    private int maxPoolSize;

    @Value("${hip.wiretap.threadpool.queue-capacity:500}")
    private int queueCapacity;

    @Value("${hip.wiretap.threadpool.keep-alive-seconds:30}")
    private int keepAliveSeconds;

    @Bean("hipWiretapExecutor")
    @Primary
    public ThreadPoolTaskExecutor hipWiretapExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setThreadNamePrefix("hip-wiretap-");
        executor.initialize();
        return executor;
    }
}