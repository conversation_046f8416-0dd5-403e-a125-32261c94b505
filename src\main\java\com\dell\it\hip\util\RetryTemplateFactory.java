package com.dell.it.hip.util;

import org.springframework.context.annotation.Bean;
import org.springframework.retry.backoff.FixedBackOffPolicy;
import org.springframework.retry.policy.SimpleRetryPolicy;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.RetrySettings;

@Component
public class RetryTemplateFactory {

	
    public RetryTemplate create(RetrySettings settings) {
        RetryTemplate template = new RetryTemplate();

        // Retry policy
        SimpleRetryPolicy retryPolicy = new SimpleRetryPolicy();
        retryPolicy.setMaxAttempts(settings != null ? settings.getMaxAttempts() : 3);

        // Backoff policy
        FixedBackOffPolicy backOffPolicy = new FixedBackOffPolicy();
        backOffPolicy.setBackOffPeriod(settings != null ? settings.getBackOffPeriodMs() : 1000);

        template.setRetryPolicy(retryPolicy);
        template.setBackOffPolicy(backOffPolicy);

        return template;
    }
}