package com.dell.it.hip.strategy.adapters;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.util.ThrottleSettings;
public interface InputAdapterStrategy {

    String getType();

    // Build all producers for a definition (at registration)
    void buildProducers(HIPIntegrationDefinition def);

    // Build a single producer for a definition + configRef
    void buildProducer(HIPIntegrationDefinition def, AdapterConfigRef ref);

    // Pause/resume all adapters of this type for the integration
    void pause(HIPIntegrationDefinition def);
    void resume(HIPIntegrationDefinition def);

    // Pause/resume a specific adapter instance (by config ref)
    void pause(HIPIntegrationDefinition def, AdapterConfigRef ref);
    void resume(HIPIntegrationDefinition def, AdapterConfigRef ref);

    // Shutdown a specific adapter instance (by config ref)
    void shutdown(HIPIntegrationDefinition def, AdapterConfigRef ref);

    // Shutdown all adapters for a definition
    void shutdown(HIPIntegrationDefinition def);

    // Update throttle settings for this adapter type/integration
    void updateThrottle(HIPIntegrationDefinition def, ThrottleSettings throttleSettings);
    void updateThrottle(HIPIntegrationDefinition def, AdapterConfigRef ref, ThrottleSettings settings);

    // Default lifecycle and cleanup hooks
    default void dispose() {}
    default void stopAll() {}
    default void startAll() {}
}