package com.dell.it.hip.util;

public class LocalThrottleWindow {
    private long lastResetTime;
    private int tokens;
    private int ratePerSecond;

    public LocalThrottleWindow(int ratePerSecond) {
        this.ratePerSecond = ratePerSecond;
        this.tokens = ratePerSecond;
        this.lastResetTime = System.currentTimeMillis();
    }

    /** Check if current quota is exceeded. */
    public synchronized boolean isQuotaExceeded() {
        refresh();
        return tokens <= 0;
    }

    /** Try to increment/consume a token. Returns true if allowed. */
    public synchronized boolean increment() {
        refresh();
        if (tokens > 0) {
            tokens--;
            return true;
        }
        return false;
    }

    /** Reset token bucket if interval elapsed. */
    private void refresh() {
        long now = System.currentTimeMillis();
        if (now - lastResetTime > 1000) {
            tokens = ratePerSecond;
            lastResetTime = now;
        }
    }

    /** Change the throttle config at runtime. */
    public synchronized void updateConfig(int newRatePerSecond) {
        this.ratePerSecond = newRatePerSecond;
        refresh();
    }
}