package com.dell.it.hip.config.FlowSteps;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;

import java.io.IOException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@JsonDeserialize(using = SplitterFlowStepConfigDeserializer.class)
public class SplitterFlowStepConfig extends FlowStepConfig {

    private String stepName;

    // List of docType/dataFormat configs for this splitter
    @JsonProperty("configurationList")
    private List<SplitterDocTypeConfig> docTypeSplitConfigs = new ArrayList<>();

    // Default fallback
    private DefaultSplitterConfig defaultConfig;

    private boolean copyHeaders = true;

    // Getters & Setters...
    public String getStepName() { return stepName; }
    public void setStepName(String stepName) { this.stepName = stepName; }
    public List<SplitterDocTypeConfig> getDocTypeSplitConfigs() { return docTypeSplitConfigs; }
    public void setDocTypeSplitConfigs(List<SplitterDocTypeConfig> docTypeSplitConfigs) { this.docTypeSplitConfigs = docTypeSplitConfigs; }
    public DefaultSplitterConfig getDefaultConfig() { return defaultConfig; }
    public void setDefaultConfig(DefaultSplitterConfig defaultConfig) { this.defaultConfig = defaultConfig; }
    public boolean isCopyHeaders() { return copyHeaders; }
    public void setCopyHeaders(boolean copyHeaders) { this.copyHeaders = copyHeaders; }
    public SplitterDocTypeConfig findBestConfig(String docTypeName, String docTypeVersion, String dataFormat) {
        // First: try to find exact match
        for (SplitterDocTypeConfig config : docTypeSplitConfigs) {
            if (config.getName().equalsIgnoreCase(docTypeName)
                    && config.getVersion().equalsIgnoreCase(docTypeVersion)
                    && (dataFormat == null || dataFormat.equalsIgnoreCase(config.getDataFormat()))) {
                return config;
            }
        }
        // Second: try to find by docType (ignore version)
        for (SplitterDocTypeConfig config : docTypeSplitConfigs) {
            if (config.getName().equalsIgnoreCase(docTypeName)
                    && (dataFormat == null || dataFormat.equalsIgnoreCase(config.getDataFormat()))) {
                return config;
            }
        }
        // Fallback: return default if present
        if (defaultConfig != null) return defaultConfig;
        return null;
    }
}

/**
 * Custom deserializer to handle dot notation with array indices like:
 * "configurationList[0].documentName": "INVOICE"
 * "configurationList[0].documentVersion": "1.0"
 * "configurationList[0].dataFormat": "EDI_X12"
 *
 * Converts them to proper List<DocTypeConfig> structure.
 */
class SplitterFlowStepConfigDeserializer extends JsonDeserializer<SplitterFlowStepConfig> {

    private static final Pattern DOT_NOTATION_PATTERN =
        Pattern.compile("configurationList\\[(\\d+)\\]\\.(.+)");

    @Override
    public SplitterFlowStepConfig deserialize(JsonParser parser, DeserializationContext context)
            throws IOException, JsonProcessingException {

        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = parser.getCodec().readTree(parser);

        SplitterFlowStepConfig config = new SplitterFlowStepConfig();

        // Map to hold DocTypeConfig objects by index
        Map<Integer, SplitterDocTypeConfig> configMap = new HashMap<>();

        Iterator<Map.Entry<String, JsonNode>> fields = rootNode.fields();
        while (fields.hasNext()) {
            Map.Entry<String, JsonNode> field = fields.next();
            String fieldName = field.getKey();
            JsonNode fieldValue = field.getValue();

            Matcher matcher = DOT_NOTATION_PATTERN.matcher(fieldName);
            if (matcher.matches()) {
                // Handle dot notation for configurationList
                int index = Integer.parseInt(matcher.group(1));
                String propertyName = matcher.group(2);

                // Get or create DocTypeConfig for this index
                SplitterDocTypeConfig docTypeConfig = configMap.computeIfAbsent(index, k -> new SplitterDocTypeConfig());

                // Set the property on the DocTypeConfig
                setDocTypeConfigProperty(docTypeConfig, propertyName, fieldValue);

            } else {
                // Handle regular properties of SplitterFlowStepConfig
                switch (fieldName) {
                    case "stepName":
                        config.setStepName(fieldValue.asText());
                        break;
                    case "copyHeaders":
                        config.setCopyHeaders(fieldValue.asBoolean());
                        break;
                    case "defaultConfig":
                        if (!fieldValue.isNull()) {
                            DefaultSplitterConfig defaultConfig = objectMapper.treeToValue(fieldValue, DefaultSplitterConfig.class);
                            config.setDefaultConfig(defaultConfig);
                        }
                        break;
                    // Add other properties from parent class FlowStepConfig if needed
                    case "type":
                        config.setType(fieldValue.asText());
                        break;
                    case "id":
                        config.setId(fieldValue.asText());
                        break;
                    case "propertyRef":
                        config.setPropertyRef(fieldValue.asText());
                        break;
                    case "role":
                        config.setRole(fieldValue.asText());
                        break;
                }
            }
        }

        // Convert map to list and set on config
        List<SplitterDocTypeConfig> docTypeSplitConfigs = new ArrayList<>();
        for (int i = 0; i < configMap.size(); i++) {
        	SplitterDocTypeConfig docTypeConfig = configMap.get(i);
            if (docTypeConfig != null) {
                docTypeSplitConfigs.add(docTypeConfig);
            }
        }
        config.setDocTypeSplitConfigs(docTypeSplitConfigs);

        return config;
    }

    private void setDocTypeConfigProperty(SplitterDocTypeConfig docTypeConfig, String propertyName, JsonNode fieldValue) {
        switch (propertyName) {
            case "documentName":
                docTypeConfig.setName(fieldValue.asText());
                break;
            case "documentVersion":
                docTypeConfig.setVersion(fieldValue.asText());
                break;
            case "documentTypeId":
                docTypeConfig.setDocumentTypeId(fieldValue.asInt());
                break;
            case "dataFormat":
                docTypeConfig.setDataFormat(fieldValue.asText());
                break;
            case "action":
                docTypeConfig.setAction(fieldValue.asText());
                break;
            case "regexExpression":
                docTypeConfig.setRegexExpression(fieldValue.asText());
                break;
            // EDI X12 properties
            case "x12SplitLevel":
                docTypeConfig.setX12SplitLevel(fieldValue.asText());
                break;
            case "x12SegmentDelimiter":
                docTypeConfig.setX12SegmentDelimiter(fieldValue.asText());
                break;
            case "x12ElementDelimiter":
                docTypeConfig.setX12ElementDelimiter(fieldValue.asText());
                break;
            case "x12SubElementDelimiter":
                docTypeConfig.setX12SubElementDelimiter(fieldValue.asText());
                break;
            case "allowMultipleInterchanges":
                docTypeConfig.setAllowMultipleInterchanges(fieldValue.asBoolean());
                break;
            // EDI EDIFACT properties
            case "edifactSplitLevel":
                docTypeConfig.setEdifactSplitLevel(fieldValue.asText());
                break;
            case "edifactSegmentDelimiter":
                docTypeConfig.setEdifactSegmentDelimiter(fieldValue.asText());
                break;
            case "edifactElementDelimiter":
                docTypeConfig.setEdifactElementDelimiter(fieldValue.asText());
                break;
            case "edifactSubElementDelimiter":
                docTypeConfig.setEdifactSubElementDelimiter(fieldValue.asText());
                break;
            case "allowMultipleEdifactInterchanges":
                docTypeConfig.setAllowMultipleEdifactInterchanges(fieldValue.asBoolean());
                break;
            // XML properties
            case "xmlXPathExpression":
                docTypeConfig.setXmlXPathExpression(fieldValue.asText());
                break;
            // JSON properties
            case "jsonPathExpression":
                docTypeConfig.setJsonPathExpression(fieldValue.asText());
                break;
            // CSV properties
            case "splitCsvLines":
                docTypeConfig.setSplitCsvLines(fieldValue.asBoolean());
                break;
            // Flat file properties
            case "flatFileExpression":
                docTypeConfig.setFlatFileExpression(fieldValue.asText());
                break;
            // Add other properties as needed
        }
    }
}