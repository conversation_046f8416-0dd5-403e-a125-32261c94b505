package com.dell.it.hip.client;

import org.apache.sshd.client.SshClient;
import org.apache.sshd.client.keyverifier.AcceptAllServerKeyVerifier;
import org.apache.sshd.client.session.ClientSession;
import org.apache.sshd.common.config.keys.FilePasswordProvider;
import org.apache.sshd.common.keyprovider.FileKeyPairProvider;
import org.apache.sshd.sftp.client.SftpClient.CloseableHandle;
import org.apache.sshd.sftp.client.SftpClient.DirEntry;
import org.apache.sshd.sftp.client.SftpClient.OpenMode;
import org.apache.sshd.sftp.client.SftpClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.dell.it.hip.util.CompressionUtil;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Paths;
import java.security.KeyPair;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Pattern;

/**
 * SftpClient utility class for testing SFTP message send and receive operations.
 * This class mirrors the implementation patterns used in DynamicSFTPInputAdapter 
 * and DynamicSftpOutputHandler for compatibility with HIP services framework.
 * 
 * Usage:
 * 1. Create SftpClient with consumer and producer configurations
 * 2. Use sendMessage() to test handler functionality
 * 3. Use receiveMessage() to test adapter functionality
 * 4. Use startListener() for continuous file monitoring
 */
public class SftpClient implements AutoCloseable {
    
    private static final Logger logger = LoggerFactory.getLogger(SftpClient.class);
    
    // Configuration for consumer (adapter testing)
    private final SftpClientConfig consumerConfig;
    
    // Configuration for producer (handler testing)  
    private final SftpClientConfig producerConfig;
    
    // Connection management
    private SshClient consumerSshClient;
    private ClientSession consumerSession;
    private SshClient producerSshClient;
    private ClientSession producerSession;
    private ExecutorService listenerExecutor;
    private final AtomicBoolean listenerRunning = new AtomicBoolean(false);
    
    public SftpClient(SftpClientConfig consumerConfig, SftpClientConfig producerConfig) {
        this.consumerConfig = consumerConfig;
        this.producerConfig = producerConfig;
    }
    
    /**
     * Send a message to SFTP using the same approach as DynamicSftpOutputHandler
     */
    public void sendMessage(String fileName, String message) throws Exception {
        sendMessage(fileName, message.getBytes(StandardCharsets.UTF_8));
    }
    
    /**
     * Send a byte array message to SFTP using the same approach as DynamicSftpOutputHandler
     */
    public void sendMessage(String fileName, byte[] payload) throws Exception {
        logger.info("Sending message to SFTP host: {}, directory: {}, file: {}", 
                   producerConfig.getHost(), producerConfig.getRemoteDirectory(), fileName);
        
        org.apache.sshd.sftp.client.SftpClient sftp = getOrCreateProducerSftpClient();
        
        try {
            // Apply compression if enabled
            byte[] finalPayload = Boolean.TRUE.equals(producerConfig.getGzipEnabled())
                    ? CompressionUtil.compress(payload)
                    : payload;
            
            String dir = producerConfig.getRemoteDirectory() != null ? producerConfig.getRemoteDirectory() : ".";
            
            // Check if file already exists
            boolean exists = false;
            try {
                for (DirEntry entry : sftp.readDir(dir)) {
                    if (entry.getFilename().equals(fileName)) {
                        exists = true;
                        break;
                    }
                }
            } catch (Exception ex) {
                logger.warn("Could not check directory contents: {}", ex.getMessage());
            }
            
            if (exists) {
                throw new RuntimeException("SFTP upload aborted: File already exists: " + fileName);
            }
            
            // File does not exist, write it
            String remotePath = dir.endsWith("/") ? dir + fileName : dir + "/" + fileName;
            EnumSet<OpenMode> openModes = EnumSet.of(OpenMode.Write, OpenMode.Create);
            
            try (CloseableHandle handle = sftp.open(remotePath, openModes)) {
                sftp.write(handle, 0L, finalPayload, 0, finalPayload.length);
            }
            
            logger.info("SFTP upload successful: {}", fileName);
            
        } catch (Exception ex) {
            logger.error("SFTP upload failed: {}", ex.getMessage(), ex);
            throw ex;
        }
    }
    
    /**
     * Receive a single file from SFTP using the same approach as DynamicSFTPInputAdapter
     */
    public SftpMessage receiveMessage() throws Exception {
        return receiveMessage(null);
    }
    
    /**
     * Receive a single file from SFTP with optional file pattern filter
     */
    public SftpMessage receiveMessage(String filePattern) throws Exception {
        logger.info("Receiving message from SFTP host: {}, directory: {}", 
                   consumerConfig.getHost(), consumerConfig.getRemoteDirectory());
        
        org.apache.sshd.sftp.client.SftpClient sftp = getOrCreateConsumerSftpClient();
        
        try {
            String remoteDir = consumerConfig.getRemoteDirectory() != null ? consumerConfig.getRemoteDirectory() : ".";
            String pattern = filePattern != null ? filePattern : consumerConfig.getFileNamePattern();
            String charset = consumerConfig.getCharset() != null ? consumerConfig.getCharset() : "UTF-8";
            
            // List files in directory
            Iterable<DirEntry> entries = sftp.readDir(remoteDir);
            
            for (DirEntry entry : entries) {
                String fileName = entry.getFilename();
                
                // Skip directories and special entries
                if (entry.getAttributes().isDirectory() || fileName.equals(".") || fileName.equals("..")) {
                    continue;
                }
                
                // Apply file pattern filter if specified
                if (pattern != null && !pattern.equals("*") && !matchesPattern(fileName, pattern)) {
                    continue;
                }
                
                // Read file content
                String filePath = remoteDir.endsWith("/") ? remoteDir + fileName : remoteDir + "/" + fileName;
                
                try (CloseableHandle handle = sftp.open(filePath, EnumSet.of(OpenMode.Read))) {
                    ByteArrayOutputStream out = new ByteArrayOutputStream();
                    byte[] buf = new byte[8192];
                    int read;
                    long offset = 0;
                    
                    while ((read = sftp.read(handle, offset, buf, 0, buf.length)) > 0) {
                        out.write(buf, 0, read);
                        offset += read;
                    }
                    
                    byte[] fileContent = out.toByteArray();
                    
                    // Apply decompression if configured
                    if (consumerConfig.isCompressed()) {
                        fileContent = CompressionUtil.decompress(fileContent);
                    }
                    
                    // Handle post-processing (delete/rename)
                    handlePostProcess(sftp, remoteDir, fileName);
                    
                    String content = new String(fileContent, charset);
                    
                    logger.info("SFTP file received successfully: {}", fileName);
                    
                    return new SftpMessage(content, fileName, entry.getAttributes().getSize(), 
                                         entry.getAttributes().getModifyTime().toMillis(), new HashMap<>());
                }
            }
            
            logger.info("No files found matching pattern: {}", pattern);
            return null;
            
        } catch (Exception ex) {
            logger.error("SFTP receive failed: {}", ex.getMessage(), ex);
            throw ex;
        }
    }
    
    /**
     * Start a continuous file listener using the same approach as DynamicSFTPInputAdapter
     */
    public void startListener(MessageHandler messageHandler) throws Exception {
        if (listenerRunning.get()) {
            logger.warn("Listener is already running");
            return;
        }
        
        logger.info("Starting SFTP listener for host: {}, directory: {}", 
                   consumerConfig.getHost(), consumerConfig.getRemoteDirectory());
        
        listenerExecutor = Executors.newSingleThreadExecutor();
        listenerRunning.set(true);
        
        listenerExecutor.submit(() -> {
            while (listenerRunning.get()) {
                try {
                    SftpMessage message = receiveMessage();
                    if (message != null) {
                        try {
                            messageHandler.handleMessage(message);
                        } catch (Exception ex) {
                            logger.error("Error handling message: {}", ex.getMessage(), ex);
                        }
                    }
                    
                    // Wait for polling interval
                    Thread.sleep(consumerConfig.getPollingIntervalMs());
                    
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception ex) {
                    if (listenerRunning.get()) {
                        logger.error("Error in SFTP listener: {}", ex.getMessage(), ex);
                        try {
                            Thread.sleep(5000); // Wait before retrying
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }
            }
        });
        
        logger.info("SFTP listener started");
    }
    
    /**
     * Stop the message listener
     */
    public void stopListener() {
        if (!listenerRunning.get()) {
            return;
        }
        
        listenerRunning.set(false);
        
        if (listenerExecutor != null) {
            listenerExecutor.shutdown();
            try {
                if (!listenerExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    listenerExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                listenerExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        logger.info("SFTP listener stopped");
    }
    
    /**
     * Wait for a specific number of files with timeout
     */
    public List<SftpMessage> waitForMessages(int expectedCount, long timeoutMs, MessageHandler messageHandler) throws Exception {
        CountDownLatch latch = new CountDownLatch(expectedCount);
        List<SftpMessage> receivedMessages = Collections.synchronizedList(new ArrayList<>());
        
        startListener(message -> {
            receivedMessages.add(message);
            messageHandler.handleMessage(message);
            latch.countDown();
        });
        
        boolean received = latch.await(timeoutMs, TimeUnit.MILLISECONDS);
        stopListener();
        
        if (!received) {
            throw new RuntimeException("Did not receive expected " + expectedCount + " files within " + timeoutMs + "ms");
        }
        
        logger.info("Successfully received {} files", expectedCount);
        return receivedMessages;
    }
    
    /**
     * Close all connections and clean up resources
     */
    @Override
    public void close() {
        stopListener();
        
        if (consumerSession != null) {
            try { consumerSession.close(); } catch (Exception ignored) {}
        }
        if (consumerSshClient != null) {
            try { consumerSshClient.stop(); } catch (Exception ignored) {}
        }
        if (producerSession != null) {
            try { producerSession.close(); } catch (Exception ignored) {}
        }
        if (producerSshClient != null) {
            try { producerSshClient.stop(); } catch (Exception ignored) {}
        }
        
        logger.info("SftpClient closed");
    }

    // Private helper methods

    private org.apache.sshd.sftp.client.SftpClient getOrCreateConsumerSftpClient() throws Exception {
        if (consumerSshClient == null || consumerSession == null || !consumerSession.isOpen()) {
            createConsumerConnection();
        }
        return SftpClientFactory.instance().createSftpClient(consumerSession);
    }

    private org.apache.sshd.sftp.client.SftpClient getOrCreateProducerSftpClient() throws Exception {
        if (producerSshClient == null || producerSession == null || !producerSession.isOpen()) {
            createProducerConnection();
        }
        return SftpClientFactory.instance().createSftpClient(producerSession);
    }

    private void createConsumerConnection() throws Exception {
        if (consumerSshClient != null) {
            try { consumerSshClient.stop(); } catch (Exception ignored) {}
        }

        consumerSshClient = SshClient.setUpDefaultClient();
        consumerSshClient.setServerKeyVerifier(AcceptAllServerKeyVerifier.INSTANCE); // For demo/lab only!
        consumerSshClient.start();

        consumerSession = consumerSshClient.connect(consumerConfig.getUsername(),
                                                   consumerConfig.getHost(),
                                                   consumerConfig.getPort())
                .verify(consumerConfig.getTimeout())
                .getSession();

        // Key-based authentication if privateKey or privateKeyPath is set
        if ((consumerConfig.getPrivateKey() != null && !consumerConfig.getPrivateKey().isEmpty()) ||
            (consumerConfig.getPrivateKeyPath() != null && !consumerConfig.getPrivateKeyPath().isEmpty())) {

            String keyPath = consumerConfig.getPrivateKeyPath() != null ?
                           consumerConfig.getPrivateKeyPath() : consumerConfig.getPrivateKey();

            File privateKeyFile = new File(keyPath);
            FileKeyPairProvider keyPairProvider = new FileKeyPairProvider(privateKeyFile.toPath());

            if (consumerConfig.getPrivateKeyPassphrase() != null && !consumerConfig.getPrivateKeyPassphrase().isEmpty()) {
                keyPairProvider.setPasswordFinder(FilePasswordProvider.of(consumerConfig.getPrivateKeyPassphrase()));
            }

            Iterable<KeyPair> keyPairs = keyPairProvider.loadKeys(null);
            boolean added = false;
            for (KeyPair keyPair : keyPairs) {
                consumerSession.addPublicKeyIdentity(keyPair);
                added = true;
            }

            if (!added) {
                throw new IllegalArgumentException("No valid private key found at: " + keyPath);
            }
        } else {
            // Password authentication fallback
            consumerSession.addPasswordIdentity(consumerConfig.getPassword());
        }

        consumerSession.auth().verify(consumerConfig.getTimeout());
    }

    private void createProducerConnection() throws Exception {
        if (producerSshClient != null) {
            try { producerSshClient.stop(); } catch (Exception ignored) {}
        }

        producerSshClient = SshClient.setUpDefaultClient();
        producerSshClient.setServerKeyVerifier(AcceptAllServerKeyVerifier.INSTANCE); // For demo/lab only!
        producerSshClient.start();

        producerSession = producerSshClient.connect(producerConfig.getUsername(),
                                                   producerConfig.getHost(),
                                                   producerConfig.getPort())
                .verify(producerConfig.getTimeout())
                .getSession();

        // Key-based authentication if privateKey or privateKeyPath is set
        if ((producerConfig.getPrivateKey() != null && !producerConfig.getPrivateKey().isEmpty()) ||
            (producerConfig.getPrivateKeyPath() != null && !producerConfig.getPrivateKeyPath().isEmpty())) {

            String keyPath = producerConfig.getPrivateKeyPath() != null ?
                           producerConfig.getPrivateKeyPath() : producerConfig.getPrivateKey();

            FileKeyPairProvider keyPairProvider = new FileKeyPairProvider(Paths.get(keyPath));
            Iterable<KeyPair> keyPairs = keyPairProvider.loadKeys(null);

            for (KeyPair kp : keyPairs) {
                producerSession.addPublicKeyIdentity(kp);
            }
        }

        // Password authentication if password is set
        if (producerConfig.getPassword() != null && !producerConfig.getPassword().isEmpty()) {
            producerSession.addPasswordIdentity(producerConfig.getPassword());
        }

        producerSession.auth().verify(producerConfig.getTimeout());
    }

    private boolean matchesPattern(String fileName, String pattern) {
        if (pattern == null || pattern.equals("*")) {
            return true;
        }

        // Convert glob pattern to regex
        String regex = pattern.replace(".", "\\.")
                             .replace("*", ".*")
                             .replace("?", ".");

        return Pattern.matches(regex, fileName);
    }

    private void handlePostProcess(org.apache.sshd.sftp.client.SftpClient sftp, String remoteDir, String fileName) {
        try {
            String action = consumerConfig.getPostProcessAction();
            if ("delete".equalsIgnoreCase(action)) {
                String filePath = remoteDir.endsWith("/") ? remoteDir + fileName : remoteDir + "/" + fileName;
                sftp.remove(filePath);
                logger.info("Deleted SFTP file: {}", fileName);
            } else if ("rename".equalsIgnoreCase(action)) {
                String newFileName = (consumerConfig.getRenamePattern() != null)
                        ? consumerConfig.getRenamePattern().replace("{file}", fileName)
                        : fileName + ".processed";
                String oldPath = remoteDir.endsWith("/") ? remoteDir + fileName : remoteDir + "/" + fileName;
                String newPath = remoteDir.endsWith("/") ? remoteDir + newFileName : remoteDir + "/" + newFileName;
                sftp.rename(oldPath, newPath);
                logger.info("Renamed SFTP file: {} -> {}", fileName, newFileName);
            }
        } catch (Exception ex) {
            logger.warn("Post-process failed for file {}: {}", fileName, ex.getMessage());
        }
    }

    /**
     * Functional interface for handling received messages
     */
    @FunctionalInterface
    public interface MessageHandler {
        void handleMessage(SftpMessage message);
    }

    /**
     * Represents an SFTP message with metadata
     */
    public static class SftpMessage {
        private final String content;
        private final String fileName;
        private final long fileSize;
        private final long lastModified;
        private final Map<String, String> headers;

        public SftpMessage(String content, String fileName, long fileSize, long lastModified, Map<String, String> headers) {
            this.content = content;
            this.fileName = fileName;
            this.fileSize = fileSize;
            this.lastModified = lastModified;
            this.headers = headers != null ? new HashMap<>(headers) : new HashMap<>();
        }

        public String getContent() { return content; }
        public String getFileName() { return fileName; }
        public long getFileSize() { return fileSize; }
        public long getLastModified() { return lastModified; }
        public Map<String, String> getHeaders() { return new HashMap<>(headers); }

        @Override
        public String toString() {
            return String.format("SftpMessage{fileName='%s', fileSize=%d, lastModified=%d, content='%s', headers=%s}",
                               fileName, fileSize, lastModified, content, headers);
        }
    }
}
