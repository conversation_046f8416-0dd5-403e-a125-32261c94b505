# Enhanced HIP Services Configuration
# This configuration includes comprehensive monitoring, security, and testing features

spring:
  application:
    name: hip-services
  
  # Database Configuration
  datasource:
    url: *********************************************
    username: ${DB_USERNAME:hip_user}
    password: ${DB_PASSWORD:hip_password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # JPA Configuration
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
  
  # Redis Configuration
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
  
  # Kafka Configuration
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:9092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      acks: all
      retries: 3
      batch-size: 16384
      linger-ms: 5
    consumer:
      group-id: hip-services
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      auto-offset-reset: earliest
      enable-auto-commit: false
  
  # Security Configuration
  security:
    user:
      name: ${ADMIN_USERNAME:admin}
      password: ${ADMIN_PASSWORD:admin123}
      roles: ADMIN

# HIP Platform Configuration
hip:
  # JWT Configuration
  jwt:
    secret: ${JWT_SECRET:hipSecretKey123456789012345678901234567890}
    expiration: ${JWT_EXPIRATION:86400000} # 24 hours
  
  # API Configuration
  api:
    version: 1.0.0
    title: HIP Integration Platform Services
    description: REST API for managing and executing integration flows
    server:
      url: ${API_SERVER_URL:http://localhost:8080}
      description: ${API_SERVER_DESCRIPTION:Development Server}
  
  # Health Check Configuration
  health:
    external-apis:
      - ${EXTERNAL_API_1:http://example.com/health}
      - ${EXTERNAL_API_2:http://api.example.com/status}
    sftp-servers:
      - ${SFTP_SERVER_1:sftp.example.com:22:testuser}
      - ${SFTP_SERVER_2:sftp2.example.com:22:testuser}
    sftp-timeout: 5000
  
  # Throttling Configuration
  throttling:
    default-max-requests: 100
    default-time-window: 60
    redis-key-ttl: 3600

# Actuator Configuration
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,env,configprops,loggers
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  health:
    redis:
      enabled: true
    db:
      enabled: true
  prometheus:
    metrics:
      export:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
      sla:
        http.server.requests: 100ms, 500ms, 1s

# Logging Configuration
logging:
  level:
    com.dell.it.hip: ${LOG_LEVEL:INFO}
    org.springframework.security: ${SECURITY_LOG_LEVEL:WARN}
    org.springframework.web: ${WEB_LOG_LEVEL:WARN}
    org.hibernate.SQL: ${SQL_LOG_LEVEL:WARN}
    org.hibernate.type.descriptor.sql.BasicBinder: ${SQL_PARAM_LOG_LEVEL:WARN}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{correlationId}] %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE:logs/hip-services.log}
    max-size: 100MB
    max-history: 30

# Server Configuration
server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: ${CONTEXT_PATH:}
  compression:
    enabled: true
    mime-types: application/json,application/xml,text/html,text/xml,text/plain
  error:
    include-stacktrace: on-param
    include-message: always

# Circuit Breaker Configuration (if using Resilience4j)
resilience4j:
  circuitbreaker:
    instances:
      external-api:
        register-health-indicator: true
        sliding-window-size: 10
        minimum-number-of-calls: 5
        permitted-number-of-calls-in-half-open-state: 3
        wait-duration-in-open-state: 30s
        failure-rate-threshold: 50
        slow-call-rate-threshold: 50
        slow-call-duration-threshold: 2s
  retry:
    instances:
      external-api:
        max-attempts: 3
        wait-duration: 1s
        exponential-backoff-multiplier: 2
  timelimiter:
    instances:
      external-api:
        timeout-duration: 5s

# Integration Testing Configuration
---
spring:
  config:
    activate:
      on-profile: test
  
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
  
  data:
    redis:
      host: hip-gscm-redis-dev.rds-a2-np.kob.dell.com
      port: 443
  
  kafka:
    bootstrap-servers: localhost:9092

hip:
  health:
    external-apis: []
    sftp-servers: []

logging:
  level:
    com.dell.it.hip: DEBUG
    org.springframework.test: DEBUG
