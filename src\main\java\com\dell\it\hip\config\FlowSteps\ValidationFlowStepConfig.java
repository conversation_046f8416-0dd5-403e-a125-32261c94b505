package com.dell.it.hip.config.FlowSteps;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ValidationFlowStepConfig extends FlowStepConfig {
    private boolean enableSchemaValidation = false;
    private String schemaKey;   // Key in Redis (or full path, if not in Redis)
    private String schemaType;  // XSD, JSON, EDI, CSV, etc.
    private String inputFormat; // Optional override ("XML", "JSON", etc.)
    private boolean failOnInvalid = true; // If false, allow passthrough even if validation fails
}