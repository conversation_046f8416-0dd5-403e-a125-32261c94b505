package com.dell.it.hip.client;

import com.ibm.mq.*;
import com.ibm.mq.constants.CMQC;
import com.ibm.mq.jakarta.jms.MQQueueConnectionFactory;
import com.ibm.msg.client.jakarta.wmq.WMQConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jms.listener.DefaultMessageListenerContainer;

import jakarta.jms.*;
import java.nio.charset.StandardCharsets;
import java.util.Hashtable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * MQClient utility class for testing IBM MQ message send and receive operations.
 * This class mirrors the implementation patterns used in DynamicIbmmqInputAdapter
 * and DynamicIbmmqOutputHandler for compatibility with HIP services framework.
 *
 * Usage:
 * 1. Create MQClient with consumer and producer configurations
 * 2. Use sendMessage() to test handler functionality
 * 3. Use receiveMessage() to test adapter functionality
 * 4. Use startListener() for continuous message consumption
 */
public class MQClient implements AutoCloseable {
    
    private static final Logger logger = LoggerFactory.getLogger(MQClient.class);
    
    // Configuration for consumer (adapter testing)
    private final MQClientConfig consumerConfig;
    
    // Configuration for producer (handler testing)  
    private final MQClientConfig producerConfig;
    
    // Connection management
    private MQQueueManager producerQueueManager;
    private MQQueue producerQueue;
    private DefaultMessageListenerContainer listenerContainer;
    
    public MQClient(MQClientConfig consumerConfig, MQClientConfig producerConfig) {
        this.consumerConfig = consumerConfig;
        this.producerConfig = producerConfig;
    }
    
    /**
     * Send a message to IBM MQ using the same approach as DynamicIbmmqOutputHandler
     */
    public void sendMessage(String message) throws Exception {
        sendMessage(message.getBytes(StandardCharsets.UTF_8));
    }
    
    /**
     * Send a byte array message to IBM MQ using the same approach as DynamicIbmmqOutputHandler
     */
    public void sendMessage(byte[] payload) throws Exception {
        logger.info("Sending message to IBM MQ queue: {}", producerConfig.getQueue());
        
        MQQueueManager qmgr = getOrCreateProducerQueueManager();
        MQQueue queue = getOrCreateProducerQueue(qmgr);
        
        MQMessage mqMessage = new MQMessage();
        
        // Set encoding and character set if provided in config
        if (producerConfig.getEncoding() != null) {
            mqMessage.encoding = producerConfig.getEncoding();
        }
        if (producerConfig.getCcsid() != null) {
            mqMessage.characterSet = producerConfig.getCcsid();
        }
        
        mqMessage.format = CMQC.MQFMT_STRING;
        if (Boolean.TRUE.equals(producerConfig.getPersistent())) {
            mqMessage.persistence = CMQC.MQPER_PERSISTENT;
        } else {
            mqMessage.persistence = CMQC.MQPER_NOT_PERSISTENT;
        }
        
        mqMessage.write(payload);
        
        MQPutMessageOptions pmo = new MQPutMessageOptions(); // default options
        
        try {
            queue.put(mqMessage, pmo);
            logger.info("Message sent successfully to queue: {}", producerConfig.getQueue());
        } catch (MQException ex) {
            logger.error("IBM MQ send failed: {}", ex.getMessage(), ex);
            throw ex;
        }
    }
    
    /**
     * Receive a single message from IBM MQ using the same approach as DynamicIbmmqInputAdapter
     */
    public String receiveMessage(long timeoutMs) throws Exception {
        logger.info("Receiving message from IBM MQ queue: {}", consumerConfig.getQueue());
        
        MQQueueConnectionFactory factory = createConsumerConnectionFactory();
        
        try (Connection connection = factory.createConnection();
             Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE)) {
            
            connection.start();
            Queue queue = session.createQueue(consumerConfig.getQueue());
            MessageConsumer consumer = session.createConsumer(queue);
            
            Message jmsMsg = consumer.receive(timeoutMs);
            if (jmsMsg == null) {
                logger.info("No message received within timeout: {}ms", timeoutMs);
                return null;
            }
            
            String messageContent = extractMessageContent(jmsMsg);
            logger.info("Message received successfully from queue: {}", consumerConfig.getQueue());
            return messageContent;
            
        } catch (Exception ex) {
            logger.error("IBM MQ receive failed: {}", ex.getMessage(), ex);
            throw ex;
        }
    }
    
    /**
     * Start a continuous message listener using the same approach as DynamicIbmmqInputAdapter
     */
    public void startListener(MessageHandler messageHandler) throws Exception {
        logger.info("Starting IBM MQ listener for queue: {}", consumerConfig.getQueue());
        
        MQQueueConnectionFactory factory = createConsumerConnectionFactory();
        
        listenerContainer = new DefaultMessageListenerContainer();
        listenerContainer.setConnectionFactory((ConnectionFactory) factory);
        listenerContainer.setDestinationName(consumerConfig.getQueue());
        
        if (consumerConfig.getConcurrency() != null) {
            listenerContainer.setConcurrentConsumers(consumerConfig.getConcurrency());
        }
        if (consumerConfig.getReceiveTimeout() != null) {
            listenerContainer.setReceiveTimeout(consumerConfig.getReceiveTimeout());
        }
        if (consumerConfig.getRecoveryInterval() != null) {
            listenerContainer.setRecoveryInterval(consumerConfig.getRecoveryInterval());
        }
        if (consumerConfig.getTransacted() != null) {
            listenerContainer.setSessionTransacted(consumerConfig.getTransacted());
        }
        
        listenerContainer.setMessageListener((MessageListener) jmsMsg -> {
            try {
                String messageContent = extractMessageContent(jmsMsg);
                messageHandler.handleMessage(messageContent);
            } catch (Exception ex) {
                logger.error("Error handling message: {}", ex.getMessage(), ex);
            }
        });
        
        listenerContainer.afterPropertiesSet();
        listenerContainer.start();
        logger.info("IBM MQ listener started for queue: {}", consumerConfig.getQueue());
    }
    
    /**
     * Stop the message listener
     */
    public void stopListener() {
        if (listenerContainer != null) {
            listenerContainer.stop();
            logger.info("IBM MQ listener stopped");
        }
    }
    
    /**
     * Wait for a specific number of messages with timeout
     */
    public void waitForMessages(int expectedCount, long timeoutMs, MessageHandler messageHandler) throws Exception {
        CountDownLatch latch = new CountDownLatch(expectedCount);
        
        startListener(message -> {
            messageHandler.handleMessage(message);
            latch.countDown();
        });
        
        boolean received = latch.await(timeoutMs, TimeUnit.MILLISECONDS);
        stopListener();
        
        if (!received) {
            throw new RuntimeException("Did not receive expected " + expectedCount + " messages within " + timeoutMs + "ms");
        }
        
        logger.info("Successfully received {} messages", expectedCount);
    }
    
    /**
     * Close all connections and clean up resources
     */
    public void close() {
        stopListener();
        
        if (producerQueue != null) {
            try { producerQueue.close(); } catch (Exception ignored) {}
        }
        if (producerQueueManager != null) {
            try { producerQueueManager.disconnect(); } catch (Exception ignored) {}
        }
        
        logger.info("MQClient closed");
    }
    
    // Private helper methods
    
    private MQQueueConnectionFactory createConsumerConnectionFactory() throws Exception {
        MQQueueConnectionFactory factory = new MQQueueConnectionFactory();
        
        factory.setQueueManager(consumerConfig.getQueueManager());
        factory.setChannel(consumerConfig.getChannel());
        if (consumerConfig.getConnName() != null) {
            factory.setConnectionNameList(consumerConfig.getConnName());
        }
        factory.setTransportType(WMQConstants.WMQ_CM_CLIENT);
        
        if (consumerConfig.getCcsid() != null) {
            factory.setCCSID(consumerConfig.getCcsid());
        }
        if (consumerConfig.getEncoding() != null) {
            factory.setIntProperty(WMQConstants.WMQ_ENCODING, consumerConfig.getEncoding());
        }
        
        // Authentication
        if (consumerConfig.getAuthenticationType() != null && !"none".equalsIgnoreCase(consumerConfig.getAuthenticationType())) {
            if (consumerConfig.getUsername() != null) {
                factory.setStringProperty(WMQConstants.USERID, consumerConfig.getUsername());
            }
            if (consumerConfig.getPassword() != null) {
                factory.setStringProperty(WMQConstants.PASSWORD, consumerConfig.getPassword());
            }
        }
        
        // SSL/TLS config if present
        if (consumerConfig.getSslCipherSuite() != null) {
            factory.setSSLCipherSuite(consumerConfig.getSslCipherSuite());
        }
        if (consumerConfig.getSslPeerName() != null) {
            factory.setSSLPeerName(consumerConfig.getSslPeerName());
        }
        
        return factory;
    }
    
    private MQQueueManager getOrCreateProducerQueueManager() throws MQException {
        if (producerQueueManager == null || !producerQueueManager.isConnected()) {
            Hashtable<String, Object> props = new Hashtable<>();
            props.put(CMQC.CHANNEL_PROPERTY, producerConfig.getChannel());
            props.put(CMQC.HOST_NAME_PROPERTY, producerConfig.getHost());
            props.put(CMQC.PORT_PROPERTY, producerConfig.getPort());
            props.put(CMQC.USER_ID_PROPERTY, producerConfig.getUsername());
            props.put(CMQC.PASSWORD_PROPERTY, producerConfig.getPassword());
            props.put(CMQC.TRANSPORT_PROPERTY, CMQC.TRANSPORT_MQSERIES_CLIENT);
            
            if (producerConfig.getCcsid() != null) {
                props.put(CMQC.CCSID_PROPERTY, producerConfig.getCcsid());
            }
            
            producerQueueManager = new MQQueueManager(producerConfig.getQueueManager(), props);
        }
        return producerQueueManager;
    }
    
    private MQQueue getOrCreateProducerQueue(MQQueueManager qmgr) throws MQException {
        if (producerQueue == null || !producerQueue.isOpen()) {
            int openOptions = CMQC.MQOO_OUTPUT;
            producerQueue = qmgr.accessQueue(producerConfig.getQueue(), openOptions);
        }
        return producerQueue;
    }
    
    private String extractMessageContent(Message jmsMsg) throws JMSException {
        if (jmsMsg instanceof BytesMessage bytesMessage) {
            byte[] payload = new byte[(int) bytesMessage.getBodyLength()];
            bytesMessage.readBytes(payload);
            return new String(payload, StandardCharsets.UTF_8);
        } else if (jmsMsg instanceof TextMessage textMessage) {
            return textMessage.getText();
        } else {
            logger.warn("Unsupported IBM MQ message type: {}", jmsMsg.getClass());
            return null;
        }
    }
    
    /**
     * Functional interface for handling received messages
     */
    @FunctionalInterface
    public interface MessageHandler {
        void handleMessage(String message);
    }
}
