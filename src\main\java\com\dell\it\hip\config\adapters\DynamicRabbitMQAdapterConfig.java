package com.dell.it.hip.config.adapters;
import java.util.List;
import java.util.Map;

import org.springframework.amqp.core.AcknowledgeMode;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DynamicRabbitMQAdapterConfig extends AdapterConfig {

    // RabbitMQ connection settings
	
	@JsonProperty("rabbitmq.consumer.host")
    private String host;
	
	@JsonProperty("rabbitmq.consumer.port")
    private Integer port;
	
	@JsonProperty("rabbitmq.consumer.vhost")
    private String virtualHost;
	
	@JsonProperty("rabbitmq.consumer.queueName")
    private String queueName;

    // Authentication
    @JsonProperty("rabbitmq.consumer.auth.type")
    private String authenticationType; // "BASIC", "TLS", "OAUTH2", etc

    @JsonProperty("rabbitmq.consumer.username")
    private String username;

    @JsonProperty("rabbitmq.consumer.password")
    private String password;

    // TLS/SSL properties
    @JsonProperty("rabbitmq.consumer.ssl.truststore.location")
    private String sslTruststoreLocation;

    @JsonProperty("rabbitmq.consumer.ssl.truststore.password")
    private String sslTruststorePassword;

    @JsonProperty("rabbitmq.consumer.ssl.keystore.location")
    private String sslKeystoreLocation;

    @JsonProperty("rabbitmq.consumer.ssl.keystore.password")
    private String sslKeystorePassword;

    @JsonProperty("rabbitmq.consumer.ssl.key.password")
    private String sslKeyPassword;

    // Consumer settings
    @JsonProperty("rabbitmq.consumer.concurrency")
    private Integer concurrency;

    @JsonProperty("rabbitmq.consumer.prefetchCount")
    private Integer prefetchCount;

    @JsonProperty("rabbitmq.consumer.acknowledgeMode")
    private AcknowledgeMode acknowledgeMode; // NONE, AUTO, MANUAL

    @JsonProperty("rabbitmq.consumer.channelCacheSize")
    private Integer channelCacheSize; // For connection pooling

    // Compression
    @JsonProperty("rabbitmq.consumer.compressed")
    private boolean compressed = false;

    // Header extraction
    @JsonProperty("rabbitmq.consumer.headersToExtract")
    private List<String> headersToExtract;

    // Custom/Advanced
    @JsonProperty("rabbitmq.consumer.properties")
    private Map<String, ?> properties;

    // (optional) Message converter
    @JsonProperty("rabbitmq.consumer.messageConverterClass")
    private String messageConverterClass;

    // Override parent's properties method to provide specific type
    @Override
    public void setProperties(Map<String, ?> properties) {
        super.setProperties(properties);
    }

    // Convenience method for String-specific properties
    public void setStringProperties(Map<String, String> properties) {
        super.setProperties(properties);
    }

    // --- Getters and Setters ---

    // ...all getters and setters for above fields...

}