package com.dell.it.hip.util.dataformatUtils;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import io.xlate.edi.stream.EDIInputFactory;
import io.xlate.edi.stream.EDIStreamEvent;
import io.xlate.edi.stream.EDIStreamReader;


/**
 * Utility for extracting fields from EDI X12 using STAEDI.
 */
public class StaediUtil {
    /**
     * Extract a field value from EDI using a path such as "ISA.6", "GS.2", etc.
     * Path: "SEGMENT.POS" (e.g., ISA.6, GS.2, etc.)
     */
    public static String extractField(String edi, String path) {
        if (edi == null || path == null) return null;
        String[] parts = path.split("\\.");
        if (parts.length != 2) return null; // Expect segmentName.elementIndex
        String segment = parts[0];
        int elementIdx;
        try {
            elementIdx = Integer.parseInt(parts[1]);
        } catch (NumberFormatException e) {
            return null;
        }
        try {
            EDIInputFactory factory = EDIInputFactory.newFactory();
            ByteArrayInputStream input = new ByteArrayInputStream(edi.getBytes(StandardCharsets.UTF_8));
            EDIStreamReader reader = factory.createEDIStreamReader(input);

            while (reader.hasNext()) {
                EDIStreamEvent event = reader.next();
                if (event == EDIStreamEvent.START_SEGMENT
                        && segment.equals(reader.getText())) {
                    int count = 0;
                    while (reader.hasNext()) {
                        event = reader.next();
                        if (event == EDIStreamEvent.ELEMENT_DATA) {
                            count++;
                            if (count == elementIdx) {
                                return reader.getText();
                            }
                        } else if (event == EDIStreamEvent.END_SEGMENT) {
                            break;
                        }
                    }
                }
            }
        } catch (Exception e) {
            // Optionally log error
        }
        return null;
    }
    /**
     * Splits EDIFACT file into individual messages by UNH...UNT (including both headers).
     * @param edifactContent The raw EDIFACT content.
     * @return List of raw EDIFACT messages (strings).
     */
    /**
     * Split EDIFACT payload into parts by splitLevel ("interchange", "message") with provided delimiters.
     */
    public List<String> splitEdifactMessages(String payload, String splitLevel,
                              String segmentDelimiter, String elementDelimiter,
                              String subElementDelimiter, boolean allowMultipleInterchanges) {
        if (payload == null || payload.isEmpty()) return Collections.emptyList();

        // Default delimiters if not supplied
        segmentDelimiter = (segmentDelimiter == null || segmentDelimiter.isEmpty()) ? "'" : segmentDelimiter;
        elementDelimiter = (elementDelimiter == null || elementDelimiter.isEmpty()) ? "+" : elementDelimiter;
        subElementDelimiter = (subElementDelimiter == null || subElementDelimiter.isEmpty()) ? ":" : subElementDelimiter;

        List<String> results = new ArrayList<>();

        // Normalize delimiters for regex use
        String segDelimRegex = Pattern.quote(segmentDelimiter);

        // --- Split at Interchange Level ---
        if ("interchange".equalsIgnoreCase(splitLevel)) {
            // Interchange is UNB...UNZ (inclusive)
            Pattern interchangePattern = Pattern.compile(
                    "(UNB.*?UNZ.*?" + segDelimRegex + ")", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);

            Matcher matcher = interchangePattern.matcher(payload);
            while (matcher.find()) {
                results.add(matcher.group(1).trim());
                if (!allowMultipleInterchanges) break; // Only first
            }
            return results;
        }

        // --- Split at Message Level ---
        if ("message".equalsIgnoreCase(splitLevel)) {
            // We need to find each UNH...UNT block within each interchange (or the whole message)
            // Find all message blocks
            Pattern messagePattern = Pattern.compile(
                    "(UNH.*?UNT.*?" + segDelimRegex + ")", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
            Matcher matcher = messagePattern.matcher(payload);
            while (matcher.find()) {
                results.add(matcher.group(1).trim());
                // If not allowing multiple interchanges, only take the first message
                if (!allowMultipleInterchanges) break;
            }
            return results;
        }

        // --- Custom/segment splitting fallback (split at segment delimiter) ---
        if ("segment".equalsIgnoreCase(splitLevel)) {
            String[] segments = payload.split(segDelimRegex);
            for (String seg : segments) {
                if (!seg.trim().isEmpty())
                    results.add(seg.trim() + segmentDelimiter); // Add the delimiter back for each
            }
            return results;
        }

        // If unknown split level or not matched, return the entire payload as one part
        results.add(payload.trim());
        return results;
    }
    public static List<String> splitEdifactMessages(String edifactContent) {
        List<String> messages = new ArrayList<>();

        // Determine segment delimiter (usually '\'', but can vary)
        char segmentDelimiter = '\'';
        Matcher matcher = Pattern.compile("UNA.{6}").matcher(edifactContent);
        if (matcher.find()) {
            segmentDelimiter = edifactContent.charAt(8); // UNA:+.? '
        }

        // Regex for UNH...UNT (non-greedy)
        String segmentDelimRegex = Pattern.quote(String.valueOf(segmentDelimiter));
        String regex = "UNH.*?UNT[^\\" + segmentDelimiter + "]*" + segmentDelimRegex;
        Pattern pattern = Pattern.compile(regex, Pattern.DOTALL);

        Matcher msgMatcher = pattern.matcher(edifactContent);
        while (msgMatcher.find()) {
            messages.add(msgMatcher.group());
        }
        return messages;
    }

    public List<String> splitX12(String payload, String splitLevel,
                              String segmentDelimiter, String elementDelimiter,
                              String subElementDelimiter, boolean allowMultipleInterchanges) {

        if (payload == null || payload.isEmpty()) return Collections.emptyList();

        segmentDelimiter = (segmentDelimiter == null || segmentDelimiter.isEmpty()) ? "~" : segmentDelimiter;
        elementDelimiter = (elementDelimiter == null || elementDelimiter.isEmpty()) ? "*" : elementDelimiter;
        subElementDelimiter = (subElementDelimiter == null || subElementDelimiter.isEmpty()) ? ":" : subElementDelimiter;

        List<String> results = new ArrayList<>();
        String segDelimRegex = Pattern.quote(segmentDelimiter);

        // === Interchange-level split: ISA...IEA ===
        if ("interchange".equalsIgnoreCase(splitLevel)) {
            Pattern interchangePattern = Pattern.compile(
                    "(ISA[\\s\\S]*?IEA\\d{1,2}" + segDelimRegex + ")",
                    Pattern.CASE_INSENSITIVE
            );
            Matcher matcher = interchangePattern.matcher(payload);
            while (matcher.find()) {
                results.add(matcher.group(1).trim());
                if (!allowMultipleInterchanges) break;
            }
            return results;
        }

        // === Group-level split: GS...GE inside each Interchange ===
        if ("group".equalsIgnoreCase(splitLevel)) {
            // Find all GS...GE segments within the payload
            Pattern groupPattern = Pattern.compile(
                    "(GS[\\s\\S]*?GE\\d{1,2}" + segDelimRegex + ")",
                    Pattern.CASE_INSENSITIVE
            );
            Matcher matcher = groupPattern.matcher(payload);
            while (matcher.find()) {
                results.add(matcher.group(1).trim());
                if (!allowMultipleInterchanges) break;
            }
            return results;
        }

        // === Transaction-level split: ST...SE inside each Group/Interchange ===
        if ("transaction".equalsIgnoreCase(splitLevel)) {
            Pattern txnPattern = Pattern.compile(
                    "(ST[\\s\\S]*?SE\\d{1,2}" + segDelimRegex + ")",
                    Pattern.CASE_INSENSITIVE
            );
            Matcher matcher = txnPattern.matcher(payload);
            while (matcher.find()) {
                results.add(matcher.group(1).trim());
                if (!allowMultipleInterchanges) break;
            }
            return results;
        }

        // === Segment-level (fallback) ===
        if ("segment".equalsIgnoreCase(splitLevel)) {
            String[] segments = payload.split(segDelimRegex);
            for (String seg : segments) {
                if (!seg.trim().isEmpty())
                    results.add(seg.trim() + segmentDelimiter);
            }
            return results;
        }

        // If no known split level, return as single document
        results.add(payload.trim());
        return results;
    }

    /**
     * Detects the segment delimiter in an X12 payload.
     * Returns "~" if not found or on error.
     */
    public static String detectX12SegmentDelimiter(String x12) {
        if (x12 == null || x12.length() < 108) return "~"; // Fallback
        int isaIndex = x12.indexOf("ISA");
        if (isaIndex < 0) return "~";

        // The segment delimiter is the character at position isaIndex + 105
        int segDelimIndex = isaIndex + 105;
        if (x12.length() > segDelimIndex) {
            char delim = x12.charAt(segDelimIndex);
            // Sanity check: it should occur frequently in the message
            long freq = x12.chars().filter(c -> c == delim).count();
            if (freq >= 3) return String.valueOf(delim);
        }
        return "~";
    }

    /**
     * Detects the element delimiter in an X12 payload.
     * The element delimiter is the character at position 4 after ISA.
     */
    public static String detectX12ElementDelimiter(String x12) {
        if (x12 == null || x12.length() < 5) return "*"; // Fallback
        int isaIndex = x12.indexOf("ISA");
        if (isaIndex < 0 || x12.length() < isaIndex + 4) return "*";
        char delim = x12.charAt(isaIndex + 3);
        // Should appear often (heuristic)
        long freq = x12.chars().filter(c -> c == delim).count();
        if (freq >= 3) return String.valueOf(delim);
        return "*";
    }

    /**
     * Detects the subelement delimiter (ISA16).
     * The subelement delimiter is at position 105 after ISA (106th character in the segment).
     */
    public static String detectX12SubElementDelimiter(String x12) {
        if (x12 == null || x12.length() < 106) return ":";
        int isaIndex = x12.indexOf("ISA");
        if (isaIndex < 0 || x12.length() < isaIndex + 105) return ":";
        char delim = x12.charAt(isaIndex + 104);
        // Subelement usually occurs at least once (but might be rare)
        return String.valueOf(delim);
    }

}