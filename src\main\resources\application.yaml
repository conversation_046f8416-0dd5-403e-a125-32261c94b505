server:
  port: 8080

spring:
  main:
    allow-circular-references: true
  datasource:
    url: ${SHARED_SHARED}
  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.OracleDialect
  data:
    redis:
#      host: hip-gscm-redis-dev.rds-a2-np.kob.dell.com
#      port: 443
#      password: sFjIvYHMJHcv
#      ssl:
#        enabled: true
      enabled: true
  config.activate.on-profile: ${SPRING_PROFILES_ACTIVE}
  cloud:
    config:
       name: ${configproperties_sheet_name}  
  config.import: optional:configserver:${configserver_uri} 

service:
  manager:
    name: OrderIntegrationManager
  concurrency:
    corePoolSize: 12
    maxPoolSize: 48
    queueCapacity: 1000

hip:
  kafka:
    bootstrap-servers: "kafka1:9092,kafka2:9092"
    security-protocol: SASL_PLAINTEXT
    sasl-mechanism: SCRAM-SHA-256

# Logging and monitoring configs...
logging:
  level:
    root: info

