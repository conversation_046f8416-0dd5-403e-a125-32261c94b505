package com.dell.it.hip.client;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Disabled;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for MQClient utility.
 * 
 * Note: These tests are disabled by default as they require an actual IBM MQ server.
 * To run these tests:
 * 1. Ensure IBM MQ server is accessible at the configured connection details
 * 2. Remove @Disabled annotations
 * 3. Update connection properties if needed
 */
@Disabled("Requires actual IBM MQ server - enable for integration testing")
public class MQClientTest {
    
    private static final Logger logger = LoggerFactory.getLogger(MQClientTest.class);
    
    private MQClient mqClient;
    private MQClientConfig consumerConfig;
    private MQClientConfig producerConfig;
    
    @BeforeEach
    void setUp() {
        // Create configurations using the test properties
        consumerConfig = MQClientConfig.createConsumerConfig();
        producerConfig = MQClientConfig.createProducerConfig();
        
        // Create MQClient instance
        mqClient = new MQClient(consumerConfig, producerConfig);
        
        logger.info("MQClient test setup completed");
    }
    
    @AfterEach
    void tearDown() {
        if (mqClient != null) {
            mqClient.close();
        }
        logger.info("MQClient test cleanup completed");
    }
    
    @Test
    void testSendMessage() throws Exception {
        // Test sending a simple text message
        String testMessage = "Test message from MQClient - " + System.currentTimeMillis();
        
        assertDoesNotThrow(() -> {
            mqClient.sendMessage(testMessage);
        });
        
        logger.info("Successfully sent message: {}", testMessage);
    }
    
    @Test
    void testSendByteArrayMessage() throws Exception {
        // Test sending a byte array message
        byte[] testPayload = "Binary test message".getBytes();
        
        assertDoesNotThrow(() -> {
            mqClient.sendMessage(testPayload);
        });
        
        logger.info("Successfully sent byte array message");
    }
    
    @Test
    void testReceiveMessage() throws Exception {
        // First send a message
        String testMessage = "Test receive message - " + System.currentTimeMillis();
        mqClient.sendMessage(testMessage);
        
        // Then try to receive it
        String receivedMessage = mqClient.receiveMessage(10000); // 10 second timeout
        
        assertNotNull(receivedMessage, "Should have received a message");
        assertTrue(receivedMessage.contains("Test receive message"), "Received message should contain expected content");
        
        logger.info("Successfully received message: {}", receivedMessage);
    }
    
    @Test
    void testReceiveMessageTimeout() throws Exception {
        // Test receiving with timeout when no message is available
        String receivedMessage = mqClient.receiveMessage(2000); // 2 second timeout
        
        // Should return null when no message is available within timeout
        assertNull(receivedMessage, "Should return null when no message is available");
        
        logger.info("Correctly handled receive timeout");
    }
    
    @Test
    void testMessageListener() throws Exception {
        AtomicInteger messageCount = new AtomicInteger(0);
        CountDownLatch latch = new CountDownLatch(3);
        
        // Start listener
        mqClient.startListener(message -> {
            logger.info("Received message via listener: {}", message);
            messageCount.incrementAndGet();
            latch.countDown();
        });
        
        // Send multiple messages
        for (int i = 1; i <= 3; i++) {
            String testMessage = "Listener test message " + i + " - " + System.currentTimeMillis();
            mqClient.sendMessage(testMessage);
            Thread.sleep(100); // Small delay between messages
        }
        
        // Wait for messages to be received
        boolean received = latch.await(15, TimeUnit.SECONDS);
        
        mqClient.stopListener();
        
        assertTrue(received, "Should have received all 3 messages within timeout");
        assertEquals(3, messageCount.get(), "Should have received exactly 3 messages");
        
        logger.info("Successfully tested message listener with {} messages", messageCount.get());
    }
    
    @Test
    void testWaitForMessages() throws Exception {
        AtomicInteger processedCount = new AtomicInteger(0);
        
        // Send messages first
        for (int i = 1; i <= 2; i++) {
            String testMessage = "Wait test message " + i + " - " + System.currentTimeMillis();
            mqClient.sendMessage(testMessage);
        }
        
        // Wait for specific number of messages
        assertDoesNotThrow(() -> {
            mqClient.waitForMessages(2, 10000, message -> {
                logger.info("Processed message: {}", message);
                processedCount.incrementAndGet();
            });
        });
        
        assertEquals(2, processedCount.get(), "Should have processed exactly 2 messages");
        
        logger.info("Successfully waited for and processed {} messages", processedCount.get());
    }
    
    @Test
    void testCustomConfiguration() throws Exception {
        // Test with custom configuration
        MQClientConfig customConsumerConfig = new MQClientConfig.Builder()
            .queueManager("BIEG4CU07")
            .queue("QA.D365.TEST_MAC_EMFP.SCG_TEST")
            .channel("BIE.GOSS.01.TLS")
            .connName("WMQNLG2A05.AMER.DELL.COM(2043)")
            .authenticationType("none")
            .username("channel.sender")
            .sslCipherSuite("TLS_RSA_WITH_AES_128_CBC_SHA256")
            .receiveTimeout(3000L)
            .concurrency(2)
            .build();
        
        MQClientConfig customProducerConfig = new MQClientConfig.Builder()
            .queueManager("BIEG4CU07")
            .queue("QA.D365.TEST_MAC_EMFP.SCG_TEST")
            .channel("BIE.GOSS.01.TLS")
            .connName("WMQNLG2A05.AMER.DELL.COM(2043)")
            .host("WMQNLG2A05.AMER.DELL.COM")
            .port(2043)
            .authenticationType("none")
            .username("channel.sender")
            .sslCipherSuite("TLS_RSA_WITH_AES_128_CBC_SHA256")
            .persistent(true)
            .build();
        
        try (MQClient customClient = new MQClient(customConsumerConfig, customProducerConfig)) {
            String testMessage = "Custom config test - " + System.currentTimeMillis();
            
            assertDoesNotThrow(() -> {
                customClient.sendMessage(testMessage);
            });
            
            logger.info("Successfully tested custom configuration");
        }
    }
    
    @Test
    void testErrorHandling() {
        // Test with invalid configuration to verify error handling
        MQClientConfig invalidConfig = new MQClientConfig.Builder()
            .queueManager("INVALID_QM")
            .queue("INVALID.QUEUE")
            .channel("INVALID.CHANNEL")
            .connName("invalid.host(1414)")
            .build();
        
        try (MQClient invalidClient = new MQClient(invalidConfig, invalidConfig)) {
            // This should throw an exception due to invalid configuration
            assertThrows(Exception.class, () -> {
                invalidClient.sendMessage("This should fail");
            });
            
            logger.info("Correctly handled invalid configuration");
        }
    }
    
    /**
     * Manual test method for interactive testing.
     * This method can be run manually to test the MQClient interactively.
     */
    public static void main(String[] args) {
        Logger mainLogger = LoggerFactory.getLogger("MQClientManualTest");
        
        try {
            mainLogger.info("Starting MQClient manual test...");
            
            MQClientConfig consumerConfig = MQClientConfig.createConsumerConfig();
            MQClientConfig producerConfig = MQClientConfig.createProducerConfig();
            
            try (MQClient client = new MQClient(consumerConfig, producerConfig)) {
                
                // Test 1: Send a message
                mainLogger.info("Test 1: Sending message...");
                String testMessage = "Manual test message - " + System.currentTimeMillis();
                client.sendMessage(testMessage);
                mainLogger.info("Message sent successfully");
                
                // Test 2: Receive a message
                mainLogger.info("Test 2: Receiving message...");
                String received = client.receiveMessage(5000);
                if (received != null) {
                    mainLogger.info("Received message: {}", received);
                } else {
                    mainLogger.info("No message received within timeout");
                }
                
                // Test 3: Start listener for a short time
                mainLogger.info("Test 3: Starting listener for 10 seconds...");
                client.startListener(message -> {
                    mainLogger.info("Listener received: {}", message);
                });
                
                Thread.sleep(10000); // Listen for 10 seconds
                client.stopListener();
                
                mainLogger.info("Manual test completed successfully");
            }
            
        } catch (Exception e) {
            mainLogger.error("Manual test failed: {}", e.getMessage(), e);
        }
    }
}
