package com.dell.it.hip.client;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Disabled;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AcknowledgeMode;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for RabbitMQClient utility.
 * 
 * Note: These tests are disabled by default as they require an actual RabbitMQ server.
 * To run these tests:
 * 1. Ensure RabbitMQ server is accessible at the configured connection details
 * 2. Remove @Disabled annotations
 * 3. Update connection properties if needed
 */
@Disabled("Requires actual RabbitMQ server - enable for integration testing")
public class RabbitMQClientTest {
    
    private static final Logger logger = LoggerFactory.getLogger(RabbitMQClientTest.class);
    
    private RabbitMQClient rabbitMQClient;
    private RabbitMQClientConfig consumerConfig;
    private RabbitMQClientConfig producerConfig;
    
    @BeforeEach
    void setUp() {
        // Create configurations using the test properties
        consumerConfig = RabbitMQClientConfig.createConsumerConfig();
        producerConfig = RabbitMQClientConfig.createProducerConfig();
        
        // Create RabbitMQClient instance
        rabbitMQClient = new RabbitMQClient(consumerConfig, producerConfig);
        
        logger.info("RabbitMQClient test setup completed");
    }
    
    @AfterEach
    void tearDown() {
        if (rabbitMQClient != null) {
            rabbitMQClient.close();
        }
        logger.info("RabbitMQClient test cleanup completed");
    }
    
    @Test
    void testSendMessage() throws Exception {
        // Test sending a simple text message
        String testMessage = "Test message from RabbitMQClient - " + System.currentTimeMillis();
        
        assertDoesNotThrow(() -> {
            rabbitMQClient.sendMessage(testMessage);
        });
        
        logger.info("Successfully sent message: {}", testMessage);
    }
    
    @Test
    void testSendByteArrayMessage() throws Exception {
        // Test sending a byte array message
        byte[] testPayload = "Binary test message".getBytes();
        
        assertDoesNotThrow(() -> {
            rabbitMQClient.sendMessage(testPayload);
        });
        
        logger.info("Successfully sent byte array message");
    }
    
    @Test
    void testSendMessageWithHeaders() throws Exception {
        // Test sending a message with custom headers
        String testMessage = "Test message with headers - " + System.currentTimeMillis();
        Map<String, Object> headers = new HashMap<>();
        headers.put("messageId", "test-123");
        headers.put("source", "RabbitMQClientTest");
        headers.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        assertDoesNotThrow(() -> {
            rabbitMQClient.sendMessage(testMessage.getBytes(), headers);
        });
        
        logger.info("Successfully sent message with headers: {}", testMessage);
    }
    
    @Test
    void testReceiveMessage() throws Exception {
        // First send a message
        String testMessage = "Test receive message - " + System.currentTimeMillis();
        rabbitMQClient.sendMessage(testMessage);
        
        // Wait a bit for the message to be available
        Thread.sleep(2000);
        
        // Then try to receive it
        RabbitMQClient.RabbitMQMessage receivedMessage = rabbitMQClient.receiveMessage(10000); // 10 second timeout
        
        assertNotNull(receivedMessage, "Should have received a message");
        assertTrue(receivedMessage.getContent().contains("Test receive message"), "Received message should contain expected content");
        
        logger.info("Successfully received message: {}", receivedMessage);
    }
    
    @Test
    void testReceiveMessageTimeout() throws Exception {
        // Test receiving with timeout when no message is available
        RabbitMQClient.RabbitMQMessage receivedMessage = rabbitMQClient.receiveMessage(2000); // 2 second timeout
        
        // Should return null when no message is available within timeout
        // Note: This might receive existing messages in the queue, so we just check it doesn't throw
        logger.info("Receive timeout test completed, received: {}", receivedMessage);
    }
    
    @Test
    void testMessageListener() throws Exception {
        AtomicInteger messageCount = new AtomicInteger(0);
        CountDownLatch latch = new CountDownLatch(3);
        
        // Start listener
        rabbitMQClient.startListener(message -> {
            logger.info("Received message via listener: {}", message);
            messageCount.incrementAndGet();
            latch.countDown();
        });
        
        // Send multiple messages
        for (int i = 1; i <= 3; i++) {
            String testMessage = "Listener test message " + i + " - " + System.currentTimeMillis();
            rabbitMQClient.sendMessage(testMessage);
            Thread.sleep(100); // Small delay between messages
        }
        
        // Wait for messages to be received
        boolean received = latch.await(30, TimeUnit.SECONDS);
        
        rabbitMQClient.stopListener();
        
        assertTrue(received, "Should have received all 3 messages within timeout");
        assertEquals(3, messageCount.get(), "Should have received exactly 3 messages");
        
        logger.info("Successfully tested message listener with {} messages", messageCount.get());
    }
    
    @Test
    void testWaitForMessages() throws Exception {
        AtomicInteger processedCount = new AtomicInteger(0);
        
        // Send messages first
        for (int i = 1; i <= 2; i++) {
            String testMessage = "Wait test message " + i + " - " + System.currentTimeMillis();
            rabbitMQClient.sendMessage(testMessage);
        }
        
        // Wait for specific number of messages
        List<RabbitMQClient.RabbitMQMessage> messages = assertDoesNotThrow(() -> {
            return rabbitMQClient.waitForMessages(2, 30000, message -> {
                logger.info("Processed message: {}", message);
                processedCount.incrementAndGet();
            });
        });
        
        assertEquals(2, messages.size(), "Should have received exactly 2 messages");
        assertEquals(2, processedCount.get(), "Should have processed exactly 2 messages");
        
        logger.info("Successfully waited for and processed {} messages", processedCount.get());
    }
    
    @Test
    void testCustomConfiguration() throws Exception {
        // Test with custom configuration
        RabbitMQClientConfig customConsumerConfig = new RabbitMQClientConfig.Builder()
            .host("paas-rmq-aic-dev01.us.dell.com")
            .port(8071)
            .virtualHost("AIC_ACTMON_DEV")
            .username("AMERICAS\\svc_npaicdvgsmrmqv1")
            .password("~UfIBrgxqj%2BVp%3FSTK69L178H")
            .queueName("custom.test.queue")
            .authenticationType("TLS")
            .acknowledgeMode(AcknowledgeMode.MANUAL)
            .prefetchCount(5)
            .concurrency(2)
            .build();
        
        RabbitMQClientConfig customProducerConfig = new RabbitMQClientConfig.Builder()
            .host("paas-rmq-aic-dev01.us.dell.com")
            .port(8071)
            .virtualHost("AIC_ACTMON_DEV")
            .username("AMERICAS\\svc_npaicdvgsmrmqv1")
            .password("~94YzSH?*VbAUXMf_pICeJTL")
            .exchange("custom.test.exchange")
            .routingKey("custom.test.routing.key")
            .authenticationType("TLS")
            .mandatory(true)
            .persistent(true)
            .gzipEnabled(true)
            .build();
        
        try (RabbitMQClient customClient = new RabbitMQClient(customConsumerConfig, customProducerConfig)) {
            String testMessage = "Custom config test - " + System.currentTimeMillis();
            
            assertDoesNotThrow(() -> {
                customClient.sendMessage(testMessage);
            });
            
            logger.info("Successfully tested custom configuration");
        }
    }
    
    @Test
    void testErrorHandling() {
        // Test with invalid configuration to verify error handling
        RabbitMQClientConfig invalidConfig = new RabbitMQClientConfig.Builder()
            .host("invalid.host")
            .port(9999)
            .virtualHost("invalid_vhost")
            .username("invalid_user")
            .password("invalid_password")
            .exchange("invalid.exchange")
            .routingKey("invalid.routing.key")
            .queueName("invalid.queue")
            .build();
        
        try (RabbitMQClient invalidClient = new RabbitMQClient(invalidConfig, invalidConfig)) {
            // This should throw an exception due to invalid configuration
            assertThrows(Exception.class, () -> {
                invalidClient.sendMessage("This should fail");
            });
            
            logger.info("Correctly handled invalid configuration");
        }
    }
    
    /**
     * Manual test method for interactive testing.
     * This method can be run manually to test the RabbitMQClient interactively.
     */
    public static void main(String[] args) {
        Logger mainLogger = LoggerFactory.getLogger("RabbitMQClientManualTest");
        
        try {
            mainLogger.info("Starting RabbitMQClient manual test...");
            
            RabbitMQClientConfig consumerConfig = RabbitMQClientConfig.createConsumerConfig();
            RabbitMQClientConfig producerConfig = RabbitMQClientConfig.createProducerConfig();
            
            try (RabbitMQClient client = new RabbitMQClient(consumerConfig, producerConfig)) {
                
                // Test 1: Send a message
                mainLogger.info("Test 1: Sending message...");
                String testMessage = "Manual test message - " + System.currentTimeMillis();
                client.sendMessage(testMessage);
                mainLogger.info("Message sent successfully");
                
                // Test 2: Receive a message
                mainLogger.info("Test 2: Receiving message...");
                RabbitMQClient.RabbitMQMessage received = client.receiveMessage(10000);
                if (received != null) {
                    mainLogger.info("Received message: {}", received);
                } else {
                    mainLogger.info("No message received within timeout");
                }
                
                // Test 3: Start listener for a short time
                mainLogger.info("Test 3: Starting listener for 15 seconds...");
                client.startListener(message -> {
                    mainLogger.info("Listener received: {}", message);
                });
                
                Thread.sleep(15000); // Listen for 15 seconds
                client.stopListener();
                
                mainLogger.info("Manual test completed successfully");
            }
            
        } catch (Exception e) {
            mainLogger.error("Manual test failed: {}", e.getMessage(), e);
        }
    }
}
