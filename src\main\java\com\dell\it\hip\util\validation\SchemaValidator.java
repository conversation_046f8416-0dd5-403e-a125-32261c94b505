package com.dell.it.hip.util.validation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

@Component
public class SchemaValidator {

    @Autowired
    private StringRedisTemplate redisTemplate;

    public boolean validate(String payload, String format, String schemaKey)  {
        String schemaContent = redisTemplate.opsForValue().get(schemaKey);
        if (schemaContent == null || schemaContent.isBlank()) {
            throw new IllegalStateException("Schema not found in Redis for key: " + schemaKey);
        }
        return validateWithSchemaContent(payload, format, schemaContent);
    }

    private boolean validateWithSchemaContent(String payload, String format, String schemaContent) {
        switch (format) {
            case "EDI_X12":
                return EdiValidator.validateAgainstSchemaX12(payload, schemaContent);
            case "EDI_EDIFACT":
                return EdiValidator.validateAgainstSchemaEdifact(payload, schemaContent);
            case "XML":
                return XmlSchemaValidator.validate(payload, schemaContent);
            case "JSON":
                return JsonSchemaValidator.validate(payload, schemaContent);
            case "CSV":
                return CsvSchemaValidator.validate(payload, schemaContent);
            default:
                return false;
        }
    }
}