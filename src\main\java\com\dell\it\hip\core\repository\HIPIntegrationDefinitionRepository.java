package com.dell.it.hip.core.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.dell.it.hip.config.HIPIntegrationRequestEntity;

@Repository
public interface HIPIntegrationDefinitionRepository extends JpaRepository<HIPIntegrationRequestEntity, Long> {
    List<HIPIntegrationRequestEntity> findByServiceManagerName(String serviceManagerName);

    boolean existsByServiceManagerNameAndHipIntegrationNameAndVersion(
            String serviceManagerName, String hipIntegrationName, String version);

    HIPIntegrationRequestEntity findByServiceManagerNameAndHipIntegrationNameAndVersion(
            String serviceManagerName, String hipIntegrationName, String version);
}
