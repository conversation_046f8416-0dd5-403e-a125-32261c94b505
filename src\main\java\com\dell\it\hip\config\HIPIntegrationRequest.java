package com.dell.it.hip.config;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.Handlers.HandlerConfigRef;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.util.ThrottleSettings;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
@JsonIgnoreProperties(ignoreUnknown = true)
public class HIPIntegrationRequest implements Serializable {
  
	private String hipIntegrationName;
    private String version;
    private String hipIntegrationType;

    private String businessFlowType;
    private String owner;
    private String businessFlowName;

    private String businessFlowVersion;

    @JsonDeserialize(using = TagsDeserializer.class)
    private List<Tag> tags;

    private List<AdapterConfigRef> adapters;
    private List<FlowStepConfigRef> flowSteps;
    private List<HandlerConfigRef> handlers;

    private List<String> propertySheets;
    private ThrottleSettings throttleSettings;
    private String serviceManagerName;
    private String status;


    // other business-specific properties

    // Getters and setters

    public String getHipIntegrationName() { return hipIntegrationName; }
    public void setHipIntegrationName(String hipIntegrationName) { this.hipIntegrationName = hipIntegrationName; }

    public String getVersion() { return version; }
    public void setVersion(String version) { this.version = version; }

    public String getOwner() { return owner; }
    public void setOwner(String owner) { this.owner = owner; }

    public String getBusinessFlowName() { return businessFlowName; }
    public void setBusinessFlowName(String businessFlowName) { this.businessFlowName = businessFlowName; }

    public List<Tag> getTags() { return tags; }
    public void setTags(List<Tag> tags) { this.tags = tags; }

    /**
     * Convenience method to get tags as a comma-separated string for backward compatibility.
     * @return comma-separated string of tag values
     */
    public String getTagsAsString() {
        if (tags == null || tags.isEmpty()) {
            return "";
        }
        return tags.stream()
                .map(tag -> tag.getKey().equals("legacy") ? tag.getValue() : tag.getKey() + ":" + tag.getValue())
                .reduce((a, b) -> a + "," + b)
                .orElse("");
    }

    /**
     * Convenience method to set tags from a comma-separated string for backward compatibility.
     * @param tagsString comma-separated string of tag values
     */
    public void setTagsFromString(String tagsString) {
        if (tagsString == null || tagsString.trim().isEmpty()) {
            this.tags = new ArrayList<>();
            return;
        }

        this.tags = new ArrayList<>();
        String[] tagArray = tagsString.split(",");
        for (String tagValue : tagArray) {
            String trimmedTag = tagValue.trim();
            if (!trimmedTag.isEmpty()) {
                this.tags.add(new Tag("legacy", trimmedTag));
            }
        }
    }

    public List<AdapterConfigRef> getAdapters() { return adapters; }
    public void setAdapters(List<AdapterConfigRef> adapters) { this.adapters = adapters; }

    public List<FlowStepConfigRef> getFlowSteps() { return flowSteps; }
    public void setFlowSteps(List<FlowStepConfigRef> flowSteps) { this.flowSteps = flowSteps; }

    public List<HandlerConfigRef> getHandlers() { return handlers; }
    public void setHandlers(List<HandlerConfigRef> handlers) { this.handlers = handlers; }

    public List<String> getPropertySheets() { return propertySheets; }
    public void setPropertySheets(List<String> propertySheets) { this.propertySheets = propertySheets; }

    public ThrottleSettings getThrottleSettings() { return throttleSettings; }
    public void setThrottleSettings(ThrottleSettings throttleSettings) { this.throttleSettings = throttleSettings; }

    public void setServiceManagerName(String serviceManagerName) { this.serviceManagerName = serviceManagerName; }
    public String getServiceManagerName() { return serviceManagerName; }

    public String getStatus() { return status;
    }
    public void setStatus(String status) {this.status = status; }

    public String getHipIntegrationType() {
        return hipIntegrationType;
    }

    public void setHipIntegrationType(String hipIntegrationType) {
        this.hipIntegrationType = hipIntegrationType;
    }

    public String getBusinessFlowVersion() {
        return businessFlowVersion;
    }

    public void setBusinessFlowVersion(String businessFlowVersion) {
        this.businessFlowVersion = businessFlowVersion;
    }

    public String getBusinessFlowType() {
        return businessFlowType;
    }

    public void setBusinessFlowType(String businessFlowType) {
        this.businessFlowType = businessFlowType;
    }

}