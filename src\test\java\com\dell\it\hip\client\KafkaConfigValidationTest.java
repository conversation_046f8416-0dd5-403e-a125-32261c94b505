package com.dell.it.hip.client;

import org.apache.kafka.clients.producer.ProducerConfig;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Method;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to validate Kafka configuration compatibility fixes.
 * This test verifies that the idempotence/acks configuration issue is resolved.
 */
public class KafkaConfigValidationTest {
    
    private static final Logger logger = LoggerFactory.getLogger(KafkaConfigValidationTest.class);
    
    @Test
    void testIdempotenceConfigurationFix() throws Exception {
        // Test configuration with idempotence=true and acks=1 (should be auto-corrected)
        KafkaClientConfig config = new KafkaClientConfig.Builder()
            .bootstrapServers("localhost:9092")
            .topic("test-topic")
            .clientId("test-client")
            .acks(1)  // This should be overridden to "all"
            .enableIdempotence(true)  // This should force acks=all
            .build();
        
        // Create KafkaClient and access the private createProducerProperties method
        KafkaClient kafkaClient = new KafkaClient(null, config);
        
        // Use reflection to test the private createProducerProperties method
        Method createProducerPropertiesMethod = KafkaClient.class.getDeclaredMethod("createProducerProperties", KafkaClientConfig.class);
        createProducerPropertiesMethod.setAccessible(true);
        
        Properties props = (Properties) createProducerPropertiesMethod.invoke(kafkaClient, config);
        
        // Verify that acks is set to "all" when idempotence is enabled
        assertEquals("all", props.getProperty(ProducerConfig.ACKS_CONFIG), 
                    "When idempotence is enabled, acks should be automatically set to 'all'");
        
        assertEquals("true", props.getProperty(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG), 
                    "Idempotence should be enabled as configured");
        
        logger.info("✅ Idempotence configuration fix validated successfully");
        logger.info("   - Original acks config: 1");
        logger.info("   - Actual acks config: {}", props.getProperty(ProducerConfig.ACKS_CONFIG));
        logger.info("   - Idempotence enabled: {}", props.getProperty(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG));
        
        kafkaClient.close();
    }
    
    @Test
    void testNonIdempotentConfigurationPreserved() throws Exception {
        // Test configuration with idempotence=false and acks=1 (should be preserved)
        KafkaClientConfig config = new KafkaClientConfig.Builder()
            .bootstrapServers("localhost:9092")
            .topic("test-topic")
            .clientId("test-client")
            .acks(1)  // This should be preserved
            .enableIdempotence(false)  // This should allow custom acks
            .build();
        
        // Create KafkaClient and access the private createProducerProperties method
        KafkaClient kafkaClient = new KafkaClient(null, config);
        
        // Use reflection to test the private createProducerProperties method
        Method createProducerPropertiesMethod = KafkaClient.class.getDeclaredMethod("createProducerProperties", KafkaClientConfig.class);
        createProducerPropertiesMethod.setAccessible(true);
        
        Properties props = (Properties) createProducerPropertiesMethod.invoke(kafkaClient, config);
        
        // Verify that acks is preserved when idempotence is disabled
        assertEquals("1", props.getProperty(ProducerConfig.ACKS_CONFIG), 
                    "When idempotence is disabled, acks should preserve the configured value");
        
        assertEquals("false", props.getProperty(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG), 
                    "Idempotence should be disabled as configured");
        
        logger.info("✅ Non-idempotent configuration preservation validated successfully");
        logger.info("   - Configured acks: 1");
        logger.info("   - Actual acks config: {}", props.getProperty(ProducerConfig.ACKS_CONFIG));
        logger.info("   - Idempotence enabled: {}", props.getProperty(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG));
        
        kafkaClient.close();
    }
    
    @Test
    void testDefaultConfigurationCompatibility() throws Exception {
        // Test default configuration from createProducerConfig()
        KafkaClientConfig config = KafkaClientConfig.createProducerConfig();
        
        // Create KafkaClient and access the private createProducerProperties method
        KafkaClient kafkaClient = new KafkaClient(null, config);
        
        // Use reflection to test the private createProducerProperties method
        Method createProducerPropertiesMethod = KafkaClient.class.getDeclaredMethod("createProducerProperties", KafkaClientConfig.class);
        createProducerPropertiesMethod.setAccessible(true);
        
        Properties props = (Properties) createProducerPropertiesMethod.invoke(kafkaClient, config);
        
        // Verify that default configuration is compatible
        String acksConfig = props.getProperty(ProducerConfig.ACKS_CONFIG);
        String idempotenceConfig = props.getProperty(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG);
        
        assertNotNull(acksConfig, "Acks configuration should be set");
        
        // If idempotence is enabled, acks must be "all"
        if ("true".equals(idempotenceConfig)) {
            assertEquals("all", acksConfig, "When idempotence is enabled, acks must be 'all'");
        }
        
        logger.info("✅ Default configuration compatibility validated successfully");
        logger.info("   - Default acks config: {}", acksConfig);
        logger.info("   - Default idempotence: {}", idempotenceConfig);
        
        kafkaClient.close();
    }
    
    @Test
    void testConfigurationWithNullValues() throws Exception {
        // Test configuration with null values (should use defaults)
        KafkaClientConfig config = new KafkaClientConfig.Builder()
            .bootstrapServers("localhost:9092")
            .topic("test-topic")
            .clientId("test-client")
            // acks and enableIdempotence are null - should use defaults
            .build();
        
        // Create KafkaClient and access the private createProducerProperties method
        KafkaClient kafkaClient = new KafkaClient(null, config);
        
        // Use reflection to test the private createProducerProperties method
        Method createProducerPropertiesMethod = KafkaClient.class.getDeclaredMethod("createProducerProperties", KafkaClientConfig.class);
        createProducerPropertiesMethod.setAccessible(true);
        
        Properties props = (Properties) createProducerPropertiesMethod.invoke(kafkaClient, config);
        
        // Verify that defaults are applied correctly
        String acksConfig = props.getProperty(ProducerConfig.ACKS_CONFIG);
        assertEquals("all", acksConfig, "Default acks should be 'all'");
        
        logger.info("✅ Null configuration handling validated successfully");
        logger.info("   - Default acks when null: {}", acksConfig);
        
        kafkaClient.close();
    }
}
