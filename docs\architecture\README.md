# HIP Services Architecture Documentation

This directory contains the definitive architectural documentation for the HIP (Hybrid Integration Platform) Services framework, reflecting the current state of the codebase after the comprehensive JSON property naming convention refactoring.

## 📋 Documentation Overview

### 🏗️ [Architecture Diagram](./architecture-diagram.md)
**Complete system architecture showing all components and their relationships**
- Configuration management layer with refactored property naming
- Input adapter layer for all supported protocols
- Message processing flow with all flow steps
- Output handler layer for all target systems
- External system integration points
- Configuration class structure and relationships

### 🚀 [Deployment Diagram](./deployment-diagram.md)
**Infrastructure and deployment architecture**
- Cloud infrastructure setup with config server clusters
- Service mesh configuration with load balancing
- Monitoring and observability stack
- Development and production environments
- Configuration property deployment patterns
- External integration point connections

### 🔄 [Sequence Diagram](./sequence-diagram.md)
**Configuration property deserialization and usage flow**
- Integration registration process
- Property sheet fetching from config servers
- JSON deserialization with new property naming conventions
- Configuration class resolution and instantiation
- Runtime configuration usage in adapters and handlers
- Error handling for invalid property names

### 🌐 [Overview Diagram](./overview-diagram.md)
**High-level system overview with comprehensive component view**
- Complete system architecture with all layers
- Property naming convention examples
- Configuration infrastructure components
- Input/output layer details
- Processing flow components
- Monitoring and observability integration

## 🔧 Configuration Management Highlights

### Property Naming Convention
All configuration classes now follow the consistent pattern:
```
{technology}.{consumer|producer}.{property.name}
```

**Examples:**
- `kafka.producer.bootstrap.servers`
- `ibmmq.consumer.queue.manager`
- `rabbitmq.producer.routing.key`
- `sftp.consumer.private.key.path`
- `https.producer.api.key.header`
- `nas.consumer.mount.path`

### Refactored Configuration Classes

#### Handler Configs (Producers)
- **DynamicKafkaHandlerConfig**: Updated 8+ properties to dot-separated format
- **DynamicIbmmqHandlerConfig**: Fixed queue.manager, ssl.cipher.suite, gzip.enabled
- **DynamicRabbitMQHandlerConfig**: Updated routing.key, gzip.enabled
- **DynamicSftpHandlerConfig**: Already compliant ✅
- **DynamicHttpsHandlerConfig**: Already compliant ✅
- **DynamicNasHandlerConfig**: Already compliant ✅

#### Adapter Configs (Consumers)
- **DynamicKafkaAdapterConfig**: Already compliant ✅
- **DynamicIBMMQAdapterConfig**: Fixed 8+ SSL and connection properties
- **DynamicRabbitMQAdapterConfig**: Already compliant ✅
- **DynamicSFTPAdapterConfig**: Already compliant ✅
- **DynamicHttpsAdapterConfig**: Already compliant ✅
- **DynamicNASAdapterConfig**: Already compliant ✅

## 🧪 Testing and Validation

### Unit Tests Created
- **ConfigurationPropertyMappingTest**: Comprehensive tests for all config classes
- **PropertyNamingValidationTest**: Validation tests for new property names
- Tests verify JSON deserialization works correctly with new naming conventions

### Documentation Updates
- **MQClient-Usage-Guide.md**: Updated with new property names
- **RabbitMQClient-Usage-Guide.md**: Updated with new property names
- All configuration examples now use consistent naming

## 🎯 Key Benefits

### 1. **Consistency**
- Uniform property naming across all technologies
- Predictable patterns for developers
- Reduced cognitive load when working with configurations

### 2. **Maintainability**
- Clear separation between consumer and producer properties
- Hierarchical property organization
- Easy to extend for new technologies

### 3. **Type Safety**
- Strong typing through configuration classes
- Compile-time validation of property access
- IDE support with auto-completion

### 4. **Flexibility**
- Dynamic configuration loading from property sheets
- Environment-specific property overrides
- Runtime configuration updates

## 📚 Usage Guidelines

### For Developers
1. **Always use the new property naming convention** when creating configurations
2. **Reference these diagrams** when understanding system architecture
3. **Follow the established patterns** when adding new configuration classes
4. **Update tests** when modifying configuration properties

### For Operations Teams
1. **Use the deployment diagram** for infrastructure planning
2. **Reference the sequence diagram** for troubleshooting configuration issues
3. **Monitor configuration loading** using the established patterns
4. **Follow the property naming convention** in all property sheets

### For Architects
1. **Use the overview diagram** for system design discussions
2. **Reference the architecture diagram** for component relationships
3. **Ensure new components** follow the established patterns
4. **Maintain consistency** with the configuration management approach

## 🔄 Maintenance

These diagrams should be updated whenever:
- New configuration classes are added
- Property naming patterns change
- New technologies are integrated
- Architecture components are modified
- Deployment patterns evolve

## 📞 Support

For questions about the architecture or configuration management:
1. Review the relevant diagram first
2. Check the configuration class implementations
3. Examine the unit tests for examples
4. Consult the team for architectural decisions

---

**Last Updated**: After JSON property naming convention refactoring
**Status**: ✅ Current and Accurate
**Next Review**: When new technologies or major changes are introduced
