package com.dell.it.hip.strategy.flows;

import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.SerializationUtils;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.FlowSteps.StrictOrderConfig;
import com.dell.it.hip.core.registry.StrictOrderMetricRegistry;
import com.dell.it.hip.util.logging.WiretapService;
@Service
public class StrictOrderProcessorFlowStepStrategy implements FlowStepStrategy {

    private final StringRedisTemplate redisTemplate;
    private final RedissonClient redissonClient;
    private final WiretapService wiretapService;
    private final StrictOrderMetricRegistry metricRegistry;

    public StrictOrderProcessorFlowStepStrategy(
            StringRedisTemplate redisTemplate,
            RedissonClient redissonClient,
            WiretapService wiretapService,
            StrictOrderMetricRegistry metricRegistry
    ) {
        this.redisTemplate = redisTemplate;
        this.redissonClient = redissonClient;
        this.wiretapService = wiretapService;
        this.metricRegistry = metricRegistry;
    }

    @Override
    public String getType() {
        return "strictOrderProcessor";
    }

    @Override
    public List<Message<?>> executeStep(Message<?> message, FlowStepConfigRef stepConfigRef, HIPIntegrationDefinition def) throws Exception {
        StrictOrderConfig config = (StrictOrderConfig) def.getConfig(stepConfigRef.getPropertyRef(), StrictOrderConfig.class);

        Assert.notNull(config, "StrictOrderConfig must not be null");
        Assert.notNull(config.getOrderingKeys(), "orderingKeys cannot be null or empty");

        // Build the partition key
        String partitionKey = buildPartitionKey(def, config, message);

        // Extract the sequence number (required for ordering)
        Long seq = getSequenceNumber(message, config);
        if (seq == null) {
            wiretapService.tap(message, def, stepConfigRef, "error", "StrictOrder: No sequence number in message for partition " + partitionKey);
            metricRegistry.incrementMissing(def, stepConfigRef.getPropertyRef());
            return List.of(); // Discard or send to error channel
        }

        String redisZsetKey = strictOrderZSetKey(def, config, partitionKey);
        String redisSeqKey = strictOrderSeqKey(def, config, partitionKey);

        RLock lock = redissonClient.getLock(redisZsetKey + ":lock");
        lock.lock(10, TimeUnit.SECONDS);
        try {
            // Get the expected sequence number for this partition
            String expectedSeqStr = redisTemplate.opsForValue().get(redisSeqKey);
            long expectedSeq = (expectedSeqStr == null) ? seq : Long.parseLong(expectedSeqStr);

            if (seq == expectedSeq) {
                // Release this message and any consecutive queued
                List<Message<?>> ready = new ArrayList<>();
                ready.add(message);

                // Fetch buffered messages and check for further consecutive sequences
                boolean more = true;
                long nextSeq = expectedSeq + 1;
                while (more) {
                    Set<String> popped = redisTemplate.opsForZSet().rangeByScore(redisZsetKey, nextSeq, nextSeq);
                    if (popped == null || popped.isEmpty()) break;
                    String bufferedMsg = popped.iterator().next();
                    Message<?> m = deserialize(bufferedMsg);
                    Long bufferedSeq = getSequenceNumber(m, config);
                    if (bufferedSeq != null && bufferedSeq == nextSeq) {
                        ready.add(m);
                        redisTemplate.opsForZSet().remove(redisZsetKey, bufferedMsg);
                        nextSeq++;
                    } else {
                        more = false;
                    }
                }
                // Update expected seq
                redisTemplate.opsForValue().set(redisSeqKey, String.valueOf(nextSeq));
                metricRegistry.incrementInOrder(def, stepConfigRef.getPropertyRef());
                wiretapService.tap(message, def, stepConfigRef, "info", "StrictOrder: Released " + ready.size() + " messages for partition " + partitionKey);
                return ready;
            } else if (seq > expectedSeq) {
                // Buffer out-of-order message
                redisTemplate.opsForZSet().add(redisZsetKey, serialize(message), seq);
                metricRegistry.incrementOutOfOrder(def, stepConfigRef.getPropertyRef());
                wiretapService.tap(message, def, stepConfigRef, "warn", "StrictOrder: Buffered out-of-order msg seq " + seq + " (expected " + expectedSeq + ") partition " + partitionKey);
                return List.of();
            } else {
                // Duplicate or too-late message
                metricRegistry.incrementMissing(def, stepConfigRef.getPropertyRef());
                wiretapService.tap(message, def, stepConfigRef, "warn", "StrictOrder: Late or duplicate message seq " + seq + " for partition " + partitionKey);
                return List.of();
            }
        } finally {
            lock.unlock();
        }
    }

    /** Manual release for ops; returns all released messages. */
    public List<Message<?>> manualRelease(HIPIntegrationDefinition def, StrictOrderConfig config, List<String> orderingKeyValues, Long uptoSeq) {
        String partitionKey = buildPartitionKey(def, config, orderingKeyValues);
        String redisZsetKey = strictOrderZSetKey(def, config, partitionKey);

        RLock lock = redissonClient.getLock(redisZsetKey + ":lock");
        lock.lock(10, TimeUnit.SECONDS);
        try {
            Set<String> buffered = uptoSeq == null
                    ? redisTemplate.opsForZSet().range(redisZsetKey, 0, -1)
                    : redisTemplate.opsForZSet().rangeByScore(redisZsetKey, 0, uptoSeq);
            List<Message<?>> msgs = new ArrayList<>();
            if (buffered != null) {
                for (String raw : buffered) {
                    msgs.add(deserialize(raw));
                    redisTemplate.opsForZSet().remove(redisZsetKey, raw);
                }
            }
            // Optionally increment a manualRelease counter if you want
            return msgs;
        } finally {
            lock.unlock();
        }
    }

    // -- Key construction: Unique per ServiceManager, integration, version, flowstep, partition
    private String strictOrderZSetKey(HIPIntegrationDefinition def, StrictOrderConfig config, String partitionKey) {
        return "hip:strictorder:" +
                def.getServiceManagerName() + ":" +
                def.getHipIntegrationName() + ":" +
                def.getVersion() + ":" +
                config.getPropertyRef() + ":" +
                partitionKey;
    }

    private String strictOrderSeqKey(HIPIntegrationDefinition def, StrictOrderConfig config, String partitionKey) {
        return strictOrderZSetKey(def, config, partitionKey) + ":nextSeq";
    }

    // -- Partition Key
    private String buildPartitionKey(HIPIntegrationDefinition def, StrictOrderConfig config, Message<?> msg) {
        StringBuilder sb = new StringBuilder();
        for (String header : config.getOrderingKeys()) {
            Object val = msg.getHeaders().get(header);
            sb.append(header).append("=").append(val != null ? val : "null").append("|");
        }
        return sb.toString();
    }
    // Overload for manual release
    private String buildPartitionKey(HIPIntegrationDefinition def, StrictOrderConfig config, List<String> keyVals) {
        return String.join("|", keyVals);
    }

    // -- Extract seq number from header
    private Long getSequenceNumber(Message<?> message, StrictOrderConfig config) {
        Object seqObj = message.getHeaders().get(config.getSequenceHeader());
        if (seqObj instanceof Number) return ((Number) seqObj).longValue();
        if (seqObj instanceof String) try { return Long.parseLong((String) seqObj); } catch (Exception ignored) {}
        return null;
    }

    // --- (De)Serialization of Messages (use your preferred mechanism) ---
    private String serialize(Message<?> msg) {
        // You can use Jackson, or Spring’s message converter, etc.
        // For demo, just use Java serialization or Base64, but in prod use JSON!
        return Base64.getEncoder().encodeToString(SerializationUtils.serialize(msg));
    }

    private Message<?> deserialize(String s) {
        try {
            return (Message<?>) SerializationUtils.deserialize(Base64.getDecoder().decode(s));
        } catch (Exception e) {
            return null;
        }
    }
}