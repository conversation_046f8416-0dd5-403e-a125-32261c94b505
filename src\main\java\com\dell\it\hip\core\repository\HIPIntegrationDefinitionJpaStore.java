package com.dell.it.hip.core.repository;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationRequestEntity;

@Component("jpaStore") 
@ConditionalOnProperty(name = "integration.store.type", havingValue = "JPA", matchIfMissing = true)
public class HIPIntegrationDefinitionJpaStore implements HIPIntegrationDefinitionStore {

	@Autowired private HIPIntegrationDefinitionRepository repo;
    @Override
    public void save(HIPIntegrationRequestEntity entity) { repo.save(entity); }
    @Override
    public HIPIntegrationRequestEntity find(String serviceManagerName, String hipIntegrationName, String version) {
        return repo.findByServiceManagerNameAndHipIntegrationNameAndVersion(serviceManagerName, hipIntegrationName, version);
    }
    @Override
    public boolean exists(String serviceManagerName, String hipIntegrationName, String version) {
        return repo.existsByServiceManagerNameAndHipIntegrationNameAndVersion(serviceManagerName, hipIntegrationName, version);
    }
    @Override
    public List<HIPIntegrationRequestEntity> findByServiceManagerName(String serviceManagerName) {
        return repo.findByServiceManagerName(serviceManagerName);
    }
	@Override
	public void deleteByServiceManagerNameAndHipIntegrationNameAndVersion(String serviceManagerName,
			String hipIntegrationName, String version) {
		 HIPIntegrationRequestEntity entity = find(serviceManagerName, hipIntegrationName, version);
	        if (entity != null) repo.delete(entity);
		
	}

}
