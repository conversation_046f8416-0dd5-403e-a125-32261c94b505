package com.dell.it.hip.util.redis;

import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import com.dell.it.hip.config.HIPClusterEvent;
import com.dell.it.hip.core.HIPClusterCoordinationService;
import com.dell.it.hip.core.HIPIntegrationRuntimeService;
import com.dell.it.hip.util.ThrottleSettings;
import com.dell.it.hip.util.ThrottlingService;

/**
 * Production-grade Redis-backed implementation of cluster-wide throttling service for HIP integrations.
 */
@Service
@ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
public class RedisThrottlingService implements ThrottlingService {

    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private HIPIntegrationRuntimeService runtimeService;
    @Autowired
    private HIPClusterCoordinationService clusterCoordinationService;

    @Override
    public boolean tryConsumeToken(String serviceManagerName, String integrationId, String integrationVersion,
                                   String adapterId, ThrottleSettings settings) {
        if (settings == null || !settings.isEnabled()) return true;

        // Use ratelimit key for token bucket
        String bucketKey = HIPRedisKeyUtil.rateLimitKey(serviceManagerName, integrationId, integrationVersion, adapterId);
        Long count = redisTemplate.opsForValue().increment(bucketKey);

        if (count != null && count == 1L) {
            redisTemplate.expire(bucketKey, settings.getPeriodSeconds(), TimeUnit.SECONDS);
        }

        boolean allowed = count != null && count <= settings.getMaxMessagesPerPeriod();

        if (!allowed) {
            long ttl = redisTemplate.getExpire(bucketKey, TimeUnit.SECONDS);
            if (ttl > 0 && ttl <= 1) {
                clusterCoordinationService.publishClusterEvent(
                        HIPClusterEvent.forAdapterTarget(
                                "REFILL",
                                integrationId,
                                integrationVersion,
                                adapterId,
                                Map.of()
                        )
                );
            }
        }

        return allowed;
    }

    @Override
    public void resetThrottle(String serviceManagerName, String integrationId, String integrationVersion, String adapterId) {
        // Clear rate limit counter (token bucket)
        redisTemplate.delete(HIPRedisKeyUtil.rateLimitKey(serviceManagerName, integrationId, integrationVersion, adapterId));
        // Optionally reset throttle settings/config as well, if needed
        clusterCoordinationService.publishClusterEvent(
                HIPClusterEvent.forAdapterTarget(
                        "REFILL",
                        integrationId,
                        integrationVersion,
                        adapterId,
                        Map.of()
                )
        );
    }

    @Override
    public void updateThrottle(String serviceManagerName, String integrationId, String integrationVersion,
                               String adapterId, ThrottleSettings settings) {
        // Update the throttle settings/config in Redis
        runtimeService.updateThrottle(serviceManagerName, integrationId, integrationVersion, adapterId, settings);

        // Publish cluster-wide throttle update event
        clusterCoordinationService.publishClusterEvent(
                HIPClusterEvent.forAdapterTarget(
                        "THROTTLE_UPDATE",
                        integrationId,
                        integrationVersion,
                        adapterId,
                        Map.of("settings", settings)
                )
        );
    }

    @Override
    public ThrottleSettings getThrottleSettings(String serviceManagerName, String integrationId,
                                                String integrationVersion, String adapterId) {
        return runtimeService.getThrottleSettings(serviceManagerName, integrationId, integrationVersion, adapterId);
    }
}