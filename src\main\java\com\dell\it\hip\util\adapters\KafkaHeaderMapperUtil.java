package com.dell.it.hip.util.adapters;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import org.apache.kafka.common.header.Header;
import org.apache.kafka.common.header.Headers;
import org.apache.kafka.common.header.internals.RecordHeader;
import org.springframework.messaging.MessageHeaders;

public class KafkaHeaderMapperUtil {
    private KafkaHeaderMapperUtil() {
        // Utility class
    }

    /**
     * Copies Spring MessageHeaders into Kafka native Headers format.
     *
     * @param messageHeaders Spring headers.
     * @param kafkaHeaders   Kafka native headers.
     */
    public static void mapToKafkaHeaders(MessageHeaders messageHeaders, Headers kafkaHeaders) {
        if (messageHeaders == null || kafkaHeaders == null) {
            return;
        }

        for (String key : messageHeaders.keySet()) {
            Object value = messageHeaders.get(key);
            if (value != null) {
                kafkaHeaders.add(new RecordHeader(key, value.toString().getBytes(StandardCharsets.UTF_8)));
            }
        }
    }

    /**
     * Extracts headers from Spring MessageHeaders into a Map.
     *
    * @param messageHeaders Kafka headers.
     * @return Map of header key-value pairs.
     */
   public static Map<String, Object> extract(Headers messageHeaders) {
           Map<String, Object> headers = new HashMap<>();
           if (messageHeaders == null) {
               return headers;
           }

           for (Header header : messageHeaders.toArray()) {
               Object value = header.value();
               String key = header.key();
               if (value != null) {
                   headers.put(key, value);
               }
           }
           return headers;
       }
}