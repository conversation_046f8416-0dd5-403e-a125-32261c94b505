package com.dell.it.hip.exception;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

import java.util.Arrays;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.context.request.WebRequest;

import com.dell.it.hip.exception.GlobalExceptionHandler.ErrorResponse;

/**
 * Unit tests for GlobalExceptionHandler.
 */
@ExtendWith(MockitoExtension.class)
class GlobalExceptionHandlerTest {

    @InjectMocks
    private GlobalExceptionHandler globalExceptionHandler;

    @Mock
    private WebRequest webRequest;

    @Mock
    private MethodArgumentNotValidException methodArgumentNotValidException;

    @Mock
    private BindingResult bindingResult;

    @BeforeEach
    void setUp() {
        // Setup common mock behavior for tests that use webRequest
        lenient().when(webRequest.getDescription(false)).thenReturn("uri=/test/path");
    }

    @Test
    void testHandleIntegrationNotFound() {
        // Arrange
        IntegrationNotFoundException exception = new IntegrationNotFoundException("test-integration", "1.0");

        // Act
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleIntegrationNotFound(exception, webRequest);

        // Assert
        assertNotNull(response);
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());

        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals(404, errorResponse.getStatus());
        assertEquals("INTEGRATION_NOT_FOUND", errorResponse.getErrorCode());
        assertTrue(errorResponse.getMessage().contains("test-integration:1.0"));
        assertEquals("uri=/test/path", errorResponse.getPath());
        assertNotNull(errorResponse.getCorrelationId());
        assertNotNull(errorResponse.getTimestamp());
    }

    @Test
    void testHandleAdapterConfiguration() {
        // Arrange
        AdapterConfigurationException exception = new AdapterConfigurationException("http", "url", "invalid format");

        // Act
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleAdapterConfiguration(exception, webRequest);

        // Assert
        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());

        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals(400, errorResponse.getStatus());
        assertEquals("ADAPTER_CONFIGURATION_ERROR", errorResponse.getErrorCode());
        assertTrue(errorResponse.getMessage().contains("http"));
        assertTrue(errorResponse.getMessage().contains("url"));
    }

    @Test
    void testHandleIntegrationRegistration() {
        // Arrange
        IntegrationRegistrationException exception = new IntegrationRegistrationException("test-integration", "1.0", "Configuration invalid");

        // Act
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleIntegrationRegistration(exception, webRequest);

        // Assert
        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());

        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals(400, errorResponse.getStatus());
        assertEquals("INTEGRATION_REGISTRATION_ERROR", errorResponse.getErrorCode());
        assertTrue(errorResponse.getMessage().contains("test-integration"));
        assertTrue(errorResponse.getMessage().contains("1.0"));
        assertNotNull(errorResponse.getCorrelationId());
        assertNotNull(errorResponse.getTimestamp());
    }

    @Test
    void testHandleIntegrationOperation() {
        // Arrange
        IntegrationOperationException exception = new IntegrationOperationException("unregister", "test-integration", "1.0", "Operation failed");

        // Act
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleIntegrationOperation(exception, webRequest);

        // Assert
        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());

        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals(400, errorResponse.getStatus());
        assertEquals("INTEGRATION_UNREGISTER_ERROR", errorResponse.getErrorCode());
        assertTrue(errorResponse.getMessage().contains("unregister"));
        assertTrue(errorResponse.getMessage().contains("test-integration"));
        assertTrue(errorResponse.getMessage().contains("1.0"));
        assertNotNull(errorResponse.getCorrelationId());
        assertNotNull(errorResponse.getTimestamp());
    }

    @Test
    void testHandleIntegrationOperationDifferentOperations() {
        // Test different operation types get different error codes
        String[] operations = {"pause", "resume", "shutdown", "unknown"};
        String[] expectedErrorCodes = {
            "INTEGRATION_PAUSE_ERROR",
            "INTEGRATION_RESUME_ERROR",
            "INTEGRATION_SHUTDOWN_ERROR",
            "INTEGRATION_OPERATION_ERROR"
        };

        for (int i = 0; i < operations.length; i++) {
            // Arrange
            IntegrationOperationException exception = new IntegrationOperationException(
                operations[i], "test-integration", "1.0", "Operation failed");

            // Act
            ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleIntegrationOperation(exception, webRequest);

            // Assert
            assertNotNull(response);
            assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());

            ErrorResponse errorResponse = response.getBody();
            assertNotNull(errorResponse);
            assertEquals(expectedErrorCodes[i], errorResponse.getErrorCode());
        }
    }

    @Test
    void testHandleThrottleLimitExceeded() {
        // Arrange
        ThrottleLimitExceededException exception = new ThrottleLimitExceededException(
                "test-integration", "1.0", 150, 100, 60);

        // Act
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleThrottleLimitExceeded(exception, webRequest);

        // Assert
        assertNotNull(response);
        assertEquals(HttpStatus.TOO_MANY_REQUESTS, response.getStatusCode());

        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals(429, errorResponse.getStatus());
        assertEquals("THROTTLE_LIMIT_EXCEEDED", errorResponse.getErrorCode());
        assertTrue(errorResponse.getMessage().contains("test-integration:1.0"));
    }

    @Test
    void testHandleExternalSystemUnavailable() {
        // Arrange
        ExternalSystemUnavailableException exception = new ExternalSystemUnavailableException(
                "External API", "http://api.example.com", new RuntimeException("Connection timeout"));

        // Act
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleExternalSystemUnavailable(exception, webRequest);

        // Assert
        assertNotNull(response);
        assertEquals(HttpStatus.SERVICE_UNAVAILABLE, response.getStatusCode());

        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals(503, errorResponse.getStatus());
        assertEquals("EXTERNAL_SYSTEM_UNAVAILABLE", errorResponse.getErrorCode());
        assertTrue(errorResponse.getMessage().contains("External API"));
    }

    @Test
    void testHandleMessageTransformation() {
        // Arrange
        MessageTransformationException exception = new MessageTransformationException(
                "json-to-xml", "JSON", "XML", new RuntimeException("Invalid JSON"));

        // Act
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleMessageTransformation(exception, webRequest);

        // Assert
        assertNotNull(response);
        assertEquals(HttpStatus.UNPROCESSABLE_ENTITY, response.getStatusCode());

        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals(422, errorResponse.getStatus());
        assertEquals("MESSAGE_TRANSFORMATION_ERROR", errorResponse.getErrorCode());
        assertTrue(errorResponse.getMessage().contains("JSON"));
        assertTrue(errorResponse.getMessage().contains("XML"));
    }

    @Test
    void testHandleRoutingDecision() {
        // Arrange
        RoutingDecisionException exception = new RoutingDecisionException(
                "test-rule", "test-integration", "1.0", new RuntimeException("No matching rule"));

        // Act
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleRoutingDecision(exception, webRequest);

        // Assert
        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());

        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals(400, errorResponse.getStatus());
        assertEquals("ROUTING_DECISION_ERROR", errorResponse.getErrorCode());
        assertTrue(errorResponse.getMessage().contains("test-rule"));
    }

    @Test
    void testHandleValidationExceptions() {
        // Arrange
        FieldError fieldError1 = new FieldError("testObject", "field1", "Field1 is required");
        FieldError fieldError2 = new FieldError("testObject", "field2", "Field2 must be positive");

        when(methodArgumentNotValidException.getBindingResult()).thenReturn(bindingResult);
        when(bindingResult.getAllErrors()).thenReturn(Arrays.asList(fieldError1, fieldError2));

        // Act
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleValidationExceptions(
                methodArgumentNotValidException, webRequest);

        // Assert
        assertNotNull(response);
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());

        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals(400, errorResponse.getStatus());
        assertEquals("VALIDATION_ERROR", errorResponse.getErrorCode());
        assertEquals("Validation failed for request fields", errorResponse.getMessage());

        assertNotNull(errorResponse.getValidationErrors());
        assertEquals(2, errorResponse.getValidationErrors().size());
        assertEquals("Field1 is required", errorResponse.getValidationErrors().get("field1"));
        assertEquals("Field2 must be positive", errorResponse.getValidationErrors().get("field2"));
    }

    @Test
    void testHandleAccessDenied() {
        // Arrange
        AccessDeniedException exception = new AccessDeniedException("Access is denied");

        // Act
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleAccessDenied(exception, webRequest);

        // Assert
        assertNotNull(response);
        assertEquals(HttpStatus.FORBIDDEN, response.getStatusCode());

        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals(403, errorResponse.getStatus());
        assertEquals("ACCESS_DENIED", errorResponse.getErrorCode());
        assertTrue(errorResponse.getMessage().contains("Access denied"));
    }

    @Test
    void testHandleGenericException() {
        // Arrange
        RuntimeException exception = new RuntimeException("Unexpected error occurred");

        // Act
        ResponseEntity<ErrorResponse> response = globalExceptionHandler.handleGenericException(exception, webRequest);

        // Assert
        assertNotNull(response);
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.getStatusCode());

        ErrorResponse errorResponse = response.getBody();
        assertNotNull(errorResponse);
        assertEquals(500, errorResponse.getStatus());
        assertEquals("INTERNAL_SERVER_ERROR", errorResponse.getErrorCode());
        assertTrue(errorResponse.getMessage().contains("unexpected error occurred"));
        assertTrue(errorResponse.getMessage().contains("correlation ID"));
        assertNotNull(errorResponse.getCorrelationId());
    }

    @Test
    void testErrorResponseStructure() {
        // Test the ErrorResponse class structure
        ErrorResponse errorResponse = new ErrorResponse(
                400, "TEST_ERROR", "Test message", "/test/path", "test-correlation-id");

        assertEquals(400, errorResponse.getStatus());
        assertEquals("TEST_ERROR", errorResponse.getErrorCode());
        assertEquals("Test message", errorResponse.getMessage());
        assertEquals("/test/path", errorResponse.getPath());
        assertEquals("test-correlation-id", errorResponse.getCorrelationId());
        assertNotNull(errorResponse.getTimestamp());
    }

    @Test
    void testCorrelationIdGeneration() {
        // Test that different exceptions generate different correlation IDs
        IntegrationNotFoundException exception1 = new IntegrationNotFoundException("integration1", "1.0");
        IntegrationNotFoundException exception2 = new IntegrationNotFoundException("integration2", "1.0");

        ResponseEntity<ErrorResponse> response1 = globalExceptionHandler.handleIntegrationNotFound(exception1, webRequest);
        ResponseEntity<ErrorResponse> response2 = globalExceptionHandler.handleIntegrationNotFound(exception2, webRequest);

        String correlationId1 = response1.getBody().getCorrelationId();
        String correlationId2 = response2.getBody().getCorrelationId();

        assertNotNull(correlationId1);
        assertNotNull(correlationId2);
        assertNotEquals(correlationId1, correlationId2);
    }
}
