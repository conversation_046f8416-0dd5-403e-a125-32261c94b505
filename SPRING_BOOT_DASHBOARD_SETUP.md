# 🚀 Spring Boot Dashboard Configuration Guide

## ✅ **Configuration Complete!**

I've successfully configured your VS Code Spring Boot Dashboard to run the HIP Services application with the required environment variables.

## 🔧 **What Was Configured**

### 1. **Launch Configuration Updated**
Added a new launch configuration in `.vscode/launch.json`:

```json
{
    "type": "java",
    "name": "Launch HipServicesApplication (Cloud Profile)",
    "request": "launch",
    "mainClass": "com.dell.it.hip.HipServicesApplication",
    "projectName": "hip-services",
    "args": "",
    "vmArgs": "-Xmx2048m -Xms512m",
    "env": {
        "JAVA_HOME": "C:\\Program Files\\Eclipse Adoptium\\jdk-********-hotspot",
        "configproperties_sheet_name": "shared-service-manger",
        "configserver_uri": "https://configserveruser:<EMAIL>",
        "SPRING_PROFILES_ACTIVE": "cloud"
    },
    "console": "internalConsole",
    "stopOnEntry": false,
    "internalConsoleOptions": "openOnSessionStart"
}
```

### 2. **Cloud Profile Configuration**
Created `src/main/resources/application-cloud.yaml` with:
- Configuration server integration
- Environment-specific settings
- Logging configuration for cloud environment
- Management endpoints configuration

### 3. **Environment Variables Set**
- ✅ `configproperties_sheet_name=shared-service-manger`
- ✅ `configserver_uri=https://configserveruser:<EMAIL>`
- ✅ `SPRING_PROFILES_ACTIVE=cloud`

## 🎯 **How to Use Spring Boot Dashboard**

### **Step 1: Open Spring Boot Dashboard**
1. In VS Code, press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
2. Type "Spring Boot Dashboard: Focus on Spring Boot Dashboard View"
3. Or click on the Spring Boot icon in the Activity Bar (left sidebar)

### **Step 2: Locate Your Application**
In the Spring Boot Dashboard, you should see:
- **hip-services** (your project)
- **HipServicesApplication** (main class)

### **Step 3: Launch with Environment Variables**
**Option A: Using Launch Configuration**
1. Go to Run and Debug view (`Ctrl+Shift+D`)
2. Select "Launch HipServicesApplication (Cloud Profile)" from dropdown
3. Click the green play button ▶️

**Option B: Using Spring Boot Dashboard**
1. In Spring Boot Dashboard, right-click on "HipServicesApplication"
2. Select "Start" or "Debug"
3. The application will use the cloud profile automatically

### **Step 4: Verify Environment Variables**
Once the application starts, check the console output for:
```
Active profiles: cloud
Config server URI: https://configserveruser:<EMAIL>
Properties sheet name: shared-service-manger
```

## 🔍 **Verification Steps**

### **1. Check Application Startup**
Look for these log messages:
```
INFO  - The following profiles are active: cloud
INFO  - Located property source: CompositePropertySource
INFO  - Started HipServicesApplication in X.XXX seconds
```

### **2. Verify Configuration Server Connection**
Check for:
```
DEBUG - Fetching config from server at: https://configserveruser:<EMAIL>
INFO  - Located property source: configService
```

### **3. Test Actuator Endpoints**
Once running, test these URLs:
- Health: `http://localhost:8080/hip-services/actuator/health`
- Config Properties: `http://localhost:8080/hip-services/actuator/configprops`
- Environment: `http://localhost:8080/hip-services/actuator/env`

## 🛠️ **Available Launch Configurations**

You now have three launch configurations:

1. **Launch HipServicesApplication** - Default/Dev profile
2. **Launch HipServicesApplication (Test Profile)** - Test environment
3. **Launch HipServicesApplication (Cloud Profile)** - Cloud environment with your specific variables

## 🔧 **Spring Boot Dashboard Features**

### **Start/Stop Applications**
- ▶️ **Start**: Launch the application
- ⏹️ **Stop**: Stop the running application
- 🔄 **Restart**: Restart the application
- 🐛 **Debug**: Start in debug mode

### **View Application Details**
- **Beans**: View Spring beans
- **Mappings**: View request mappings
- **Properties**: View configuration properties
- **Loggers**: Manage log levels

### **Live Reload**
- Automatic restart when code changes
- Hot swapping for certain changes
- DevTools integration

## 🚨 **Troubleshooting**

### **If Application Doesn't Appear in Dashboard**
1. Ensure Spring Boot Dashboard extension is installed
2. Reload VS Code window: `Ctrl+Shift+P` → "Developer: Reload Window"
3. Check that `@SpringBootApplication` annotation is present in main class

### **If Environment Variables Don't Work**
1. Verify the launch configuration in `.vscode/launch.json`
2. Check that the correct profile is selected
3. Look for environment variable values in application logs

### **If Configuration Server Connection Fails**
1. Check network connectivity to Dell's configuration server
2. Verify VPN connection if required
3. Check credentials in the `configserver_uri`

### **Common Issues**
- **Port already in use**: Change server port in application-cloud.yaml
- **Memory issues**: Increase heap size in vmArgs
- **Authentication errors**: Verify configuration server credentials

## 🎉 **Success Indicators**

You'll know everything is working when:
- ✅ Application appears in Spring Boot Dashboard
- ✅ Application starts with "cloud" profile active
- ✅ Configuration server connection is established
- ✅ All three environment variables are loaded
- ✅ Actuator endpoints are accessible
- ✅ Application logs show successful startup

## 📝 **Next Steps**

1. **Test the configuration**: Launch the application using the Spring Boot Dashboard
2. **Verify connectivity**: Check that the configuration server is reachable
3. **Monitor logs**: Watch for any configuration-related errors
4. **Test endpoints**: Verify that your application endpoints work correctly

## 🔗 **Useful Commands**

```bash
# Check if application is running
curl http://localhost:8080/hip-services/actuator/health

# View environment variables
curl http://localhost:8080/hip-services/actuator/env

# View configuration properties
curl http://localhost:8080/hip-services/actuator/configprops
```

Your Spring Boot Dashboard is now fully configured and ready to run your HIP Services application with the Dell configuration server integration! 🚀
