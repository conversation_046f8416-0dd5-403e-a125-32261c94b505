package com.dell.it.hip.strategy.flows;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.dell.it.hip.config.rules.Rule;
import com.dell.it.hip.strategy.flows.FlowStepStrategy;
import com.dell.it.hip.strategy.flows.rules.RuleCache;
import com.dell.it.hip.util.logging.WiretapService;
import com.emc.it.eis.contivo.integration.transformer.ContivoTransformer;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.ContivoMap;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.FlowSteps.MappingTransformerFlowStepConfig;
import com.dell.it.hip.core.registry.ContivoTransformerRegistry;
import com.dell.it.hip.strategy.flows.rules.RuleProcessor;
import com.dell.it.hip.util.contivoUtils.ContivoTransformerService;
import org.springframework.stereotype.Component;

public class MappingTransformerFlowStepStrategy implements FlowStepStrategy {
    private final RuleProcessor ruleProcessor;
    private final ContivoTransformerRegistry transformerRegistry;
    private ContivoTransformerService contivosrvice;

    public MappingTransformerFlowStepStrategy(
            RuleProcessor ruleProcessor,
            ContivoTransformerRegistry transformerRegistry
    ) {
        this.ruleProcessor = ruleProcessor;
        this.transformerRegistry = transformerRegistry;
    }

    public String getType() {
        return "mappingTrasformer";
    }

    public List<Message<?>> executeStep(
            Message<?> message,
            FlowStepConfigRef stepConfigRef,
            HIPIntegrationDefinition def
    ) throws Exception {
        MappingTransformerFlowStepConfig stepConfig = (MappingTransformerFlowStepConfig) def.getConfig(stepConfigRef.getPropertyRef(), MappingTransformerFlowStepConfig.class);
        Map<String, Object> context = new HashMap<>();
        boolean dbBacked = stepConfig.isDbBacked();
        Message<?> messageWithMap = ruleProcessor.processRules(
                message,
                stepConfig.getRuleRefs(),
                def,
                stepConfigRef,
                context,
                dbBacked
        );

        ContivoMap contivoMap = (ContivoMap) context.get("contivoMap");
        if (contivoMap == null) {
            throw new RuntimeException("No ContivoMap found in context after rule processing.");
        }

        String transformedPayload = contivosrvice.transform((String)messageWithMap.getPayload(), contivoMap);

        Message<?> outputMsg = MessageBuilder.withPayload(transformedPayload)
                .copyHeaders(messageWithMap.getHeaders())
                .setHeader("hip.contivoMapId", contivoMap.getMapIdentifier())
                .setHeader("hip.contivoMapVersion", contivoMap.getMapIdentifierVersion())
                .setHeader("hip.contivoMapName", contivoMap.getMapName())
                .build();

        return Collections.singletonList(outputMsg);
    }
}