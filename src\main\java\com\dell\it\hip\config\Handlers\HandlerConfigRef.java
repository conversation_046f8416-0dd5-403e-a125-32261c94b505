package com.dell.it.hip.config.Handlers;

import java.io.Serializable;

import com.dell.it.hip.config.ConfigRef;

public class HandlerConfigRef implements Serializable, ConfigRef {
    private String type;         // e.g., "kafka", "sftp", "https", etc.
    private String role;         // e.g., "primary", "fallback"
    private String id;           // Unique within HIPIntegration
    private String propertyRef;  // Property sheet key for this handler's configuration
    private String fallbackRef; // propertyRef of fallback handler

    // Getter & setter

    public HandlerConfigRef() {}

    public HandlerConfigRef(String type, String role, String id, String propertyRef) {
        this.type = type;
        this.role = role;
        this.id = id;
        this.propertyRef = propertyRef;
    }

    // Getters & setters
    public String getType() { return type; }
    public void setType(String type) { this.type = type; }

    public String getRole() { return role; }
    public void setRole(String role) { this.role = role; }

    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getPropertyRef() { return propertyRef; }
    public void setPropertyRef(String propertyRef) { this.propertyRef = propertyRef; }
    public String getFallbackRef() { return fallbackRef; }
    public void setFallbackRef(String fallbackRef) { this.fallbackRef = fallbackRef; }
}