package com.dell.it.hip.strategy.flows;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.AggregatorFlowStepConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.util.logging.WiretapService;

/**
 * Aggregates messages in a cluster-wide, header/grouped way.
 */
@Component("aggregator")
public class AggregatorFlowStepStrategy implements FlowStepStrategy {

    private final RedissonClient redissonClient;
    private final WiretapService wiretapService;
    private static final String AGGREGATE_MAP_PREFIX = "hip:aggregate:";

    public AggregatorFlowStepStrategy(RedissonClient redissonClient, WiretapService wiretapService) {
        this.redissonClient = redissonClient;
        this.wiretapService = wiretapService;
    }

    @Override
    public String getType() {
        return "aggregator";
    }

    @Override
    public List<Message<?>> executeStep(Message<?> message, FlowStepConfigRef stepConfigRef, HIPIntegrationDefinition def) throws Exception {
        AggregatorFlowStepConfig config = (AggregatorFlowStepConfig) def.getConfig(stepConfigRef.getPropertyRef(), AggregatorFlowStepConfig.class);
        Assert.notNull(config, "AggregatorFlowStepConfig must not be null");

        String groupKey = buildGroupKey(def, config, message);
        String mapKey = buildAggregateMapKey(def, config, groupKey);

        // Get or create a cluster-wide cache for this batch group
        RMapCache<String, AggregationState> batchMap = redissonClient.getMapCache(mapKey);

        // Lock for this group
        String lockKey = mapKey + ":lock";
        var lock = redissonClient.getLock(lockKey);
        lock.lock(5, TimeUnit.SECONDS);
        try {
            AggregationState state = batchMap.get(groupKey);
            if (state == null) {
                state = new AggregationState();
                state.setFirstMessageTime(Instant.now().toEpochMilli());
                state.setHeaders(message.getHeaders());
            }
            state.addMessage(message);
            batchMap.put(groupKey, state, config.getBatchTimeoutMs(), TimeUnit.MILLISECONDS);

            // Release condition 1: Size threshold met
            if (state.getMessages().size() >= config.getBatchSize()) {
                batchMap.remove(groupKey);
                wiretapService.tap(message, def, stepConfigRef, "info", "Aggregator: Batch size reached for group " + groupKey);
                return List.of(buildAggregatedMessage(state, config));
            }

            // Release condition 2: Timeout expired (handed via scheduled scan, optional)
            // For demo: You may wish to schedule a Redisson map entry expiration event handler, or check on each new message.
            long elapsed = Instant.now().toEpochMilli() - state.getFirstMessageTime();
            if (elapsed >= config.getBatchTimeoutMs()) {
                batchMap.remove(groupKey);
                wiretapService.tap(message, def, stepConfigRef, "info", "Aggregator: Batch timeout for group " + groupKey);
                return List.of(buildAggregatedMessage(state, config));
            }

            // Not enough for batch yet, keep waiting
            return Collections.emptyList();
        } finally {
            lock.unlock();
        }
    }

    private Message<?> buildAggregatedMessage(AggregationState state, AggregatorFlowStepConfig config) {
        // 1. Aggregate payloads
        String payload = state.getMessages().stream()
                .map(msg -> msg.getPayload().toString())
                .collect(Collectors.joining(",\n")); // User can customize separator if needed

        // 2. Aggregate headers
        Map<String, Object> aggregatedHeaders = aggregateHeaders(state.getMessages(), config);

        return MessageBuilder.withPayload(payload)
                .copyHeaders(aggregatedHeaders)
                .setHeader("HIP.aggregatedMessageCount", state.getMessages().size())
                .build();
    }

    // -- Cluster-wide group key: ServiceManager:Integration:Version:Step:GroupValue --
    private String buildAggregateMapKey(HIPIntegrationDefinition def, AggregatorFlowStepConfig config, String groupKey) {
        return AGGREGATE_MAP_PREFIX +
                def.getServiceManagerName() + ":" +
                def.getHipIntegrationName() + ":" +
                def.getVersion() + ":" +
                config.getPropertyRef() + ":" +
                (groupKey == null ? "" : groupKey);
    }

    private String buildGroupKey(HIPIntegrationDefinition def, AggregatorFlowStepConfig config, Message<?> msg) {
        if (config.getGroupByHeaders() == null || config.getGroupByHeaders().isEmpty()) return "default";
        StringBuilder sb = new StringBuilder();
        for (String header : config.getGroupByHeaders()) {
            Object val = msg.getHeaders().get(header);
            sb.append(header).append("=").append(val != null ? val : "null").append("|");
        }
        return sb.toString();
    }

    /** Aggregates headers according to best-practices described above. */
    private Map<String, Object> aggregateHeaders(List<Message<?>> batch, AggregatorFlowStepConfig config) {
        Map<String, Object> aggregateHeaders = new HashMap<>();
        if (batch.isEmpty()) return aggregateHeaders;

        // 1. Copy all headers from the first message (default)
        aggregateHeaders.putAll(batch.get(0).getHeaders());

        // 2. Collect all header names from all messages
        Set<String> allHeaderNames = batch.stream()
                .flatMap(msg -> msg.getHeaders().keySet().stream())
                .collect(Collectors.toSet());

        // 3. Aggregate header values (traceparent and others)
        for (String header : allHeaderNames) {
            List<Object> values = batch.stream()
                    .map(m -> m.getHeaders().get(header))
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
            if (values.size() == 1) {
                aggregateHeaders.put(header, values.get(0));
            } else if ("traceparent".equalsIgnoreCase(header)) {
                aggregateHeaders.put(config.getAggregateTraceHeaderName() != null
                        ? config.getAggregateTraceHeaderName()
                        : "HIP.traceParents", values);
            } else {
                // Only aggregate as list if asked, or for key headers
                if (config.getPreserveHeaders() != null && config.getPreserveHeaders().contains(header)) {
                    aggregateHeaders.put("HIP.aggregatedHeaders." + header, values);
                }
                // Optionally, always aggregate as list:
                // aggregateHeaders.put("HIP.aggregatedHeaders." + header, values);
            }
        }
        aggregateHeaders.put("HIP.aggregatedMessageCount", batch.size());
        return aggregateHeaders;
    }

    /** Inner class: tracks aggregation state per group */
    public static class AggregationState implements java.io.Serializable {
        private List<Message<?>> messages = new ArrayList<>();
        private Map<String, Object> headers = new HashMap<>();
        private long firstMessageTime;
        public List<Message<?>> getMessages() { return messages; }
        public void addMessage(Message<?> msg) { this.messages.add(msg); }
        public Map<String, Object> getHeaders() { return headers; }
        public void setHeaders(Map<String, Object> headers) { this.headers = headers; }
        public long getFirstMessageTime() { return firstMessageTime; }
        public void setFirstMessageTime(long firstMessageTime) { this.firstMessageTime = firstMessageTime; }
    }
}