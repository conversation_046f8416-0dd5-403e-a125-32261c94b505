package com.dell.it.hip.strategy.flows;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.dell.it.hip.config.rules.Rule;
import com.dell.it.hip.strategy.flows.rules.RuleCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowRoutingConfig;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.core.ServiceManager;
import com.dell.it.hip.strategy.flows.rules.RuleProcessor;
import com.dell.it.hip.util.logging.WiretapService;

@Component("flowRouting")
public class FlowRoutingFlowStepStrategy implements FlowStepStrategy {

    @Autowired private ServiceManager serviceManager;
    @Autowired private WiretapService wiretapService;
    @Autowired private RuleProcessor ruleProcessor;

    @Override
    public List<Message<?>> executeStep(Message<?> message, FlowStepConfigRef stepConfigRef, HIPIntegrationDefinition def) {
        // No dbBacked option here; always use getRulesForIntegration()
        FlowRoutingConfig config = (FlowRoutingConfig) def.getConfig(stepConfigRef.getPropertyRef(), FlowRoutingConfig.class);

        // Evaluate rules: expects RuleProcessor to set "HIP.targetFlowChannels" in message header
        Map<String, Object> context = new HashMap<>();
        Message<?> processed = ruleProcessor.processRules(
                message,
                def,
                stepConfigRef,
                context
        );
        @SuppressWarnings("unchecked")
        List<String> targetChannels = (List<String>) processed.getHeaders().get("HIP.targetFlowChannels");

        if (targetChannels == null || targetChannels.isEmpty()) {
            wiretapService.tap(
                    message,
                    def,
                    stepConfigRef,
                    "terminated",
                    "No routing channels found after rule processing for FlowRoutingFlowStep [" + stepConfigRef.getPropertyRef() + "], message terminated."
            );
            // If TransactionLoggingUtil is used:
            // TransactionLoggingUtil.logEvent("FlowRoutingFlowStepStrategy", "terminated", stepConfigRef, message);
            return Collections.emptyList();
        }

        List<Message<?>> out = new ArrayList<>();
        for (String channelName : targetChannels) {
            MessageChannel channel = serviceManager.getChannelByName(channelName);
            if (channel != null) {
                Message<?> routedMsg = MessageBuilder.fromMessage(processed).build();
                out.add(routedMsg);
                wiretapService.tap(
                        routedMsg,
                        def,
                        stepConfigRef,
                        "info",
                        "Routed message to channel: " + channelName
                );
            } else {
                wiretapService.tap(
                        message,
                        def,
                        stepConfigRef,
                        "warn",
                        "Target channel not found in ServiceManager: " + channelName
                );
            }
        }
        return out;
    }

    @Override
    public String getType() { return "flowRouting"; }
}