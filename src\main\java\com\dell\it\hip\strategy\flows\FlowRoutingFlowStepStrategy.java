package com.dell.it.hip.strategy.flows;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.core.ServiceManager;
import com.dell.it.hip.strategy.flows.rules.RuleProcessor;
import com.dell.it.hip.util.OpenTelemetryPropagationUtil;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;

import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Scope;

/**
 * FlowRoutingFlowStepStrategy:
 * - Evaluates rules using RuleProcessor.
 * - Reads "HIP.targetFlowChannels" from message header.
 * - Sends routed messages to the appropriate channels using ServiceManager.
 * - Does NOT pass messages to the default pipeline after routing.
 */
@Component("flowRouting")
public class FlowRoutingFlowStepStrategy implements FlowStepStrategy {

    @Autowired private ServiceManager serviceManager;
    @Autowired private WiretapService wiretapService;
    @Autowired private TransactionLoggingUtil transactionLoggingUtil;
    @Autowired private RuleProcessor ruleProcessor;
    
    @Autowired
    private OpenTelemetryPropagationUtil openTelemetryPropagationUtil;
    
    @Autowired
    private Tracer tracer;

    @Override
    public List<Message<?>> executeStep(
            Message<?> message,
            FlowStepConfigRef stepConfigRef,
            HIPIntegrationDefinition def
    ) {
       /* FlowRoutingConfig config = (FlowRoutingConfig) def.getConfig(
                stepConfigRef.getPropertyRef(), FlowRoutingConfig.class);*/

        // Rule evaluation: expects RuleProcessor to set "HIP.targetFlowChannels" in message header
        Map<String, Object> context = new HashMap<>();
        Message<?> processed = ruleProcessor.processRules(
                message,
                def,
                stepConfigRef,
                context
        );

        @SuppressWarnings("unchecked")
        List<String> targetChannels = (List<String>) processed.getHeaders().get("HIP.targetFlowChannels");

        if (targetChannels == null || targetChannels.isEmpty()) {
            String msg = "No routing channels found after rule processing for FlowRoutingFlowStep [" +
                    stepConfigRef.getPropertyRef() + "], message terminated.";
            wiretapService.tap(
                    message,
                    def,
                    stepConfigRef,
                    "terminated",
                    msg
            );
            //TODO: fix logWarn in transactionLoggingUtil
          /*  transactionLoggingUtil.logWarn(
                    message, def, stepConfigRef, "FlowRoutingFlowStepStrategy", msg
            );*/
            return Collections.emptyList();
        }

        for (String channelName : targetChannels) {
            MessageChannel channel = serviceManager.getChannelByName(channelName);
            if (channel != null) {
                Message<?> routedMsg = MessageBuilder.fromMessage(processed).build();
                
                Span span = tracer.spanBuilder("route-message")
                        .setNoParent()  // NEW TRACE
                        .setSpanKind(SpanKind.PRODUCER)
                        .startSpan();
                
                try(Scope scope = span.makeCurrent()) {
                	
                	String traceId = "Trace ID: "+ span.getSpanContext().getTraceId();
                	TransactionLoggingUtil.logInfo(routedMsg, def, stepConfigRef, traceId);
                	
                	Message<?> routedMsgWithTrace = openTelemetryPropagationUtil.injectTraceContext(routedMsg);

                    channel.send(routedMsgWithTrace);
                    String infoMsg = "Routed message to channel: " + channelName;
                    wiretapService.tap(routedMsgWithTrace, def, stepConfigRef, "info", infoMsg);
                    //TODO: fix transactionLoggingUtil
                 //   transactionLoggingUtil.logInfo(routedMsg, def, stepConfigRef, "FlowRoutingFlowStepStrategy", infoMsg);
                } catch (Exception ex) {
                    String errorMsg = "Failed to send to channel " + channelName + ": " + ex.getMessage();
                    wiretapService.tap(routedMsg, def, stepConfigRef, "error", errorMsg);
                    TransactionLoggingUtil.logError(routedMsg, def, stepConfigRef, "FlowRoutingFlowStepStrategy", errorMsg);
                }finally {
                    span.end();
                }
            } else {
                String warnMsg = "Target channel not found in ServiceManager: " + channelName;
                wiretapService.tap(
                        message,
                        def,
                        stepConfigRef,
                        "warn",
                        warnMsg
                );
                //TODO: add logWarn method in transactionLoggingUtil
               /* transactionLoggingUtil.logWarn(
                        message, def, stepConfigRef, "FlowRoutingFlowStepStrategy", warnMsg
                );*/
            }
        }

        // Do NOT return messages to the next pipeline step (routing is complete here).
        return Collections.emptyList();
    }

    @Override
    public String getType() {
        return "flowRouting";
    }
}