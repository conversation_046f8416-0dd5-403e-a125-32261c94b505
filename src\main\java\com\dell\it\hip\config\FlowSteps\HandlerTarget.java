package com.dell.it.hip.config.FlowSteps;

public class HandlerTarget {
    private String primaryHandler;
    private String fallbackHandler;

    // Optionally: add role/type/priority if future extensibility is needed.

    public HandlerTarget() {}
    public HandlerTarget(String primaryHandler, String fallbackHandler) {
        this.primaryHandler = primaryHandler;
        this.fallbackHandler = fallbackHandler;
    }

    // Getters and Setters
    public String getPrimaryHandler() { return primaryHandler; }
    public void setPrimaryHandler(String primaryHandler) { this.primaryHandler = primaryHandler; }
    public String getFallbackHandler() { return fallbackHandler; }
    public void setFallbackHandler(String fallbackHandler) { this.fallbackHandler = fallbackHandler; }

    @Override
    public String toString() {
        return "HandlerTarget{primaryHandler='" + primaryHandler + "', fallbackHandler='" + fallbackHandler + "'}";
    }
}
