package com.dell.it.hip.util.validation;
public class StructuralValidator {

    public static boolean validate(String payload, String format) {
        switch (format) {
            case "EDI_X12":
                return EdiValidator.structurallyValidateX12(payload);
            case "EDI_EDIFACT":
                return EdiValidator.structurallyValidateEdifact(payload);
            case "XML":
                return XmlSchemaValidator.isWellFormed(payload);
            case "JSON":
                return JsonSchemaValidator.isWellFormed(payload);
            case "CSV":
                return CsvSchemaValidator.hasValidStructure(payload);
            default:
                return false;
        }
    }
}