package com.dell.it.hip.integration;

import static org.junit.jupiter.api.Assumptions.assumeTrue;

import java.time.Duration;

import org.junit.jupiter.api.BeforeAll;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.DockerClientFactory;
import org.testcontainers.containers.GenericContainer;
import org.testcontainers.containers.KafkaContainer;
import org.testcontainers.containers.PostgreSQLContainer;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;
import org.testcontainers.utility.DockerImageName;

/**
 * Base class for integration tests using TestContainers.
 * Provides shared container setup and Docker environment validation.
 */
@SpringBootTest
@ActiveProfiles("test")
@Testcontainers
public abstract class BaseIntegrationTest {

    private static final Logger logger = LoggerFactory.getLogger(BaseIntegrationTest.class);

    // Shared containers for all integration tests
    @Container
    protected static PostgreSQLContainer<?> postgres = new PostgreSQLContainer<>(DockerImageName.parse("postgres:15-alpine"))
            .withDatabaseName("hip_test")
            .withUsername("test")
            .withPassword("test")
            .withStartupTimeout(Duration.ofMinutes(2))
            .withReuse(true);

    @Container
    protected static GenericContainer<?> redis = new GenericContainer<>(DockerImageName.parse("redis:6.2.6"))
            .withExposedPorts(6379)
            .withStartupTimeout(Duration.ofMinutes(2))
            .withReuse(true);

    @Container
    protected static KafkaContainer kafka = new KafkaContainer(DockerImageName.parse("confluentinc/cp-kafka:7.4.0"))
            .withStartupTimeout(Duration.ofMinutes(2))
            .withReuse(true);

    /**
     * Validates Docker environment before running tests.
     * Skips tests if Docker is not available.
     * Modified to allow embedded Docker client strategy.
     */
    @BeforeAll
    static void checkDockerEnvironment() {
        try {
            // For embedded Docker client strategy, skip availability check
            String clientStrategy = System.getProperty("testcontainers.docker.client.strategy");
            if ("org.testcontainers.dockerclient.EmbeddedDockerClientProviderStrategy".equals(clientStrategy)) {
                logger.info("Using embedded Docker client strategy, skipping Docker availability check.");
                return;
            }
            boolean dockerAvailable = DockerClientFactory.instance().isDockerAvailable();
            if (!dockerAvailable) {
                logger.warn("Docker is not available. Skipping integration tests.");
                logger.warn("To run integration tests, please:");
                logger.warn("1. Install Docker Desktop");
                logger.warn("2. Start Docker Desktop");
                logger.warn("3. Ensure Docker daemon is running");
            }
            assumeTrue(dockerAvailable, "Docker environment is not available");
            logger.info("Docker environment validated successfully");
        } catch (Exception e) {
            logger.error("Failed to validate Docker environment: {}", e.getMessage());
            assumeTrue(false, "Docker environment validation failed: " + e.getMessage());
        }
    }

    /**
     * Configures Spring properties with TestContainer connection details.
     */
    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        // PostgreSQL configuration
        registry.add("spring.datasource.url", postgres::getJdbcUrl);
        registry.add("spring.datasource.username", postgres::getUsername);
        registry.add("spring.datasource.password", postgres::getPassword);
        registry.add("spring.datasource.driver-class-name", () -> "org.postgresql.Driver");

        // Redis configuration
        registry.add("spring.data.redis.host", redis::getHost);
        registry.add("spring.data.redis.port", redis::getFirstMappedPort);

        // Kafka configuration
        registry.add("spring.kafka.bootstrap-servers", kafka::getBootstrapServers);
        
        logger.info("TestContainers configuration applied:");
        logger.info("PostgreSQL: {}", postgres.getJdbcUrl());
        logger.info("Redis: {}:{}", redis.getHost(), redis.getFirstMappedPort());
        logger.info("Kafka: {}", kafka.getBootstrapServers());
    }

    /**
     * Checks if Docker is available for tests.
     * @return true if Docker is available, false otherwise
     */
    protected static boolean isDockerAvailable() {
        try {
            return DockerClientFactory.instance().isDockerAvailable();
        } catch (Exception e) {
            logger.debug("Docker availability check failed: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Gets the PostgreSQL container for test-specific operations.
     */
    protected static PostgreSQLContainer<?> getPostgresContainer() {
        return postgres;
    }

    /**
     * Gets the Redis container for test-specific operations.
     */
    protected static GenericContainer<?> getRedisContainer() {
        return redis;
    }

    /**
     * Gets the Kafka container for test-specific operations.
     */
    protected static KafkaContainer getKafkaContainer() {
        return kafka;
    }
}
