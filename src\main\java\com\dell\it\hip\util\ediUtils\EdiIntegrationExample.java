package com.dell.it.hip.util.ediUtils;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;

public class EdiIntegrationExample {
   
		
	public void edioperation () {
        // Configure Redisson
        Config config = new Config();
        config.useSingleServer().setAddress("redis://127.0.0.1:6379");
        RedissonClient redisson = Redisson.create(config);

        // Create manager with default settings
        RangeAwareEdiControlManager ediManager = new RangeAwareEdiControlManager(
            redisson,
            "edi:control",  // Redis key prefix
            9,             // Default ID length (9 digits)
            30,            // Lock wait time (seconds)
            60             // Lock lease time (seconds)
        );

        // Register transaction types with custom ranges
        ediManager.registerTransactionType("850", 1000, 999999, 90); // PO: 1000-999999 (reset at 90%)
        ediManager.registerTransactionType("855", 1, *********, 95);  // PO Ack: 1-********* (reset at 95%)
        ediManager.registerTransactionType("810", 5000, 99999, 80);    // Invoice: 5000-99999 (reset at 80%)

        // Generate control IDs
        try {
            // Generate PO Acknowledgement (855)
            RangeAwareEdiControlManager.EdiControlRecord ackRecord = 
                ediManager.generateControlId("855", "COMPANYA", "PARTNERB");
            System.out.println("Generated 855 Control ID: " + ackRecord.getControlNumber());

            // Generate Purchase Order (850)
            RangeAwareEdiControlManager.EdiControlRecord poRecord = 
                ediManager.generateControlId("850", "COMPANYA", "PARTNERB");
            System.out.println("Generated 850 Control ID: " + poRecord.getControlNumber());

            // Generate Invoice (810)
            RangeAwareEdiControlManager.EdiControlRecord invRecord = 
                ediManager.generateControlId("810", "COMPANYA", "PARTNERB");
            System.out.println("Generated 810 Control ID: " + invRecord.getControlNumber());

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            System.err.println("Thread interrupted during control ID generation");
        } finally {
            redisson.shutdown();
        }
    }
	
	public void example() {
		Config config = new Config();
        config.useSingleServer().setAddress("redis://127.0.0.1:6379");
        RedissonClient redisson = Redisson.create(config);
        
        try {
            // 2. Create EDI generator
            EdiDocumentGenerator generator = new EdiDocumentGenerator(redisson, "edi:control");
            
            // 3. Generate 855 document
            String edi855 = generator.generate855Document(
                "SENDER01", 
                "RECEIVER01", 
                "ZZ",       // ISA qualifier
                "PR",       // GS functional code (PR for 855)
                "PO123456"  // Purchase Order Number
            );
            
            // 4. Output the EDI document
            System.out.println(edi855);
            
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            redisson.shutdown();
        }
	}
}