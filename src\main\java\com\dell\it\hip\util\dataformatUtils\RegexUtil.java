package com.dell.it.hip.util.dataformatUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
public class RegexUtil {
    /**
     * Extracts the first match (or group) for the pattern from the input.
     * If the pattern contains groups, returns the first group's value.
     * If not, returns the full match.
     */
    public static String extract(String input, String pattern) {
        if (input == null || pattern == null) return null;
        Pattern p = Pattern.compile(pattern);
        Matcher m = p.matcher(input);
        if (m.find()) {
            if (m.groupCount() >= 1) {
                return m.group(1);
            } else {
                return m.group();
            }
        }
        return null;
    }
}