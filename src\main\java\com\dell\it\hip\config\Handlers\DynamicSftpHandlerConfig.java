package com.dell.it.hip.config.Handlers;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class DynamicSftpHandlerConfig extends HandlerConfig {

	@JsonProperty("sftp.producer.host")
    private String host;

	@JsonProperty("sftp.producer.port")
    private int port;              // Default SFTP port

	@JsonProperty("sftp.producer.username")
	private String username;

	@JsonProperty("sftp.producer.password")
    private String password;

    @JsonProperty("sftp.producer.private.key.path")
    private String privateKeyPath;      // Optional: path to private key for key-based auth

    @JsonProperty("sftp.producer.remote.directory")
    private String remoteDirectory;     // Directory where files will be uploaded

    @JsonProperty("sftp.producer.timeout")
    private Integer timeout;            // Connection/auth timeout in milliseconds

    @JsonProperty("sftp.producer.gzip.enabled")
    private Boolean gzipEnabled;
}