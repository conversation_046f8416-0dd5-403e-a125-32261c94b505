package com.dell.it.hip.controller;

import com.dell.it.hip.config.HIPIntegrationRequest;
import com.dell.it.hip.config.Tag;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit test for HIPIntegrationRequest JSON deserialization to verify
 * that the new tags format works correctly with ObjectMapper (which is used by the REST API).
 */
public class HIPIntegrationRequestControllerTest {

    private ObjectMapper objectMapper;

    @BeforeEach
    public void setUp() {
        objectMapper = new ObjectMapper();
    }

    @Test
    public void testHIPIntegrationRequestDeserializationInController() throws Exception {
        // Create a JSON request with the new tags format
        String jsonRequest = """
            {
              "hipIntegrationName": "controller-test-integration",
              "version": "1.0",
              "hipIntegrationType": "INBOUND",
              "businessFlowType": "PAYMENT",
              "owner": "test-team",
              "businessFlowName": "controller-test-flow",
              "businessFlowVersion": "1.0",
              "tags": [
                {"key": "environment", "value": "test"},
                {"key": "team", "value": "integration-test"}
              ],
              "adapters": [
                {
                  "type": "kafkaAdapter",
                  "propertyRef": "test-kafka-adapter"
                }
              ],
              "flowSteps": [
                {
                  "type": "validation",
                  "propertyRef": "test-validation-step"
                }
              ],
              "handlers": [
                {
                  "type": "kafkaHandler",
                  "propertyRef": "test-kafka-handler",
                  "role": "primary"
                }
              ],
              "propertySheets": [
                "test-kafka-adapter",
                "test-validation-step",
                "test-kafka-handler"
              ],
              "throttleSettings": {
                "enabled": false,
                "maxMessagesPerPeriod": 100,
                "periodSeconds": 60
              }
            }
            """;

        // Test that the JSON can be deserialized correctly (same as what the controller would do)
        // Note: This test focuses on deserialization using ObjectMapper
        HIPIntegrationRequest request = objectMapper.readValue(jsonRequest, HIPIntegrationRequest.class);

        // Verify the request was deserialized correctly
        assertNotNull(request);
        assertEquals("controller-test-integration", request.getHipIntegrationName());
        assertEquals("1.0", request.getVersion());
        assertEquals("INBOUND", request.getHipIntegrationType());
        assertEquals("PAYMENT", request.getBusinessFlowType());
        assertEquals("test-team", request.getOwner());

        // Verify tags deserialization
        List<Tag> tags = request.getTags();
        assertNotNull(tags);
        assertEquals(2, tags.size());

        Tag envTag = tags.get(0);
        assertEquals("environment", envTag.getKey());
        assertEquals("test", envTag.getValue());

        Tag teamTag = tags.get(1);
        assertEquals("team", teamTag.getKey());
        assertEquals("integration-test", teamTag.getValue());

        // Verify other collections
        assertNotNull(request.getAdapters());
        assertEquals(1, request.getAdapters().size());
        assertEquals("kafkaAdapter", request.getAdapters().get(0).getType());

        assertNotNull(request.getFlowSteps());
        assertEquals(1, request.getFlowSteps().size());
        assertEquals("validation", request.getFlowSteps().get(0).getType());

        assertNotNull(request.getHandlers());
        assertEquals(1, request.getHandlers().size());
        assertEquals("kafkaHandler", request.getHandlers().get(0).getType());
        assertEquals("primary", request.getHandlers().get(0).getRole());

        assertNotNull(request.getPropertySheets());
        assertEquals(3, request.getPropertySheets().size());

        assertNotNull(request.getThrottleSettings());
        assertFalse(request.getThrottleSettings().isEnabled());
        assertEquals(100, request.getThrottleSettings().getMaxMessagesPerPeriod());
        assertEquals(60, request.getThrottleSettings().getPeriodSeconds());
    }

    @Test
    public void testBackwardCompatibilityWithLegacyTagsFormat() throws Exception {
        // Test with legacy string array format
        String legacyJsonRequest = """
            {
              "hipIntegrationName": "legacy-test-integration",
              "version": "1.0",
              "tags": ["legacy-tag1", "legacy-tag2"],
              "adapters": [],
              "flowSteps": [],
              "handlers": [],
              "propertySheets": []
            }
            """;

        HIPIntegrationRequest request = objectMapper.readValue(legacyJsonRequest, HIPIntegrationRequest.class);

        assertNotNull(request);
        assertEquals("legacy-test-integration", request.getHipIntegrationName());

        List<Tag> tags = request.getTags();
        assertNotNull(tags);
        assertEquals(2, tags.size());

        // Verify legacy tags are converted correctly
        assertEquals("legacy", tags.get(0).getKey());
        assertEquals("legacy-tag1", tags.get(0).getValue());

        assertEquals("legacy", tags.get(1).getKey());
        assertEquals("legacy-tag2", tags.get(1).getValue());
    }

    @Test
    public void testSerializationRoundTripThroughController() throws Exception {
        // Create a request with new tag format
        HIPIntegrationRequest originalRequest = new HIPIntegrationRequest();
        originalRequest.setHipIntegrationName("roundtrip-test");
        originalRequest.setVersion("1.0");
        originalRequest.setTags(List.of(
            new Tag("environment", "production"),
            new Tag("team", "payments")
        ));

        // Serialize to JSON
        String json = objectMapper.writeValueAsString(originalRequest);

        // Deserialize back (simulating what the controller would do)
        HIPIntegrationRequest deserializedRequest = objectMapper.readValue(json, HIPIntegrationRequest.class);

        // Verify round trip
        assertNotNull(deserializedRequest);
        assertEquals(originalRequest.getHipIntegrationName(), deserializedRequest.getHipIntegrationName());
        assertEquals(originalRequest.getVersion(), deserializedRequest.getVersion());

        List<Tag> originalTags = originalRequest.getTags();
        List<Tag> deserializedTags = deserializedRequest.getTags();

        assertNotNull(deserializedTags);
        assertEquals(originalTags.size(), deserializedTags.size());

        for (int i = 0; i < originalTags.size(); i++) {
            assertEquals(originalTags.get(i).getKey(), deserializedTags.get(i).getKey());
            assertEquals(originalTags.get(i).getValue(), deserializedTags.get(i).getValue());
        }
    }

    @Test
    public void testMixedTagFormatsHandling() throws Exception {
        // This test verifies that our deserializer can handle edge cases
        // where different tag formats might be mixed (though this shouldn't happen in practice)
        
        String mixedJson = """
            {
              "hipIntegrationName": "mixed-test",
              "version": "1.0",
              "tags": [
                {"key": "explicit-key", "value": "explicit-value"},
                "legacy-string-tag"
              ]
            }
            """;

        HIPIntegrationRequest request = objectMapper.readValue(mixedJson, HIPIntegrationRequest.class);

        assertNotNull(request);
        List<Tag> tags = request.getTags();
        assertNotNull(tags);
        assertEquals(2, tags.size());

        // First tag should be the explicit key-value pair
        assertEquals("explicit-key", tags.get(0).getKey());
        assertEquals("explicit-value", tags.get(0).getValue());

        // Second tag should be converted to legacy format
        assertEquals("legacy", tags.get(1).getKey());
        assertEquals("legacy-string-tag", tags.get(1).getValue());
    }
}
