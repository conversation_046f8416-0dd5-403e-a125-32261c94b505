{"hipIntegrationName": "InvoiceRoutingIntegration", "version": "1.0", "serviceManagerName": "svcMgr1", "flowStepConfigRefs": [{"propertyRef": "routingStep", "type": "FlowRoutingFlowStep"}], "flowStepConfigs": [{"propertyRef": "routingStep", "type": "FlowRoutingFlowStep", "stepName": "InvoicePaymentRouter", "documentType": "invoice", "flowIdentifier": "payment", "rules": [{"ruleName": "RouteToPaymentsAndAudit", "ruleVersion": "1.0"}]}], "handlerConfigRefs": [{"propertyRef": "<PERSON><PERSON><PERSON><PERSON>", "type": "KafkaOutputHandler", "role": "primary"}], "handlerConfigs": [{"propertyRef": "<PERSON><PERSON><PERSON><PERSON>", "type": "KafkaOutputHandler", "topic": "processed-invoices"}], "adapterConfigRefs": [{"propertyRef": "invoiceKafkaInput", "type": "KafkaInputAdapter"}], "adapterConfigs": [{"propertyRef": "invoiceKafkaInput", "type": "KafkaInputAdapter", "topic": "incoming-invoices"}]}