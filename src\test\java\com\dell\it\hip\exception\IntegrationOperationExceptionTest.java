package com.dell.it.hip.exception;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class IntegrationOperationExceptionTest {

    @Test
    void testBasicConstructor() {
        String message = "Operation failed";
        IntegrationOperationException exception = new IntegrationOperationException(message);
        
        assertEquals(message, exception.getMessage());
        assertNull(exception.getIntegrationName());
        assertNull(exception.getVersion());
        assertNull(exception.getOperation());
    }

    @Test
    void testConstructorWithCause() {
        String message = "Operation failed";
        RuntimeException cause = new RuntimeException("Root cause");
        IntegrationOperationException exception = new IntegrationOperationException(message, cause);
        
        assertEquals(message, exception.getMessage());
        assertEquals(cause, exception.getCause());
        assertNull(exception.getIntegrationName());
        assertNull(exception.getVersion());
        assertNull(exception.getOperation());
    }

    @Test
    void testConstructorWithOperationDetails() {
        String operation = "unregister";
        String integrationName = "test-integration";
        String version = "1.0";
        String message = "Configuration error";
        
        IntegrationOperationException exception = new IntegrationOperationException(
            operation, integrationName, version, message);
        
        assertTrue(exception.getMessage().contains(operation));
        assertTrue(exception.getMessage().contains(integrationName));
        assertTrue(exception.getMessage().contains(version));
        assertTrue(exception.getMessage().contains(message));
        assertEquals(operation, exception.getOperation());
        assertEquals(integrationName, exception.getIntegrationName());
        assertEquals(version, exception.getVersion());
    }

    @Test
    void testConstructorWithOperationDetailsAndCause() {
        String operation = "pause";
        String integrationName = "test-integration";
        String version = "1.0";
        String message = "Configuration error";
        RuntimeException cause = new RuntimeException("Root cause");
        
        IntegrationOperationException exception = new IntegrationOperationException(
            operation, integrationName, version, message, cause);
        
        assertTrue(exception.getMessage().contains(operation));
        assertTrue(exception.getMessage().contains(integrationName));
        assertTrue(exception.getMessage().contains(version));
        assertTrue(exception.getMessage().contains(message));
        assertEquals(operation, exception.getOperation());
        assertEquals(integrationName, exception.getIntegrationName());
        assertEquals(version, exception.getVersion());
        assertEquals(cause, exception.getCause());
    }

    @Test
    void testConstructorWithCauseOnly() {
        String operation = "resume";
        String integrationName = "test-integration";
        String version = "1.0";
        RuntimeException cause = new RuntimeException("Root cause");
        
        IntegrationOperationException exception = new IntegrationOperationException(
            operation, integrationName, version, cause);
        
        assertTrue(exception.getMessage().contains(operation));
        assertTrue(exception.getMessage().contains(integrationName));
        assertTrue(exception.getMessage().contains(version));
        assertTrue(exception.getMessage().contains("Root cause"));
        assertEquals(operation, exception.getOperation());
        assertEquals(integrationName, exception.getIntegrationName());
        assertEquals(version, exception.getVersion());
        assertEquals(cause, exception.getCause());
    }

    @Test
    void testDifferentOperationTypes() {
        String[] operations = {"register", "unregister", "pause", "resume", "shutdown"};
        String integrationName = "test-integration";
        String version = "1.0";
        
        for (String operation : operations) {
            IntegrationOperationException exception = new IntegrationOperationException(
                operation, integrationName, version, "Test message");
            
            assertEquals(operation, exception.getOperation());
            assertTrue(exception.getMessage().contains(operation));
        }
    }
}
