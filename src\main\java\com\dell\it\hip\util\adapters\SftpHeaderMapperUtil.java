package com.dell.it.hip.util.adapters;

import java.util.HashMap;
import java.util.Map;

import org.springframework.integration.file.remote.FileInfo;
import org.springframework.messaging.MessageHeaders;
/**
        * Utility to extract and map SFTP file headers into Spring Integration message headers.
 */
public class SftpHeaderMapperUtil {

    // Standard header names for SFTP files
    public static final String SFTP_FILENAME = "sftp_filename";
    public static final String SFTP_PATH = "sftp_path";
    public static final String SFTP_SIZE = "sftp_size";
    public static final String SFTP_MODIFIED = "sftp_modified";
    public static final String SFTP_PERMISSIONS = "sftp_permissions";
    public static final String SFTP_OWNER = "sftp_owner";
    public static final String SFTP_GROUP = "sftp_group";
    public static final String SFTP_RAW_ATTRIBUTES = "sftp_raw_attributes";

    /**
     * Extracts SFTP-specific file metadata as message headers.
     *
     * @param fileInfo the FileInfo representing a remote SFTP file
     * @return a map of message headers with SFTP metadata
     */
    public static Map<String, Object> extract(FileInfo<?> fileInfo) {
        Map<String, Object> headers = new HashMap<>();
        if (fileInfo == null) return headers;

        headers.put(SFTP_FILENAME, fileInfo.getFilename());
        headers.put(SFTP_PATH, fileInfo.getRemoteDirectory());
        headers.put(SFTP_SIZE, fileInfo.getSize());
        headers.put(SFTP_MODIFIED, fileInfo.getModified());
        headers.put(SFTP_PERMISSIONS, fileInfo.getPermissions());

        headers.put(SFTP_RAW_ATTRIBUTES, fileInfo.getFileInfo());

        // Add additional standard headers if needed, e.g. custom attributes
        // headers.put("custom_header", ...);

        return headers;
    }

    /**
     * Copies SFTP headers from FileInfo into the message headers map.
     * This can be merged with other existing headers as needed.
     *
     * @param fileInfo the FileInfo object representing the file
     * @param baseHeaders an existing headers map (may be null)
     * @return merged map with SFTP headers included
     */
    public static Map<String, Object> mergeWithFileHeaders(FileInfo<?> fileInfo, Map<String, Object> baseHeaders) {
        Map<String, Object> headers = baseHeaders != null ? new HashMap<>(baseHeaders) : new HashMap<>();
        headers.putAll(extract(fileInfo));
        return headers;
    }

    /**
     * Checks for SFTP headers in the message headers and returns them as a map.
     * This can be useful when extracting SFTP-specific information from processed messages.
     *
     * @param headers the message headers
     * @return map with SFTP-specific headers, or empty if not present
     */
    public static Map<String, Object> extractFromMessageHeaders(MessageHeaders headers) {
        Map<String, Object> sftpHeaders = new HashMap<>();
        if (headers == null) return sftpHeaders;

        for (String key : headers.keySet()) {
            if (key.startsWith("sftp_")) {
                sftpHeaders.put(key, headers.get(key));
            }
        }
        return sftpHeaders;
    }
}