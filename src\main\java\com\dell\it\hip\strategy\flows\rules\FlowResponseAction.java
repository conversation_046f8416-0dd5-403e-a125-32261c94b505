package com.dell.it.hip.strategy.flows.rules;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.rules.Rule;

@Component
public class FlowResponseAction implements RuleAction {
    private static final Logger logger = LoggerFactory.getLogger(FlowResponseAction.class);

    @Override
    public String getName() { return "flowResponse"; }

    @Override
    public String getType() { return "FlowResponse"; }


    @Override
    public Message<?> performAction(Message<?> message, Map<String, Object> params, Rule rule, HIPIntegrationDefinition def, Map<String, Object> context) {
        String channel = params != null ? (String) params.get("targetChannels").toString() : null;
        if (channel == null) {
            logger.warn("FlowResponseAction: No channel specified in rule '{}'", rule != null ? rule.getRuleName() : null);
        } else {
            logger.info("FlowResponseAction: Adding channel '{}' from rule '{}'", channel, rule != null ? rule.getRuleName() : null);
            getOrCreateChannelList(context).add(channel);
        }
        return message;
    }

    private List<String> getOrCreateChannelList(Map<String, Object> context) {
        @SuppressWarnings("unchecked")
        List<String> channels = (List<String>) context.get("targetFlowChannels");
        if (channels == null) {
            channels = new ArrayList<>();
            context.put("targetFlowChannels", channels);
        }
        return channels;
    }




}