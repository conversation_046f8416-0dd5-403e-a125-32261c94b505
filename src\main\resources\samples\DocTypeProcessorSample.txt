{
  "name": "INVOICE",
  "version": "1.0",
  "docTypeIdentifiers": [
    {
      "attributeName": "invoiceType",
      "derivedFrom": "JSONPATH",
      "expression": "type",
      "expectedValue": "INVOICE"
    }
  ],
  "docTypeRuleOperator": "ALL",
  "validation": {
    "structural": true,
    "schema": true,
    "schemaKey": "INVOICE_JSON_SCHEMA"
  },
  "attributeMappings": {
    "invoiceNumber": {
      "attributeName": "invoiceNumber",
      "derivedFrom": "JSONPATH",
      "expression": "invoiceNumber"
    },
    "invoiceDate": {
      "attributeName": "invoiceDate",
      "derivedFrom": "JSONPATH",
      "expression": "date"
    }
  }
}

{
  "name": "PAYMENT",
  "version": "2.1",
  "docTypeIdentifiers": [
    {
      "attributeName": "paymentType",
      "derivedFrom": "JSONPATH",
      "expression": "type",
      "expectedValue": "PAYMENT"
    }
  ],
  "docTypeRuleOperator": "ALL",
  "validation": {
    "structural": true,
    "schema": false
  },
  "attributeMappings": {
    "paymentId": {
      "attributeName": "paymentId",
      "derivedFrom": "JSONPATH",
      "expression": "id"
    },
    "paymentDate": {
      "attributeName": "paymentDate",
      "derivedFrom": "JSONPATH",
      "expression": "date"
    }
  }
}

----------
propertysheet
{
  "propertyRef": "docTypeStep1",
  "type": "docTypeProcessor",
  "supportedDocTypesPerFormat": {
    "JSON": ["INVOICE:1.0", "PAYMENT:2.1"]
  },
  "genericDocType": "GENERIC",
  "allowGenericDocType": true,
  "terminateOnUnknownFormat": true,
  "attributeMappings": {
    "sourceSystem": {
      "attributeName": "sourceSystem",
      "derivedFrom": "JSONPATH",
      "expression": "source"
    }
  }
}
propertyRef: docTypeStep1
type: docTypeProcessor
supportedDocTypesPerFormat:
  JSON:
    - INVOICE:1.0
    - PAYMENT:2.1
genericDocType: GENERIC
allowGenericDocType: true
terminateOnUnknownFormat: true
attributeMappings:
  sourceSystem:
    attributeName: sourceSystem
    derivedFrom: JSONPATH
    expression: source

  ---------
  {
    "propertyRef": "docTypeStep1",
    "type": "docTypeProcessor",
    "supportedDocTypesPerFormat": {
      "JSON": ["INVOICE:1.0", "PAYMENT:2.1"]
    },
    "genericDocType": "GENERIC_JSON",
    "allowGenericDocType": true,
    "terminateOnUnknownFormat": true,
    "attributeMappings": {
      "sourceSystem": {
        "attributeName": "sourceSystem",
        "derivedFrom": "JSONPATH",
        "expression": "source"
      },
      "recordType": {
        "attributeName": "recordType",
        "derivedFrom": "JSONPATH",
        "expression": "type"
      }
    },
    "validation": {
      "structural": true,
      "schema": false
    }
  }