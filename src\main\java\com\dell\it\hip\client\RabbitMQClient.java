package com.dell.it.hip.client;

import com.rabbitmq.client.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AcknowledgeMode;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * RabbitMQClient utility class for testing RabbitMQ message send and receive operations.
 * This class mirrors the implementation patterns used in DynamicRabbitMQInputAdapter 
 * and DynamicRabbitMQOutputHandler for compatibility with HIP services framework.
 * 
 * Usage:
 * 1. Create RabbitMQClient with consumer and producer configurations
 * 2. Use sendMessage() to test handler functionality
 * 3. Use receiveMessage() to test adapter functionality
 * 4. Use startListener() for continuous message consumption
 */
public class RabbitMQClient implements AutoCloseable {
    
    private static final Logger logger = LoggerFactory.getLogger(RabbitMQClient.class);
    
    // Configuration for consumer (adapter testing)
    private final RabbitMQClientConfig consumerConfig;
    
    // Configuration for producer (handler testing)  
    private final RabbitMQClientConfig producerConfig;
    
    // Connection management
    private Connection producerConnection;
    private Channel producerChannel;
    private Connection consumerConnection;
    private Channel consumerChannel;
    private ExecutorService listenerExecutor;
    private final AtomicBoolean listenerRunning = new AtomicBoolean(false);
    private String consumerTag;
    
    public RabbitMQClient(RabbitMQClientConfig consumerConfig, RabbitMQClientConfig producerConfig) {
        this.consumerConfig = consumerConfig;
        this.producerConfig = producerConfig;
    }
    
    /**
     * Send a message to RabbitMQ using the same approach as DynamicRabbitMQOutputHandler
     */
    public void sendMessage(String message) throws Exception {
        sendMessage(message.getBytes(StandardCharsets.UTF_8));
    }
    
    /**
     * Send a byte array message to RabbitMQ using the same approach as DynamicRabbitMQOutputHandler
     */
    public void sendMessage(byte[] payload) throws Exception {
        sendMessage(payload, new HashMap<>());
    }
    
    /**
     * Send a message with custom headers
     */
    public void sendMessage(byte[] payload, Map<String, Object> headers) throws Exception {
        logger.info("Sending message to RabbitMQ exchange: {}, routingKey: {}", 
                   producerConfig.getExchange(), producerConfig.getRoutingKey());
        
        Channel channel = getOrCreateProducerChannel();
        
        // Build properties - mirrors DynamicRabbitMQOutputHandler logic
        AMQP.BasicProperties.Builder propsBuilder = new AMQP.BasicProperties.Builder();
        
        if (Boolean.TRUE.equals(producerConfig.getPersistent())) {
            propsBuilder.deliveryMode(2); // persistent
        } else {
            propsBuilder.deliveryMode(1); // non-persistent
        }
        
        // Add headers
        if (!headers.isEmpty()) {
            propsBuilder.headers(headers);
        }
        
        AMQP.BasicProperties props = propsBuilder.build();
        
        try {
            channel.basicPublish(
                producerConfig.getExchange(),
                producerConfig.getRoutingKey(),
                Boolean.TRUE.equals(producerConfig.getMandatory()),
                props,
                payload
            );
            logger.info("Message sent successfully to exchange: {}, routingKey: {}", 
                       producerConfig.getExchange(), producerConfig.getRoutingKey());
        } catch (IOException ex) {
            logger.error("RabbitMQ send failed: {}", ex.getMessage(), ex);
            throw ex;
        }
    }
    
    /**
     * Receive a single message from RabbitMQ using the same approach as DynamicRabbitMQInputAdapter
     */
    public RabbitMQMessage receiveMessage(long timeoutMs) throws Exception {
        logger.info("Receiving message from RabbitMQ queue: {}", consumerConfig.getQueueName());
        
        Channel channel = getOrCreateConsumerChannel();
        
        try {
            GetResponse response = channel.basicGet(consumerConfig.getQueueName(), false);
            
            if (response == null) {
                logger.info("No message received within timeout: {}ms", timeoutMs);
                return null;
            }
            
            // Extract headers
            Map<String, Object> headers = new HashMap<>();
            if (response.getProps().getHeaders() != null) {
                headers.putAll(response.getProps().getHeaders());
            }
            
            String messageContent = new String(response.getBody(), StandardCharsets.UTF_8);
            
            RabbitMQMessage rabbitMessage = new RabbitMQMessage(
                messageContent,
                consumerConfig.getQueueName(),
                response.getEnvelope().getExchange(),
                response.getEnvelope().getRoutingKey(),
                response.getEnvelope().getDeliveryTag(),
                headers
            );
            
            // Acknowledge the message
            channel.basicAck(response.getEnvelope().getDeliveryTag(), false);
            
            logger.info("Message received successfully from queue: {}", consumerConfig.getQueueName());
            return rabbitMessage;
            
        } catch (Exception ex) {
            logger.error("RabbitMQ receive failed: {}", ex.getMessage(), ex);
            throw ex;
        }
    }
    
    /**
     * Start a continuous message listener using the same approach as DynamicRabbitMQInputAdapter
     */
    public void startListener(MessageHandler messageHandler) throws Exception {
        if (listenerRunning.get()) {
            logger.warn("Listener is already running");
            return;
        }
        
        logger.info("Starting RabbitMQ listener for queue: {}", consumerConfig.getQueueName());
        
        Channel channel = getOrCreateConsumerChannel();
        
        // Set prefetch count if configured
        if (consumerConfig.getPrefetchCount() != null) {
            channel.basicQos(consumerConfig.getPrefetchCount());
        }
        
        listenerRunning.set(true);
        
        DefaultConsumer consumer = new DefaultConsumer(channel) {
            @Override
            public void handleDelivery(String consumerTag, Envelope envelope,
                                     AMQP.BasicProperties properties, byte[] body) throws IOException {
                try {
                    // Extract headers
                    Map<String, Object> headers = new HashMap<>();
                    if (properties.getHeaders() != null) {
                        headers.putAll(properties.getHeaders());
                    }
                    
                    String messageContent = new String(body, StandardCharsets.UTF_8);
                    
                    RabbitMQMessage rabbitMessage = new RabbitMQMessage(
                        messageContent,
                        consumerConfig.getQueueName(),
                        envelope.getExchange(),
                        envelope.getRoutingKey(),
                        envelope.getDeliveryTag(),
                        headers
                    );
                    
                    try {
                        messageHandler.handleMessage(rabbitMessage);
                        
                        // Acknowledge the message based on acknowledge mode
                        if (consumerConfig.getAcknowledgeMode() == null || 
                            consumerConfig.getAcknowledgeMode() == AcknowledgeMode.AUTO) {
                            channel.basicAck(envelope.getDeliveryTag(), false);
                        }
                    } catch (Exception ex) {
                        logger.error("Error handling message: {}", ex.getMessage(), ex);
                        // Reject and requeue the message on error
                        channel.basicNack(envelope.getDeliveryTag(), false, true);
                    }
                } catch (Exception ex) {
                    logger.error("Error in message listener: {}", ex.getMessage(), ex);
                }
            }
        };
        
        consumerTag = channel.basicConsume(consumerConfig.getQueueName(), false, consumer);
        logger.info("RabbitMQ listener started for queue: {}", consumerConfig.getQueueName());
    }
    
    /**
     * Stop the message listener
     */
    public void stopListener() {
        if (!listenerRunning.get()) {
            return;
        }
        
        listenerRunning.set(false);
        
        try {
            if (consumerChannel != null && consumerTag != null) {
                consumerChannel.basicCancel(consumerTag);
                consumerTag = null;
            }
        } catch (Exception e) {
            logger.warn("Error stopping consumer: {}", e.getMessage());
        }
        
        logger.info("RabbitMQ listener stopped");
    }
    
    /**
     * Wait for a specific number of messages with timeout
     */
    public List<RabbitMQMessage> waitForMessages(int expectedCount, long timeoutMs, MessageHandler messageHandler) throws Exception {
        CountDownLatch latch = new CountDownLatch(expectedCount);
        List<RabbitMQMessage> receivedMessages = Collections.synchronizedList(new ArrayList<>());
        
        startListener(message -> {
            receivedMessages.add(message);
            messageHandler.handleMessage(message);
            latch.countDown();
        });
        
        boolean received = latch.await(timeoutMs, TimeUnit.MILLISECONDS);
        stopListener();
        
        if (!received) {
            throw new RuntimeException("Did not receive expected " + expectedCount + " messages within " + timeoutMs + "ms");
        }
        
        logger.info("Successfully received {} messages", expectedCount);
        return receivedMessages;
    }
    
    /**
     * Close all connections and clean up resources
     */
    @Override
    public void close() {
        stopListener();
        
        try {
            if (producerChannel != null && producerChannel.isOpen()) {
                producerChannel.close();
            }
        } catch (Exception ignored) {}
        
        try {
            if (consumerChannel != null && consumerChannel.isOpen()) {
                consumerChannel.close();
            }
        } catch (Exception ignored) {}
        
        try {
            if (producerConnection != null && producerConnection.isOpen()) {
                producerConnection.close();
            }
        } catch (Exception ignored) {}
        
        try {
            if (consumerConnection != null && consumerConnection.isOpen()) {
                consumerConnection.close();
            }
        } catch (Exception ignored) {}
        
        logger.info("RabbitMQClient closed");
    }
    
    // Private helper methods
    
    private Channel getOrCreateProducerChannel() throws Exception {
        if (producerChannel == null || !producerChannel.isOpen()) {
            if (producerConnection == null || !producerConnection.isOpen()) {
                producerConnection = createConnection(producerConfig);
            }
            producerChannel = producerConnection.createChannel();
        }
        return producerChannel;
    }
    
    private Channel getOrCreateConsumerChannel() throws Exception {
        if (consumerChannel == null || !consumerChannel.isOpen()) {
            if (consumerConnection == null || !consumerConnection.isOpen()) {
                consumerConnection = createConnection(consumerConfig);
            }
            consumerChannel = consumerConnection.createChannel();
        }
        return consumerChannel;
    }

    private Connection createConnection(RabbitMQClientConfig config) throws Exception {
        ConnectionFactory factory = new ConnectionFactory();

        // Basic connection settings - mirrors DynamicRabbitMQOutputHandler logic
        factory.setHost(config.getHost());
        if (config.getPort() != null) {
            factory.setPort(config.getPort());
        }
        if (config.getUsername() != null) {
            factory.setUsername(config.getUsername());
        }
        if (config.getPassword() != null) {
            factory.setPassword(config.getPassword());
        }
        if (config.getVirtualHost() != null) {
            factory.setVirtualHost(config.getVirtualHost());
        }

        // SSL/TLS configuration - mirrors DynamicRabbitMQInputAdapter logic
        String authType = config.getAuthenticationType();
        if ("TLS".equalsIgnoreCase(authType) || "CERT".equalsIgnoreCase(authType)) {
            try {
                factory.useSslProtocol();
            } catch (Exception e) {
                logger.error("Failed to enable TLS on RabbitMQ connection: {}", e.getMessage(), e);
                throw new IllegalStateException("RabbitMQ SSL setup failed", e);
            }
        }

        // Additional SSL parameters if present
        if (config.getParameters() != null) {
            Object ssl = config.getParameters().get("sslEnabled");
            if (Boolean.TRUE.equals(ssl)) {
                try {
                    factory.useSslProtocol();
                } catch (Exception ignored) {}
            }
        }

        return factory.newConnection();
    }

    /**
     * Functional interface for handling received messages
     */
    @FunctionalInterface
    public interface MessageHandler {
        void handleMessage(RabbitMQMessage message);
    }

    /**
     * Represents a RabbitMQ message with metadata
     */
    public static class RabbitMQMessage {
        private final String content;
        private final String queue;
        private final String exchange;
        private final String routingKey;
        private final long deliveryTag;
        private final Map<String, Object> headers;

        public RabbitMQMessage(String content, String queue, String exchange, String routingKey,
                              long deliveryTag, Map<String, Object> headers) {
            this.content = content;
            this.queue = queue;
            this.exchange = exchange;
            this.routingKey = routingKey;
            this.deliveryTag = deliveryTag;
            this.headers = headers != null ? new HashMap<>(headers) : new HashMap<>();
        }

        public String getContent() { return content; }
        public String getQueue() { return queue; }
        public String getExchange() { return exchange; }
        public String getRoutingKey() { return routingKey; }
        public long getDeliveryTag() { return deliveryTag; }
        public Map<String, Object> getHeaders() { return new HashMap<>(headers); }

        @Override
        public String toString() {
            return String.format("RabbitMQMessage{queue='%s', exchange='%s', routingKey='%s', deliveryTag=%d, content='%s', headers=%s}",
                               queue, exchange, routingKey, deliveryTag, content, headers);
        }
    }
}
