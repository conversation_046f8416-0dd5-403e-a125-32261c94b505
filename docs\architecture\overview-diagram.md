# HIP Services System Overview

This high-level overview diagram provides a comprehensive view of the HIP Services architecture, emphasizing the refactored configuration management system and the consistent JSON property naming conventions across all components.

```mermaid
graph TB
    %% Legend
    subgraph "Legend"
        L1[🔧 Configuration Layer]
        L2[📥 Input Adapters]
        L3[⚙️ Processing Flow]
        L4[📤 Output Handlers]
        L5[🌐 External Systems]
        L6[📊 Monitoring]
    end

    %% Configuration Management Core
    subgraph "🔧 Configuration Management Layer"
        subgraph "Property Naming Convention"
            PNC["{technology}.{consumer|producer}.{property.name}<br/>Examples:<br/>• kafka.producer.bootstrap.servers<br/>• ibmmq.consumer.queue.manager<br/>• rabbitmq.producer.routing.key<br/>• sftp.consumer.private.key.path<br/>• https.producer.api.key.header<br/>• nas.consumer.mount.path"]
        end

        subgraph "Configuration Infrastructure"
            PSF[PropertySheetFetcher<br/>- Fetches from Config Server<br/>- Merges property sheets<br/>- Handles environment profiles]
            CCR[ConfigClassRegistry<br/>- Maps types to config classes<br/>- Resolves adapter/handler configs<br/>- Supports dynamic registration]
            HID[HIPIntegrationDefinition<br/>- Central config repository<br/>- Type-safe config access<br/>- Runtime property management]
        end

        subgraph "Configuration Classes"
            subgraph "Adapter Configs (Consumers)"
                AC1[DynamicKafkaAdapterConfig]
                AC2[DynamicIBMMQAdapterConfig]
                AC3[DynamicRabbitMQAdapterConfig]
                AC4[DynamicSFTPAdapterConfig]
                AC5[DynamicHttpsAdapterConfig]
                AC6[DynamicNASAdapterConfig]
            end

            subgraph "Handler Configs (Producers)"
                HC1[DynamicKafkaHandlerConfig]
                HC2[DynamicIbmmqHandlerConfig]
                HC3[DynamicRabbitMQHandlerConfig]
                HC4[DynamicSftpHandlerConfig]
                HC5[DynamicHttpsHandlerConfig]
                HC6[DynamicNasHandlerConfig]
            end
        end
    end

    %% Input Layer
    subgraph "📥 Input Adapter Layer"
        IA1[DynamicKafkaInputAdapter<br/>- Kafka Consumer<br/>- Message deserialization<br/>- Header promotion]
        IA2[DynamicIbmmqInputAdapter<br/>- IBM MQ Listener<br/>- Queue management<br/>- Transaction handling]
        IA3[DynamicRabbitMQInputAdapter<br/>- RabbitMQ Consumer<br/>- Exchange/Queue binding<br/>- Acknowledgment handling]
        IA4[DynamicSftpInputAdapter<br/>- SFTP File polling<br/>- File pattern matching<br/>- Post-process actions]
        IA5[DynamicHttpsInputAdapter<br/>- REST endpoints<br/>- OAuth/API key auth<br/>- Request validation]
        IA6[DynamicNasInputAdapter<br/>- NFS/SMB file monitoring<br/>- Directory watching<br/>- File system events]
    end

    %% Processing Layer
    subgraph "⚙️ Message Processing Flow"
        subgraph "Flow Steps"
            FS1[Validation<br/>- Schema validation<br/>- Business rules<br/>- Data integrity]
            FS2[Attribute Processing<br/>- Header extraction<br/>- Data enrichment<br/>- Transformation]
            FS3[Splitting<br/>- Message decomposition<br/>- Batch processing<br/>- Parallel routing]
            FS4[Envelope Processing<br/>- Wrapper handling<br/>- Metadata extraction<br/>- Format conversion]
            FS5[Flow Routing<br/>- Conditional routing<br/>- Target selection<br/>- Load balancing]
            FS6[Document Type Processing<br/>- Type detection<br/>- Format handling<br/>- Content analysis]
            FS7[Aggregation<br/>- Message correlation<br/>- Batch assembly<br/>- Timeout handling]
        end
    end

    %% Output Layer
    subgraph "📤 Output Handler Layer"
        OH1[DynamicKafkaOutputHandler<br/>- Kafka Producer<br/>- Topic publishing<br/>- Partitioning strategy]
        OH2[DynamicIbmmqOutputHandler<br/>- IBM MQ Publisher<br/>- Queue management<br/>- Message persistence]
        OH3[DynamicRabbitMQOutputHandler<br/>- RabbitMQ Publisher<br/>- Exchange routing<br/>- Message properties]
        OH4[DynamicSftpOutputHandler<br/>- SFTP File upload<br/>- Directory creation<br/>- File permissions]
        OH5[DynamicHttpsOutputHandler<br/>- REST API calls<br/>- Authentication<br/>- Retry logic]
        OH6[DynamicNasOutputHandler<br/>- NFS/SMB file write<br/>- Path resolution<br/>- Access control]
    end

    %% External Systems
    subgraph "🌐 External Integration Points"
        EXT1[Apache Kafka<br/>- Message streaming<br/>- Event sourcing<br/>- Real-time processing]
        EXT2[IBM MQ<br/>- Enterprise messaging<br/>- Guaranteed delivery<br/>- Transaction support]
        EXT3[RabbitMQ<br/>- Message queuing<br/>- Pub/Sub patterns<br/>- Routing flexibility]
        EXT4[SFTP Servers<br/>- Secure file transfer<br/>- Batch processing<br/>- Legacy integration]
        EXT5[HTTPS APIs<br/>- REST services<br/>- Microservices<br/>- Cloud integration]
        EXT6[NAS Storage<br/>- File systems<br/>- Shared storage<br/>- Archive systems]
    end

    %% Monitoring and Observability
    subgraph "📊 Monitoring & Observability"
        MON1[Metrics Collection<br/>- Prometheus<br/>- Custom metrics<br/>- Performance monitoring]
        MON2[Distributed Tracing<br/>- Jaeger/Zipkin<br/>- Request correlation<br/>- Latency analysis]
        MON3[Centralized Logging<br/>- ELK Stack<br/>- Log aggregation<br/>- Error tracking]
        MON4[Health Checks<br/>- Endpoint monitoring<br/>- Service discovery<br/>- Load balancer integration]
    end

    %% Configuration Flow
    PSF --> HID
    CCR --> AC1
    CCR --> AC2
    CCR --> AC3
    CCR --> AC4
    CCR --> AC5
    CCR --> AC6
    CCR --> HC1
    CCR --> HC2
    CCR --> HC3
    CCR --> HC4
    CCR --> HC5
    CCR --> HC6

    %% Data Flow - Input
    EXT1 --> IA1
    EXT2 --> IA2
    EXT3 --> IA3
    EXT4 --> IA4
    EXT5 --> IA5
    EXT6 --> IA6

    %% Processing Flow
    IA1 --> FS1
    IA2 --> FS1
    IA3 --> FS1
    IA4 --> FS1
    IA5 --> FS1
    IA6 --> FS1

    FS1 --> FS2
    FS2 --> FS3
    FS3 --> FS4
    FS4 --> FS5
    FS5 --> FS6
    FS6 --> FS7

    %% Data Flow - Output
    FS7 --> OH1
    FS7 --> OH2
    FS7 --> OH3
    FS7 --> OH4
    FS7 --> OH5
    FS7 --> OH6

    OH1 --> EXT1
    OH2 --> EXT2
    OH3 --> EXT3
    OH4 --> EXT4
    OH5 --> EXT5
    OH6 --> EXT6

    %% Configuration Usage
    HID --> IA1
    HID --> IA2
    HID --> IA3
    HID --> IA4
    HID --> IA5
    HID --> IA6
    HID --> OH1
    HID --> OH2
    HID --> OH3
    HID --> OH4
    HID --> OH5
    HID --> OH6

    %% Monitoring Connections
    IA1 --> MON1
    IA1 --> MON2
    IA1 --> MON3
    FS1 --> MON1
    FS1 --> MON2
    OH1 --> MON1
    OH1 --> MON2
    OH1 --> MON3

    %% Styling
    classDef configClass fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    classDef inputClass fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef processClass fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef outputClass fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef externalClass fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef monitorClass fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef legendClass fill:#fafafa,stroke:#424242,stroke-width:1px

    class PSF,CCR,HID,PNC,AC1,AC2,AC3,AC4,AC5,AC6,HC1,HC2,HC3,HC4,HC5,HC6 configClass
    class IA1,IA2,IA3,IA4,IA5,IA6 inputClass
    class FS1,FS2,FS3,FS4,FS5,FS6,FS7 processClass
    class OH1,OH2,OH3,OH4,OH5,OH6 outputClass
    class EXT1,EXT2,EXT3,EXT4,EXT5,EXT6 externalClass
    class MON1,MON2,MON3,MON4 monitorClass
    class L1,L2,L3,L4,L5,L6 legendClass
```

## System Overview

### 🔧 Configuration Management Layer
The foundation of the HIP Services framework, providing:
- **Consistent Property Naming**: All configurations follow `{technology}.{consumer|producer}.{property.name}` pattern
- **Type-Safe Configuration**: Strongly typed configuration classes with compile-time validation
- **Dynamic Loading**: Runtime configuration updates from external property sources
- **Environment Support**: Profile-based configuration management for different environments

### 📥 Input Adapter Layer
Handles incoming data from various external systems:
- **Protocol Support**: Kafka, IBM MQ, RabbitMQ, SFTP, HTTPS, NAS/SMB/NFS
- **Message Processing**: Deserialization, header promotion, and format conversion
- **Error Handling**: Robust error handling and retry mechanisms
- **Monitoring**: Comprehensive metrics and health checks

### ⚙️ Message Processing Flow
Seven-step processing pipeline for message transformation:
1. **Validation**: Schema and business rule validation
2. **Attribute Processing**: Header extraction and data enrichment
3. **Splitting**: Message decomposition for parallel processing
4. **Envelope Processing**: Wrapper handling and metadata extraction
5. **Flow Routing**: Conditional routing and target selection
6. **Document Type Processing**: Content analysis and format handling
7. **Aggregation**: Message correlation and batch assembly

### 📤 Output Handler Layer
Sends processed messages to target systems:
- **Multi-Protocol Support**: Same protocols as input adapters
- **Reliable Delivery**: Guaranteed message delivery with retry logic
- **Format Conversion**: Output format transformation and serialization
- **Performance Optimization**: Batching and connection pooling

### 🌐 External Integration Points
Comprehensive support for enterprise integration patterns:
- **Message Brokers**: Real-time streaming and queuing systems
- **File Systems**: Secure file transfer and storage systems
- **APIs**: RESTful web services and microservices integration
- **Legacy Systems**: Support for traditional enterprise systems

### 📊 Monitoring & Observability
Complete visibility into system operations:
- **Metrics**: Performance monitoring with Prometheus
- **Tracing**: Distributed request tracing with Jaeger
- **Logging**: Centralized log aggregation with ELK Stack
- **Health Checks**: Service health monitoring and alerting
