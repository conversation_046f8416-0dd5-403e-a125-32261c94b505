package com.dell.it.hip.config;

import java.io.Serializable;

import com.dell.it.hip.util.ThrottleSettings;

public class HIPIntegrationControlEvent implements Serializable {

    private String hipIntegrationId;
    private String hipIntegrationName;
    private String version;
    private String action; // e.g., "pause", "resume", "applyThrottle", "removeThrottle", "unregister"
    private String originServiceManagerName;
    private ThrottleSettings throttleSettings;

    public HIPIntegrationControlEvent() {}

    // Getters and setters

    public String getHipIntegrationId() {
        return hipIntegrationId;
    }

    public void setHipIntegrationId(String hipIntegrationId) {
        this.hipIntegrationId = hipIntegrationId;
    }

    public String getHipIntegrationName() {
        return hipIntegrationName;
    }

    public void setHipIntegrationName(String hipIntegrationName) {
        this.hipIntegrationName = hipIntegrationName;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getOriginServiceManagerName() {
        return originServiceManagerName;
    }

    public void setOriginServiceManagerName(String originServiceManagerName) {
        this.originServiceManagerName = originServiceManagerName;
    }

    public ThrottleSettings getThrottleSettings() {
        return throttleSettings;
    }

    public void setThrottleSettings(ThrottleSettings throttleSettings) {
        this.throttleSettings = throttleSettings;
    }
}