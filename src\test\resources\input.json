{"hipIntegrationName": "testing-doctype1", "version": "1.0", "hipIntegrationType": "INBOUND", "businessFlowType": "PAYMENT", "owner": "payment-team", "businessFlowName": "testing-doctype-flowname", "businessFlowVersion": "1.0", "tags": [{"key": "test key", "value": "test value"}], "adapters": [{"type": "ibmmqAdapter", "role": "string", "id": "string", "propertyRef": "ibmAdp"}], "flowSteps": [{"propertyRef": "docTypeStep", "type": "docTypeProcessor"}, {"propertyRef": "flowRoute", "type": "flowRouting"}], "handlers": [{"propertyRef": "test-kafka-producer", "type": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "role": "primary"}], "propertySheets": ["ibmAdp", "test-kafka-producer", "docTypeStep", "flowRoute"], "throttleSettings": {"maxMessagesPerPeriod": 0, "periodSeconds": 0, "enabled": true}, "serviceManagerName": "hipServiceManagertest1", "status": "Active"}