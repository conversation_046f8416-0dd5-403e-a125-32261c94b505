package com.dell.it.hip.util;

public interface NASFileLockManager {
    /**
     * Try to acquire a lock for a file.
     * @return true if lock acquired
     */
    boolean acquireLock(String integrationName, String version, String adapterId, String fileName, String nodeId, int expireSeconds);

    /**
     * Release a lock for a file.
     */
    void releaseLock(String integrationName, String version, String adapterId, String fileName);
}
