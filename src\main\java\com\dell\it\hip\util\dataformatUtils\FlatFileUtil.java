package com.dell.it.hip.util.dataformatUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import lombok.extern.slf4j.Slf4j;
@Slf4j
public class FlatFileUtil {
	
	/**
     * Split the input flat file into records.
     * Each record starts with a line that matches the given startRegex.
     *
     * @param payload The entire flat file content as string
     * @param startRegex The regex pattern that matches the start of each record (e.g. "^1TEST")
     * @return List of records as strings
     */
    public static List<String> splitByRegexStart(String payload, String startRegex) {
        if (payload == null || payload.isEmpty()) return Collections.emptyList();

        // Normalize line endings to '\n'
        String normalized = payload.replace("\r\n", "\n").replace("\r", "\n");

        Pattern pattern = Pattern.compile(startRegex, Pattern.MULTILINE);
        Matcher matcher = pattern.matcher(normalized);

        List<Integer> recordStartIndices = new ArrayList<>();
        while (matcher.find()) {
            recordStartIndices.add(matcher.start());
        }

        List<String> records = new ArrayList<>();

        for (int i = 0; i < recordStartIndices.size(); i++) {
            int start = recordStartIndices.get(i);
            int end = (i + 1 < recordStartIndices.size()) ? recordStartIndices.get(i + 1) : normalized.length();

            String record = normalized.substring(start, end).trim();
            if (!record.isEmpty()) {
                records.add(record);
            }
        }

        return records;
    }
    
    public static String extractFromPayload(String message, String expression) {
		if (message == null || expression == null) {
			log.error("Message or expression is null.");
			return null;
		}
		try {
			Pattern pattern = Pattern.compile(expression);
			Matcher matcher = pattern.matcher(message);
			String extractedValue;
			if (matcher.find()) {
				if (matcher.groupCount() > 0) {
	                extractedValue = matcher.group(1).trim();
	                log.info("Extracted Value : '" + extractedValue);
	            } else {
	                extractedValue = matcher.group(0).trim();
	                log.info("Extracted Value : '" + extractedValue);
	            }
				return extractedValue;
			} else {
				log.info("No match found.");
				return null;
			}
		} catch (Exception e) {
			log.error("An error occurred while extracting the value from the Flat file payload.", e);
			return null;
		}

	}
    public String contentStartsWith(String message, String expression) {
		if (message == null || expression == null) {
			log.error("Message or expression is null.");
			return null;
		}
		 try {
	            // Check if the message starts with the given expression
	            if (message.startsWith(expression)) {
	                // Use a regular expression to get the first word (without space)
	                Pattern pattern = Pattern.compile("^" + Pattern.quote(expression) + "\\S*");
	                Matcher matcher = pattern.matcher(message);

	                if (matcher.find()) {
	                    String firstWord = matcher.group();
	                    log.info("First word before space: " + firstWord);
	                    return firstWord;
	                }
	            } else {
	                log.info("String does not start with the given expression");
	            }
	        }catch (Exception e) {
			log.error("An error occurred while extracting the value from the Flat file contentStartsWith.", e);
		}
		return null;

	}
}

