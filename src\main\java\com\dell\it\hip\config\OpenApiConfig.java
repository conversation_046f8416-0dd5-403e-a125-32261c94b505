package com.dell.it.hip.config;

import java.util.List;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;

/**
 * OpenAPI 3.0 configuration for HIP Services API documentation.
 */
@Configuration
public class OpenApiConfig {

    @Value("${hip.api.version:1.0.0}")
    private String apiVersion;

    @Value("${hip.api.title:HIP Integration Platform Services}")
    private String apiTitle;

    @Value("${hip.api.description:REST API for managing and executing integration flows}")
    private String apiDescription;

    @Value("${HIP_API_SERVER_URL:http://localhost:8080}")
    private String serverUrl; 

    @Value("${hip.api.server.description:Development Server}")
    private String serverDescription;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .servers(List.of(serverInfo()))
                .addSecurityItem(securityRequirement())
                .components(securityComponents());
    }

    private Info apiInfo() {
        return new Info()
                .title(apiTitle)
                .version(apiVersion)
                .description(apiDescription + "\n\n" +
                        "## Authentication\n" +
                        "This API uses JWT Bearer token authentication. Include the token in the Authorization header:\n" +
                        "```\n" +
                        "Authorization: Bearer <your-jwt-token>\n" +
                        "```\n\n" +
                        "## Rate Limiting\n" +
                        "API requests are subject to rate limiting based on integration-specific throttle settings.\n\n" +
                        "## Error Handling\n" +
                        "All error responses follow a standardized format with correlation IDs for tracking.\n\n" +
                        "## Versioning\n" +
                        "API versioning is handled through URL paths (e.g., /api/v1/...).")
                .contact(contact())
                .license(license());
    }

    private Contact contact() {
        return new Contact()
                .name("HIP Platform Team")
                .email("<EMAIL>")
                .url("https://dell.com/hip-platform");
    }

    private License license() {
        return new License()
                .name("Dell Technologies Internal License")
                .url("https://dell.com/licenses/internal");
    }

    private Server serverInfo() {
        return new Server()
                .url(serverUrl)
                .description(serverDescription);
    }

    private SecurityRequirement securityRequirement() {
        return new SecurityRequirement()
                .addList("bearerAuth");
    }

    private Components securityComponents() {
        return new Components()
                .addSecuritySchemes("bearerAuth",
                        new SecurityScheme()
                                .type(SecurityScheme.Type.HTTP)
                                .scheme("bearer")
                                .bearerFormat("JWT")
                                .description("JWT Bearer token authentication. " +
                                        "Obtain a token from the /api/auth/login endpoint."));
    }
}
