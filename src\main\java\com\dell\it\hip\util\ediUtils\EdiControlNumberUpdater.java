package com.dell.it.hip.util.ediUtils;
import java.io.ByteArrayOutputStream;
import java.io.StringWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import io.xlate.edi.stream.EDIOutputFactory;
import io.xlate.edi.stream.EDIStreamException;
import io.xlate.edi.stream.EDIStreamWriter;

public class EdiControlNumberUpdater {
    private final RedissonClient redisson;
    private final String baseKey;
    private final String isaSenderId;
    private final String isaReceiverId;
    private final String isaQualifier;
    private final String gsFunctionalCode;
    private final int controlNumberLength = 9;

    public EdiControlNumberUpdater(RedissonClient redisson, String baseKey,
                                 String isaSenderId, String isaReceiverId,
                                 String isaQualifier, String gsFunctionalCode) {
        this.redisson = redisson;
        this.baseKey = baseKey;
        this.isaSenderId = isaSenderId;
        this.isaReceiverId = isaReceiverId;
        this.isaQualifier = isaQualifier;
        this.gsFunctionalCode = gsFunctionalCode;
    }

    public String updateControlNumbers(String ediContent) throws Exception {
        if (hasCompleteEnvelope(ediContent)) {
            return replaceExistingControlNumbers(ediContent);
        } else {
            return addNewEnvelope(ediContent);
        }
    }

    private boolean hasCompleteEnvelope(String ediContent) {
        String trimmed = ediContent.trim();
        return trimmed.startsWith("ISA") && trimmed.contains("IEA");
    }

    private String replaceExistingControlNumbers(String ediContent) throws Exception {
        ControlNumbers newControls = generateControlNumbers();
        StringBuilder output = new StringBuilder();
        String[] segments = ediContent.split("\\r?\\n|\\r");
        boolean inIsa = false;
        boolean inGs = false;
        boolean inIea = false;
        boolean inGe = false;
        int transactionCount = 0;

        for (String segment : segments) {
            if (segment.startsWith("ISA")) {
                inIsa = true;
                output.append(replaceIsaControlNumber(segment, newControls.isaControlNumber)).append("\n");
            } else if (segment.startsWith("GS")) {
                inGs = true;
                output.append(replaceGsControlNumber(segment, newControls.gsControlNumber)).append("\n");
            } else if (segment.startsWith("IEA")) {
                inIea = true;
                output.append(replaceIeaControlNumber(segment, newControls.isaControlNumber)).append("\n");
            } else if (segment.startsWith("GE")) {
                inGe = true;
                // Count transactions between GS and GE
                transactionCount = countTransactions(segments);
                output.append(replaceGeControlNumber(segment, newControls.gsControlNumber, transactionCount)).append("\n");
            } else {
                output.append(segment).append("\n");
            }
        }

        // Validation
        if (!inIsa || !inIea || !inGs || !inGe) {
            throw new Exception("Invalid EDI envelope structure");
        }

        return output.toString();
    }

    private String replaceIsaControlNumber(String isaSegment, String newControlNumber) {
        String[] elements = isaSegment.split("\\*", -1);
        if (elements.length >= 14) {
            elements[13] = newControlNumber; // ISA13 is control number
        }
        return String.join("*", elements);
    }

    private String replaceGsControlNumber(String gsSegment, String newControlNumber) {
        String[] elements = gsSegment.split("\\*", -1);
        if (elements.length >= 7) {
            elements[6] = newControlNumber; // GS06 is control number
        }
        return String.join("*", elements);
    }

    private String replaceIeaControlNumber(String ieaSegment, String newControlNumber) {
        String[] elements = ieaSegment.split("\\*", -1);
        if (elements.length >= 3) {
            elements[2] = newControlNumber; // IEA02 is control number
        }
        return String.join("*", elements);
    }

    private String replaceGeControlNumber(String geSegment, String newControlNumber, int transactionCount) {
        String[] elements = geSegment.split("\\*", -1);
        if (elements.length >= 3) {
            elements[1] = String.valueOf(transactionCount); // GE01 is transaction count
            elements[2] = newControlNumber; // GE02 is control number
        }
        return String.join("*", elements);
    }

    private int countTransactions(String[] segments) {
        int count = 0;
        boolean inGroup = false;
        
        for (String segment : segments) {
            if (segment.startsWith("GS")) {
                inGroup = true;
            } else if (segment.startsWith("GE")) {
                inGroup = false;
            } else if (inGroup && segment.startsWith("ST")) {
                count++;
            }
        }
        
        return count;
    }

    private String addNewEnvelope(String ediContent) throws Exception {
        ControlNumbers controls = generateControlNumbers();
        StringWriter writer = new StringWriter();
        
        EDIOutputFactory factory = EDIOutputFactory.newFactory();
		ByteArrayOutputStream stream = new ByteArrayOutputStream();
		EDIStreamWriter ediWriter = factory.createEDIStreamWriter(stream);
            // Write ISA Segment
            writeIsaSegment(ediWriter, controls);           
            // Write GS Segment
            writeGsSegment(ediWriter, controls);           
            // Write the original content
            writer.write(ediContent);
            writer.write("\n");          
            // Write GE Segment
            writeGeSegment(ediWriter, controls, 1); // Assuming 1 transaction           
            // Write IEA Segment
            writeIeaSegment(ediWriter, controls);
        
        return writer.toString();
    }

    private void writeIsaSegment(EDIStreamWriter writer, ControlNumbers controls) 
			throws EDIStreamException {

		writer.writeStartSegment("ISA")
		.writeElement("00") // Authorization qualifier
		.writeElement("")   // Authorization info
		.writeElement("00") // Security qualifier
		.writeElement("")   // Security info
		.writeElement(isaSenderId)
		.writeElement(isaQualifier)
		.writeElement(isaReceiverId)
		.writeElement(isaQualifier)
		.writeElement(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMdd")))
		.writeElement(LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmm")))
		.writeElement("U")  // Standards ID
		.writeElement("00401") // Version
		.writeElement(controls.isaControlNumber)
		.writeElement("0")  // Ack request
		.writeElement("P")  // Usage indicator
		.writeElement(">")  // Component separator
		.writeEndSegment();
	}

	private void writeGsSegment(EDIStreamWriter writer, ControlNumbers controls) 
			throws EDIStreamException {

		writer.writeStartSegment("GS")
		.writeElement(gsFunctionalCode)
		.writeElement(isaSenderId)
		.writeElement(isaReceiverId)
		.writeElement(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")))
		.writeElement(LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmm")))
		.writeElement(controls.gsControlNumber)
		.writeElement("X")  // Responsible agency
		.writeElement("004010") // Version
		.writeEndSegment();
	}

	private void writeGeSegment(EDIStreamWriter writer, ControlNumbers controls, int transactionCount) 
			throws EDIStreamException {

		writer.writeStartSegment("GE")
		.writeElement(String.valueOf(transactionCount))
		.writeElement(controls.gsControlNumber)
		.writeEndSegment();
	}

	private void writeIeaSegment(EDIStreamWriter writer, ControlNumbers controls) 
			throws EDIStreamException {

		writer.writeStartSegment("IEA")
		.writeElement("1")  // Number of groups
		.writeElement(controls.isaControlNumber)
		.writeEndSegment();
	}

    private ControlNumbers generateControlNumbers() throws InterruptedException {
		String isaControlNumber = generateNumber("isa", controlNumberLength);
		String gsControlNumber = generateNumber("gs:" + isaControlNumber, controlNumberLength);

		return new ControlNumbers(
				isaControlNumber,
				gsControlNumber,
				null, // ST control number not needed at envelope level
				isaSenderId,
				isaReceiverId,
				LocalDateTime.now()
				);
	}

	private String generateNumber(String keySuffix, int length) throws InterruptedException {
		String lockKey = baseKey + ":lock:" + keySuffix;
		RLock lock = redisson.getLock(lockKey);

		try {
			if (!lock.tryLock(5, 30, TimeUnit.SECONDS)) {
				throw new RuntimeException("Failed to acquire lock for: " + keySuffix);
			}

			RAtomicLong counter = redisson.getAtomicLong(baseKey + ":" + keySuffix);
			return String.format("%0" + length + "d", counter.getAndIncrement());
		} finally {
			lock.unlock();
		}
	}

	private static class ControlNumbers {
		final String isaControlNumber;
		final String gsControlNumber;
		final String stControlNumber;
		final String senderId;
		final String receiverId;
		final LocalDateTime timestamp;

		ControlNumbers(String isa, String gs, String st, 
				String sender, String receiver, LocalDateTime ts) {
			this.isaControlNumber = isa;
			this.gsControlNumber = gs;
			this.stControlNumber = st;
			this.senderId = sender;
			this.receiverId = receiver;
			this.timestamp = ts;
		}
	}
}