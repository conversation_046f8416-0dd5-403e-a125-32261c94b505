# HTTPS Client Development and Configuration Analysis

## Overview

This document provides a comprehensive analysis of the HTTPS configuration properties in the HIP services framework and documents the development of the HttpsClient utility class for testing HTTPS adapter and handler functionality.

## 1. Configuration Property Analysis

### DynamicHttpsAdapterConfig Analysis

#### Used Properties (with JSON bindings added):
- ✅ `apiKeyHeader` - `@JsonProperty("https.consumer.api.key.header")` - Used in `authenticate()` method
- ✅ `apiKeyValue` - `@JsonProperty("https.consumer.api.key.value")` - Used in `authenticate()` method
- ✅ `oAuthRequired` - `@JsonProperty("https.consumer.oauth.required")` - Used in `authenticate()` method
- ✅ `headersToExtract` - `@JsonProperty("https.consumer.headers.to.extract")` - Used in `buildMessage()` method
- ✅ `maxRequestSizeBytes` - `@JsonProperty("https.consumer.max.request.size.bytes")` - Used in `buildProducer()` method
- ✅ `maxConcurrency` - `@JsonProperty("https.consumer.max.concurrency")` - Used in `buildProducer()` method
- ✅ `requestTimeoutMs` - `@JsonProperty("https.consumer.request.timeout.ms")` - Used in `buildProducer()` method
- ✅ `rateLimitPerSecond` - `@JsonProperty("https.consumer.rate.limit.per.second")` - Used in throttling logic
- ✅ `allowedHttpMethods` - `@JsonProperty("https.consumer.allowed.http.methods")` - Used in `buildMessage()` method

#### Removed Unused Properties:
- ❌ `oAuthIssuerUrl` - Not used anywhere
- ❌ `maxRetries` - Not used anywhere
- ❌ `retryBackoffMs` - Not used anywhere
- ❌ `circuitBreakerEnabled` - Not used anywhere
- ❌ `circuitBreakerThreshold` - Not used anywhere
- ❌ `circuitBreakerResetMs` - Not used anywhere
- ❌ `responseTimeoutMs` - Not used anywhere
- ❌ `allowedOrigins` - Not used anywhere
- ❌ `allowedMethods` - Not used anywhere
- ❌ `allowedHeaders` - Not used anywhere
- ❌ `tlsClientCertRequired` - Not used anywhere
- ❌ `allowedCipherSuites` - Not used anywhere

### DynamicHttpsHandlerConfig Analysis

#### Used Properties (with JSON bindings added):
- ✅ `endpointUrl` - `@JsonProperty("https.producer.endpoint.url")` - Used in `sendHttp()` method
- ✅ `httpMethod` - `@JsonProperty("https.producer.http.method")` - Used in `sendHttp()` method
- ✅ `headers` - `@JsonProperty("https.producer.headers")` - Used in `sendHttp()` method
- ✅ `apiKeyHeader` - `@JsonProperty("https.producer.api.key.header")` - Used in `sendHttp()` method
- ✅ `apiKeyValue` - `@JsonProperty("https.producer.api.key.value")` - Used in `sendHttp()` method
- ✅ `connectTimeoutMs` - `@JsonProperty("https.producer.connect.timeout.ms")` - Used in `sendHttp()` method
- ✅ `readTimeoutMs` - `@JsonProperty("https.producer.read.timeout.ms")` - Used in `sendHttp()` method
- ✅ `maxInMemorySize` - `@JsonProperty("https.producer.max.in.memory.size")` - Used in `sendHttp()` method
- ✅ `retryAttempts` - `@JsonProperty("https.producer.retry.attempts")` - Used in `doHandle()` method
- ✅ `retryBackoffMs` - `@JsonProperty("https.producer.retry.backoff.ms")` - Used in `doHandle()` method
- ✅ `compressed` - `@JsonProperty("https.producer.compressed")` - Used in `sendHttp()` method
- ✅ `oauthEnabled` - `@JsonProperty("https.producer.oauth.enabled")` - Used in `sendHttp()` method
- ✅ `oauthTokenUrl` - `@JsonProperty("https.producer.oauth.token.url")` - Used in OAuth2 token service
- ✅ `oauthClientId` - `@JsonProperty("https.producer.oauth.client.id")` - Used in OAuth2 token service
- ✅ `oauthClientSecret` - `@JsonProperty("https.producer.oauth.client.secret")` - Used in OAuth2 token service
- ✅ `oauthScope` - `@JsonProperty("https.producer.oauth.scope")` - Used in OAuth2 token service
- ✅ `oauthAudience` - `@JsonProperty("https.producer.oauth.audience")` - Used in OAuth2 token service
- ✅ `oauthAdditionalParams` - `@JsonProperty("https.producer.oauth.additional.params")` - Used in OAuth2 token service

#### Removed Unused Properties:
- ❌ `sslKeystorePath` - Not used anywhere
- ❌ `sslKeystorePassword` - Not used anywhere
- ❌ `sslTruststorePath` - Not used anywhere
- ❌ `sslTruststorePassword` - Not used anywhere
- ❌ `maxConnections` - Not used anywhere
- ❌ `maxConcurrency` - Not used anywhere
- ❌ `headersToExtract` - Not used anywhere
- ❌ `enableWiretap` - Not used anywhere
- ❌ `properties` - Not used anywhere

## 2. Configuration Changes Made

### DynamicHttpsAdapterConfig Updates:
1. Added missing `@JsonProperty` annotations for all used properties
2. Removed 12 unused properties and their imports
3. Added Lombok `@Getter` and `@Setter` annotations
4. Cleaned up imports and removed manual getters/setters

### DynamicHttpsHandlerConfig Updates:
1. Added missing `@JsonProperty` annotations for all used properties
2. Removed 9 unused properties
3. Added Lombok `@Getter` and `@Setter` annotations
4. Kept the `isOauthEnabled()` method for boolean property access

## 3. HttpsClient Utility Development

### HttpsClientConfig Class
Created a comprehensive configuration class that mirrors the patterns used in other client configs:

**Key Features:**
- Supports both consumer (adapter testing) and producer (handler testing) configurations
- Builder pattern for easy configuration
- Pre-configured factory methods for different authentication types
- All properties from the cleaned-up adapter/handler configs

**Test Configuration Properties:**
```java
// Basic Consumer Configuration
HttpsClientConfig.createConsumerConfig()
    .endpointUrl("https://httpbin.org/post")
    .httpMethod("POST")
    .maxConcurrency(100)
    .maxRequestSizeBytes(2 * 1024 * 1024)

// Basic Producer Configuration  
HttpsClientConfig.createProducerConfig()
    .endpointUrl("https://httpbin.org/post")
    .httpMethod("POST")
    .connectTimeoutMs(10000L)
    .readTimeoutMs(30000L)
    .retryAttempts(3)

// API Key Authentication
HttpsClientConfig.createApiKeyConfig(url, "X-API-Key", "api-key-value")

// OAuth2 Authentication
HttpsClientConfig.createOAuth2Config(url, tokenUrl, clientId, clientSecret)
```

### HttpsClient Class
Created a comprehensive HTTPS client utility that mirrors the implementation patterns from existing adapter/handler classes:

**Key Features:**
- **Message Sending**: Uses the same approach as `DynamicHttpsOutputHandler` with WebClient
- **Message Receiving**: Simulates adapter functionality with authentication and validation
- **Authentication Support**: API key and OAuth2 authentication (with token caching)
- **Connection Management**: Proper WebClient setup with timeouts and configuration
- **Compression**: Supports gzip compression/decompression
- **Retry Logic**: Configurable retry attempts with backoff
- **Continuous Monitoring**: Provides listener functionality for simulated message reception

**Core Methods:**
```java
// Send messages (handler testing)
HttpsResponse response = httpsClient.sendMessage("Hello World");
HttpsResponse response = httpsClient.sendMessage(byteArray, customHeaders);

// Receive messages (adapter testing)
HttpsMessage message = httpsClient.receiveMessage(requestBody, headers);

// Continuous monitoring (simulated)
httpsClient.startListener(message -> {
    System.out.println("Received: " + message.getContent());
});

// Wait for specific number of messages
List<HttpsMessage> messages = httpsClient.waitForMessages(5, 30000, 
    message -> System.out.println("Got: " + message.getContent()));
```

**Authentication Support:**
- **API Key**: Automatic header injection and validation
- **OAuth2**: Token acquisition, caching, and automatic refresh
- **Bearer Token**: Automatic Authorization header management

## 4. Comprehensive Test Suite (HttpsClientTest)

### Test Class Structure
Created `HttpsClientTest.java` following the same patterns as existing client tests:
- **Location**: `src/test/java/com/dell/it/hip/client/HttpsClientTest.java`
- **Framework**: JUnit 5 with standard assertions
- **Setup/Teardown**: Proper resource management
- **External Service**: Uses httpbin.org for reliable testing

### Test Coverage (18+ Test Methods)

#### **Core Functionality Tests:**
1. ✅ **`testSendMessage()`** - Basic HTTPS message sending (handler testing)
2. ✅ **`testSendByteArrayMessage()`** - Binary message sending
3. ✅ **`testSendMessageWithCustomHeaders()`** - Custom header support
4. ✅ **`testReceiveMessage()`** - Message receiving (adapter testing)

#### **Authentication Tests:**
5. ✅ **`testSendMessageWithApiKey()`** - API key authentication for sending
6. ✅ **`testReceiveMessageWithAuthentication()`** - API key authentication for receiving
7. ✅ **`testReceiveMessageAuthenticationFailure()`** - Authentication failure handling

#### **Advanced Feature Tests:**
8. ✅ **`testCompressionHandling()`** - Gzip compression/decompression
9. ✅ **`testRetryMechanism()`** - Retry logic with backoff
10. ✅ **`testConnectionManagement()`** - Connection lifecycle management

#### **Listener and Monitoring Tests:**
11. ✅ **`testMessageListener()`** - Continuous message monitoring (simulated)
12. ✅ **`testWaitForMessages()`** - Wait for specific number of messages

#### **Configuration and Error Tests:**
13. ✅ **`testCustomConfiguration()`** - Custom HTTPS configurations
14. ✅ **`testErrorHandling()`** - Invalid configuration and connection errors

#### **Robustness Tests:**
15. ✅ **`testLargeMessageHandling()`** - Large messages (10KB+)
16. ✅ **`testSpecialCharacterHandling()`** - Unicode, special chars, JSON
17. ✅ **`testConcurrentOperations()`** - Concurrent HTTPS operations
18. ✅ **`testHttpMethods()`** - Different HTTP methods (POST, PUT, PATCH)
19. ✅ **`testRequestSizeLimit()`** - Request size limit enforcement

#### **Manual Testing:**
20. ✅ **`main()` method** - Interactive testing against actual HTTPS endpoints

### Key Test Features:

```java
// HTTPS Message Sending Testing (Handler functionality)
@Test
void testSendMessage() throws Exception {
    String testMessage = TEST_MESSAGE + System.currentTimeMillis();
    
    HttpsClient.HttpsResponse response = assertDoesNotThrow(() -> {
        return httpsClient.sendMessage(testMessage);
    });
    
    assertNotNull(response, "Response should not be null");
    assertTrue(response.isSuccessful(), "Response should be successful");
    assertEquals(200, response.getStatusCode(), "Status code should be 200");
}

// Authentication Testing
@Test
void testReceiveMessageWithAuthentication() throws Exception {
    HttpsClientConfig authConsumerConfig = new HttpsClientConfig.Builder()
        .apiKeyHeader("X-API-Key")
        .apiKeyValue("test-api-key-123")
        .build();
    
    Map<String, String> headers = new HashMap<>();
    headers.put("X-API-Key", "test-api-key-123"); // Correct API key
    
    HttpsClient.HttpsMessage receivedMessage = authClient.receiveMessage(requestBody, headers);
    assertNotNull(receivedMessage, "Should have received authenticated message");
}

// Listener Testing (Simulated)
@Test
void testMessageListener() throws Exception {
    AtomicInteger messageCount = new AtomicInteger(0);
    CountDownLatch latch = new CountDownLatch(2);
    
    httpsClient.startListener(message -> {
        messageCount.incrementAndGet();
        latch.countDown();
    });
    
    boolean received = latch.await(15, TimeUnit.SECONDS);
    assertTrue(received, "Should have processed simulated messages within timeout");
}
```

## 5. Usage Instructions

### Basic Usage Example:
```java
// Create configurations
HttpsClientConfig consumerConfig = HttpsClientConfig.createConsumerConfig();
HttpsClientConfig producerConfig = HttpsClientConfig.createProducerConfig();

// Create client
try (HttpsClient httpsClient = new HttpsClient(consumerConfig, producerConfig)) {
    
    // Test handler functionality (sending)
    HttpsClient.HttpsResponse response = httpsClient.sendMessage("Hello from HIP HTTPS Client!");
    System.out.println("Response: " + response.getStatusCode() + " - " + response.getBody());
    
    // Test adapter functionality (receiving)
    Map<String, String> headers = Map.of("Content-Type", "application/json");
    HttpsClient.HttpsMessage receivedMessage = httpsClient.receiveMessage("Test message", headers);
    System.out.println("Received: " + receivedMessage.getContent());
    
    // Continuous monitoring (simulated)
    httpsClient.startListener(message -> {
        System.out.println("Message received: " + message.getContent());
    });
    
    Thread.sleep(10000); // Monitor for 10 seconds
    httpsClient.stopListener();
}
```

### API Key Authentication Example:
```java
HttpsClientConfig apiKeyConfig = HttpsClientConfig.createApiKeyConfig(
    "https://api.example.com/endpoint", 
    "X-API-Key", 
    "your-api-key-here"
);

try (HttpsClient client = new HttpsClient(consumerConfig, apiKeyConfig)) {
    HttpsClient.HttpsResponse response = client.sendMessage("Authenticated message");
    System.out.println("Authenticated request status: " + response.getStatusCode());
}
```

### OAuth2 Authentication Example:
```java
HttpsClientConfig oauth2Config = HttpsClientConfig.createOAuth2Config(
    "https://api.example.com/endpoint",
    "https://auth.example.com/oauth/token",
    "your-client-id",
    "your-client-secret"
);

try (HttpsClient client = new HttpsClient(consumerConfig, oauth2Config)) {
    HttpsClient.HttpsResponse response = client.sendMessage("OAuth2 authenticated message");
    System.out.println("OAuth2 request status: " + response.getStatusCode());
}
```

## 6. Technical Implementation Details

### HTTP Client Setup:
- Uses Spring WebClient with Reactor Netty for non-blocking operations
- Supports connection timeouts, read timeouts, and memory limits
- Implements proper error handling and response processing

### Authentication:
- **API Key**: Automatic header injection and validation
- **OAuth2**: Token acquisition with caching and automatic refresh
- **Security**: Proper token validation and error handling

### Compression:
- Uses `CompressionUtil` for gzip compression/decompression
- Automatic Content-Encoding header management
- Compatible with existing framework compression patterns

### Error Handling:
- Comprehensive exception handling and logging
- Retry logic with configurable attempts and backoff
- Proper HTTP status code handling and response parsing

## 7. Independence and Self-Containment

- **No HIP Application Dependency**: Runs independently without full application startup
- **Isolated Testing**: Each test method is independent
- **Resource Management**: Proper setup/teardown ensures clean environment
- **External Service**: Uses httpbin.org for reliable and consistent testing

## 8. Future Enhancements

1. **SSL/TLS Configuration**: Add support for custom keystores and truststores
2. **Circuit Breaker**: Implement circuit breaker pattern for resilience
3. **Metrics Integration**: Add metrics collection for monitoring
4. **WebSocket Support**: Add WebSocket client capabilities
5. **Mock Server**: Add embedded mock server for faster unit testing
6. **Advanced OAuth2**: Support for different OAuth2 flows and token types
7. **Request/Response Interceptors**: Add support for custom interceptors
