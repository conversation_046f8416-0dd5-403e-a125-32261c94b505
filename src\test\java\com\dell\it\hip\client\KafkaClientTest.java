package com.dell.it.hip.client;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Disabled;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for KafkaClient utility.
 * 
 * Note: These tests are disabled by default as they require an actual Kafka server.
 * To run these tests:
 * 1. Ensure Kafka server is accessible at the configured connection details
 * 2. Remove @Disabled annotations
 * 3. Update connection properties if needed
 */
@Disabled("Requires actual Kafka server - enable for integration testing")
public class KafkaClientTest {
    
    private static final Logger logger = LoggerFactory.getLogger(KafkaClientTest.class);
    
    private KafkaClient kafkaClient;
    private KafkaClientConfig consumerConfig;
    private KafkaClientConfig producerConfig;
    
    @BeforeEach
    void setUp() {
        // Create configurations using the test properties
        consumerConfig = KafkaClientConfig.createConsumerConfig();
        producerConfig = KafkaClientConfig.createProducerConfig();
        
        // Create KafkaClient instance
        kafkaClient = new KafkaClient(consumerConfig, producerConfig);
        
        logger.info("KafkaClient test setup completed");
    }
    
    @AfterEach
    void tearDown() {
        if (kafkaClient != null) {
            kafkaClient.close();
        }
        logger.info("KafkaClient test cleanup completed");
    }
    
    @Test
    void testSendMessage() throws Exception {
        // Test sending a simple text message
        String testMessage = "Test message from KafkaClient - " + System.currentTimeMillis();
        
        assertDoesNotThrow(() -> {
            kafkaClient.sendMessage(testMessage);
        });
        
        logger.info("Successfully sent message: {}", testMessage);
    }
    
    @Test
    void testSendByteArrayMessage() throws Exception {
        // Test sending a byte array message
        byte[] testPayload = "Binary test message".getBytes();
        
        assertDoesNotThrow(() -> {
            kafkaClient.sendMessage(testPayload);
        });
        
        logger.info("Successfully sent byte array message");
    }
    
    @Test
    void testSendMessageWithHeaders() throws Exception {
        // Test sending a message with custom headers
        String testMessage = "Test message with headers - " + System.currentTimeMillis();
        Map<String, String> headers = new HashMap<>();
        headers.put("messageId", "test-123");
        headers.put("source", "KafkaClientTest");
        headers.put("timestamp", String.valueOf(System.currentTimeMillis()));
        
        assertDoesNotThrow(() -> {
            kafkaClient.sendMessage(testMessage.getBytes(), headers);
        });
        
        logger.info("Successfully sent message with headers: {}", testMessage);
    }
    
    @Test
    void testReceiveMessage() throws Exception {
        // First send a message
        String testMessage = "Test receive message - " + System.currentTimeMillis();
        kafkaClient.sendMessage(testMessage);
        
        // Wait a bit for the message to be available
        Thread.sleep(2000);
        
        // Then try to receive it
        KafkaClient.KafkaMessage receivedMessage = kafkaClient.receiveMessage(10000); // 10 second timeout
        
        assertNotNull(receivedMessage, "Should have received a message");
        assertTrue(receivedMessage.getContent().contains("Test receive message"), "Received message should contain expected content");
        
        logger.info("Successfully received message: {}", receivedMessage);
    }
    
    @Test
    void testReceiveMessageTimeout() throws Exception {
        // Test receiving with timeout when no message is available
        KafkaClient.KafkaMessage receivedMessage = kafkaClient.receiveMessage(2000); // 2 second timeout
        
        // Should return null when no message is available within timeout
        // Note: This might receive existing messages in the topic, so we just check it doesn't throw
        logger.info("Receive timeout test completed, received: {}", receivedMessage);
    }
    
    @Test
    void testMessageListener() throws Exception {
        AtomicInteger messageCount = new AtomicInteger(0);
        CountDownLatch latch = new CountDownLatch(3);
        
        // Start listener
        kafkaClient.startListener(message -> {
            logger.info("Received message via listener: {}", message);
            messageCount.incrementAndGet();
            latch.countDown();
        });
        
        // Send multiple messages
        for (int i = 1; i <= 3; i++) {
            String testMessage = "Listener test message " + i + " - " + System.currentTimeMillis();
            kafkaClient.sendMessage(testMessage);
            Thread.sleep(100); // Small delay between messages
        }
        
        // Wait for messages to be received
        boolean received = latch.await(30, TimeUnit.SECONDS);
        
        kafkaClient.stopListener();
        
        assertTrue(received, "Should have received all 3 messages within timeout");
        assertEquals(3, messageCount.get(), "Should have received exactly 3 messages");
        
        logger.info("Successfully tested message listener with {} messages", messageCount.get());
    }
    
    @Test
    void testWaitForMessages() throws Exception {
        AtomicInteger processedCount = new AtomicInteger(0);
        
        // Send messages first
        for (int i = 1; i <= 2; i++) {
            String testMessage = "Wait test message " + i + " - " + System.currentTimeMillis();
            kafkaClient.sendMessage(testMessage);
        }
        
        // Wait for specific number of messages
        List<KafkaClient.KafkaMessage> messages = assertDoesNotThrow(() -> {
            return kafkaClient.waitForMessages(2, 30000, message -> {
                logger.info("Processed message: {}", message);
                processedCount.incrementAndGet();
            });
        });
        
        assertEquals(2, messages.size(), "Should have received exactly 2 messages");
        assertEquals(2, processedCount.get(), "Should have processed exactly 2 messages");
        
        logger.info("Successfully waited for and processed {} messages", processedCount.get());
    }
    
    @Test
    void testCustomConfiguration() throws Exception {
        // Test with custom configuration
        KafkaClientConfig customConsumerConfig = new KafkaClientConfig.Builder()
            .bootstrapServers("kafnlprfgscm.us.dell.com:9094")
            .topic("GOBIG.B2BFLOWMAC.E2EV.DIT")
            .groupId("custom-test-group")
            .clientId("custom-test-consumer")
            .securityProtocol("SASL_SSL")
            .saslMechanism("PLAIN")
            .saslJaasConfig("org.apache.kafka.common.security.plain.PlainLoginModule required")
            .username("svc_nphipkafkagscm")
            .password("ji?zVD58h2WkfxmY0AL+N*PC")
            .sslTruststoreLocation("/etc/ssl/certs/kafka-truststore/kafka.client.truststore.jks")
            .sslTruststorePassword("good8008")
            .sslTruststoreType("JKS")
            .protocols("TLSv1.2")
            .autoOffsetReset("earliest")
            .maxPollRecords(100)
            .build();
        
        KafkaClientConfig customProducerConfig = new KafkaClientConfig.Builder()
            .bootstrapServers("kafnlprfgscm.us.dell.com:9094")
            .topic("GOBIG.B2BFLOWMAC.E2EV.DIT")
            .clientId("custom-test-producer")
            .securityProtocol("SASL_SSL")
            .saslMechanism("PLAIN")
            .saslJaasConfig("org.apache.kafka.common.security.plain.PlainLoginModule required")
            .username("svc_nphipkafkagscm")
            .password("ji?zVD58h2WkfxmY0AL+N*PC")
            .sslTruststoreLocation("/etc/ssl/certs/kafka-truststore/kafka.client.truststore.jks")
            .sslTruststorePassword("good8008")
            .sslTruststoreType("JKS")
            .protocols("TLSv1.2")
            .acks(1)
            .retries(5)
            .enableIdempotence(false)
            .build();
        
        try (KafkaClient customClient = new KafkaClient(customConsumerConfig, customProducerConfig)) {
            String testMessage = "Custom config test - " + System.currentTimeMillis();
            
            assertDoesNotThrow(() -> {
                customClient.sendMessage(testMessage);
            });
            
            logger.info("Successfully tested custom configuration");
        }
    }
    
    @Test
    void testIdempotenceConfiguration() throws Exception {
        // Test with idempotence enabled - should automatically set acks=all
        KafkaClientConfig idempotentProducerConfig = new KafkaClientConfig.Builder()
            .bootstrapServers("kafnlprfgscm.us.dell.com:9094")
            .topic("GOBIG.B2BFLOWMAC.E2EV.DIT")
            .clientId("idempotent-test-producer")
            .securityProtocol("SASL_SSL")
            .saslMechanism("PLAIN")
            .saslJaasConfig("org.apache.kafka.common.security.plain.PlainLoginModule required")
            .username("svc_nphipkafkagscm")
            .password("ji?zVD58h2WkfxmY0AL+N*PC")
            .sslTruststoreLocation("/etc/ssl/certs/kafka-truststore/kafka.client.truststore.jks")
            .sslTruststorePassword("good8008")
            .sslTruststoreType("JKS")
            .protocols("TLSv1.2")
            .acks(1)  // This should be overridden to "all" when idempotence is enabled
            .retries(5)
            .enableIdempotence(true)  // This should force acks=all
            .build();

        try (KafkaClient idempotentClient = new KafkaClient(consumerConfig, idempotentProducerConfig)) {
            String testMessage = "Idempotent producer test - " + System.currentTimeMillis();

            // This should not throw ConfigException anymore
            assertDoesNotThrow(() -> {
                idempotentClient.sendMessage(testMessage);
            });

            logger.info("Successfully tested idempotent producer configuration");
        }
    }

    @Test
    void testErrorHandling() {
        // Test with invalid configuration to verify error handling
        KafkaClientConfig invalidConfig = new KafkaClientConfig.Builder()
            .bootstrapServers("invalid.host:9092")
            .topic("invalid.topic")
            .groupId("invalid-group")
            .build();
        
        try (KafkaClient invalidClient = new KafkaClient(invalidConfig, invalidConfig)) {
            // This should throw an exception due to invalid configuration
            assertThrows(Exception.class, () -> {
                invalidClient.sendMessage("This should fail");
            });
            
            logger.info("Correctly handled invalid configuration");
        }
    }
    
    /**
     * Manual test method for interactive testing.
     * This method can be run manually to test the KafkaClient interactively.
     */
    public static void main(String[] args) {
        Logger mainLogger = LoggerFactory.getLogger("KafkaClientManualTest");
        
        try {
            mainLogger.info("Starting KafkaClient manual test...");
            
            KafkaClientConfig consumerConfig = KafkaClientConfig.createConsumerConfig();
            KafkaClientConfig producerConfig = KafkaClientConfig.createProducerConfig();
            
            try (KafkaClient client = new KafkaClient(consumerConfig, producerConfig)) {
                
                // Test 1: Send a message
                mainLogger.info("Test 1: Sending message...");
                String testMessage = "Manual test message - " + System.currentTimeMillis();
                client.sendMessage(testMessage);
                mainLogger.info("Message sent successfully");
                
                // Test 2: Receive a message
                mainLogger.info("Test 2: Receiving message...");
                KafkaClient.KafkaMessage received = client.receiveMessage(10000);
                if (received != null) {
                    mainLogger.info("Received message: {}", received);
                } else {
                    mainLogger.info("No message received within timeout");
                }
                
                // Test 3: Start listener for a short time
                mainLogger.info("Test 3: Starting listener for 15 seconds...");
                client.startListener(message -> {
                    mainLogger.info("Listener received: {}", message);
                });
                
                Thread.sleep(15000); // Listen for 15 seconds
                client.stopListener();
                
                mainLogger.info("Manual test completed successfully");
            }
            
        } catch (Exception e) {
            mainLogger.error("Manual test failed: {}", e.getMessage(), e);
        }
    }
}
