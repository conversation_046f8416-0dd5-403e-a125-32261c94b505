package com.dell.it.hip.config;

import java.io.Serializable;
import java.util.Map;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;


public class HIPClusterEvent implements Serializable {

    private String eventType;           // e.g. PAUSE, RESUME, SHUTDOWN, THROTTLE_UPDATE, etc.
    private String targetType;          // "ADAPTER" or "HANDLER" (could extend for other types)
    private String integrationName;     // Target integration (or "*" for broadcast)
    private String integrationVersion;  // Target version (or "*" for all)
    private String targetId;            // AdapterId or HandlerId (or "*" for all)
    private Map<String, Object> payload; // Optional event payload
    private boolean adapterTarget;
    private boolean handlerTarget;

    // --- Constructors ---

    public HIPClusterEvent() {}

    public HIPClusterEvent(String eventType,
                           String targetType,
                           String integrationName,
                           String integrationVersion,
                           String targetId,
                           Map<String, Object> payload) {
        this.eventType = eventType;
        this.targetType = targetType;
        this.integrationName = integrationName;
        this.integrationVersion = integrationVersion;
        this.targetId = targetId;
        this.payload = payload;
    }

    // --- Factory Methods ---

    public static HIPClusterEvent forAdapterTarget(String eventType, String integrationName, String integrationVersion, String adapterId, Map<String, Object> payload) {
        return new HIPClusterEvent(eventType, "ADAPTER", integrationName, integrationVersion, adapterId, payload);
    }

    public static HIPClusterEvent forHandlerTarget(String eventType, String integrationName, String integrationVersion, String handlerId, Map<String, Object> payload) {
        return new HIPClusterEvent(eventType, "HANDLER", integrationName, integrationVersion, handlerId, payload);
    }

    public static HIPClusterEvent forTarget(String eventType, String targetType, String integrationName, String integrationVersion, String targetId, Map<String, Object> payload) {
        return new HIPClusterEvent(eventType, targetType, integrationName, integrationVersion, targetId, payload);
    }

    // --- Target Checking Methods ---

    public boolean isTargetFor(String integrationName, String integrationVersion, String id, String expectedTargetType) {
        return (Objects.equals(this.integrationName, integrationName) || "*".equals(this.integrationName))
                && (Objects.equals(this.integrationVersion, integrationVersion) || "*".equals(this.integrationVersion))
                && (Objects.equals(this.targetId, id) || "*".equals(this.targetId))
                && (Objects.equals(this.targetType, expectedTargetType));
    }

    public boolean isAdapterTarget() {
        return "ADAPTER".equalsIgnoreCase(this.targetType);
    }

    public boolean isHandlerTarget() {
        return "HANDLER".equalsIgnoreCase(this.targetType);
    }

    // --- Getters and Setters ---

    public String getEventType() {
        return eventType;
    }
    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getTargetType() {
        return targetType;
    }
    public void setTargetType(String targetType) {
        this.targetType = targetType;
    }

    public String getIntegrationName() {
        return integrationName;
    }
    public void setIntegrationName(String integrationName) {
        this.integrationName = integrationName;
    }

    public String getIntegrationVersion() {
        return integrationVersion;
    }
    public void setIntegrationVersion(String integrationVersion) {
        this.integrationVersion = integrationVersion;
    }

    public String getTargetId() {
        return targetId;
    }
    public void setTargetId(String targetId) {
        this.targetId = targetId;
    }

    public Map<String, Object> getPayload() {
        return payload;
    }
    public void setPayload(Map<String, Object> payload) {
        this.payload = payload;
    }

    // --- Event Filtering Utility (Optional) ---

    public boolean matches(String eventType, String integrationName, String integrationVersion, String id, String expectedTargetType) {
        return (eventType == null || Objects.equals(this.eventType, eventType))
                && isTargetFor(integrationName, integrationVersion, id, expectedTargetType);
    }

    @Override
    public String toString() {
        return "HIPClusterEvent{" +
                "eventType='" + eventType + '\'' +
                ", targetType='" + targetType + '\'' +
                ", integrationName='" + integrationName + '\'' +
                ", integrationVersion='" + integrationVersion + '\'' +
                ", targetId='" + targetId + '\'' +
                ", payload=" + payload +
                '}';
    }

	public void setAdapterTarget(boolean adapterTarget) {
		this.adapterTarget = adapterTarget;
	}

	public void setHandlerTarget(boolean handlerTarget) {
		this.handlerTarget = handlerTarget;
	}
}