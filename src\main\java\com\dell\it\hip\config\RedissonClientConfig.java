package com.dell.it.hip.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RedissonClientConfig {
	
	@Value("${spring.data.redis.url}") 
	private String redisUrl;
	
	@Bean
    public RedissonClient redissonClient(Config config) {
		RedissonClient redissonClient = Redisson.create(config); 
        return redissonClient;
    }
	
	@Bean
    public Config config() {
		Config config = new Config();
		config.useSingleServer().setPassword("sFjIvYHMJHcv")
		.setKeepAlive(true)
		.setAddress(redisUrl);
        return config;
    }

}
