package com.dell.it.hip.util;

import java.io.File;
import java.security.KeyPair;
import java.util.concurrent.TimeUnit;

import org.apache.sshd.client.SshClient;
import org.apache.sshd.client.session.ClientSession;
import org.apache.sshd.common.config.keys.FilePasswordProvider;
import org.apache.sshd.common.keyprovider.FileKeyPairProvider;

import com.dell.it.hip.config.adapters.DynamicSFTPAdapterConfig;

public class SftpUtil {

    public static ClientSession buildClientSession(DynamicSFTPAdapterConfig cfg, SshClient client) throws Exception {
        // Connect
        ClientSession session = client.connect(cfg.getUsername(), cfg.getHost(), cfg.getPort() != null ? cfg.getPort() : 22)
                .verify(20, TimeUnit.SECONDS)
                .getSession();

        // Key-based authentication
        if (cfg.getPrivateKey() != null && !cfg.getPrivateKey().isEmpty()) {
            File privateKeyFile = new File(cfg.getPrivateKey());
            FileKeyPairProvider keyPairProvider = new FileKeyPairProvider(privateKeyFile.toPath());
            if (cfg.getPrivateKeyPassphrase() != null && !cfg.getPrivateKeyPassphrase().isEmpty()) {
                keyPairProvider.setPasswordFinder(
                        FilePasswordProvider.of(cfg.getPrivateKeyPassphrase())
                );
            }
            Iterable<KeyPair> keyPairs = keyPairProvider.loadKeys(null);
            boolean added = false;
            for (KeyPair keyPair : keyPairs) {
                session.addPublicKeyIdentity(keyPair);
                added = true;
            }
            if (!added) {
                throw new IllegalArgumentException("No valid private key found at: " + cfg.getPrivateKey());
            }
        } else {
            // Password auth fallback
            session.addPasswordIdentity(cfg.getPassword());
        }

        // Auth
        session.auth().verify(20, TimeUnit.SECONDS);
        return session;
    }
}