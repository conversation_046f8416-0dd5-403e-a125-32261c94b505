package com.dell.it.hip.config.FlowSteps;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SplitFlowConfig extends DocTypeConfig {
	
    private String action;
    private String x12SplitLevel; // e.g. "interchange", "group", "transaction"
    private String x12SegmentDelimiter;
    private String x12ElementDelimiter;
    private String x12SubElementDelimiter;
    private boolean allowMultipleInterchanges = false;
    
    // ==== EDI EDIFACT options ====
    private String edifactSplitLevel; // e.g. "interchange", "message"
    private String edifactSegmentDelimiter;
    private String edifactElementDelimiter;
    private String edifactSubElementDelimiter;
    private boolean allowMultipleEdifactInterchanges = false;

    // ==== XML options ====
	private String xmlXPathExpression;
    
	// ==== CSV options ====
    private boolean splitCsvLines = false;
    
    // ==== JSON options ====
    private String jsonPathExpression;
    
    // ==== FLAT options ====   
    private String flatFileExpression;
    
	// ==== default options ====
    private String regexExpression;
    
    private String splitterRegex;
    
    



}
