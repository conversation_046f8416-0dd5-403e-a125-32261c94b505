package com.dell.it.hip.config.FlowSteps;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DocTypeConfig {
	@JsonProperty("documentTypeName")
    private String name;
	@JsonProperty("documentTypeVersion")
    private String version;
	private int documentTypeId;
    private DocTypeRuleOperator docTypeRuleOperator = DocTypeRuleOperator.ALL; // ALL or ANY
    private String dataFormat;
    private String action;
    private List<DocTypeIdentifier> docTypeIdentifiers;
    /*private EdiX12SplitterConfig ediX12SplitterConfig;
    private XmlSplitterConfig xmlSplitterConfig;
    private JsonSplitterConfig jsonSplitterConfig;
    private CsvSplitterConfig csvSplitterConfig;
    private XsdSplitterConfig xsdSplitterConfig;*/
    // ==== EDI X12 options ====
    private String x12SplitLevel; // e.g. "interchange", "group", "transaction"
    private String x12SegmentDelimiter;
    private String x12ElementDelimiter;
    private String x12SubElementDelimiter;
    private boolean allowMultipleInterchanges = false;
    
    // ==== EDI EDIFACT options ====
    private String edifactSplitLevel; // e.g. "interchange", "message"
    private String edifactSegmentDelimiter;
    private String edifactElementDelimiter;
    private String edifactSubElementDelimiter;
    private boolean allowMultipleEdifactInterchanges = false;

    // ==== XML options ====
	private String xmlXPathExpression;
    
	// ==== CSV options ====
    private boolean splitCsvLines = false;
    
    // ==== JSON options ====
    private String jsonPathExpression;
    
    // ==== FLAT options ====   
    private String flatFileExpression;
    
	// ==== default options ====
    private String regexExpression;


    private ValidationConfig validation;
    @JsonProperty("attributeMappings")
    private List<AttributeMapping> attributeMappings = new ArrayList<>();

    /*@JsonSetter("attributeMappings")
    public void setAttributeMappings(Object attributeMappings) {
        if (attributeMappings instanceof Map) {
            this.attributeMappings = (Map<String, AttributeMapping>) attributeMappings;
        } else if (attributeMappings instanceof List) {
            List<AttributeMapping> list = (List<AttributeMapping>) attributeMappings;
            this.attributeMappings = list.stream()
                    .collect(Collectors.toMap(AttributeMapping::getAttributeName, Function.identity()));
        }
    }*/
    public List<AttributeMapping> getAttributeMappings() { return attributeMappings; }
    // Getters & Setters
    // ...
}