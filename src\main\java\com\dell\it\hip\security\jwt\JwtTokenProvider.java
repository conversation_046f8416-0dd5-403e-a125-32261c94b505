package com.dell.it.hip.security.jwt;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.crypto.spec.SecretKeySpec;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

/**
 * JWT Token Provider for generating and validating JWT tokens.
 * Note: This is a simplified implementation. In production, consider using
 * a proper JWT library like jjwt when dependencies are available.
 */
@Component
public class JwtTokenProvider {

    private static final Logger logger = LoggerFactory.getLogger(JwtTokenProvider.class);

    @Value("${hip.jwt.secret:hipSecretKey123456789012345678901234567890}")
    private String jwtSecret;

    @Value("${hip.jwt.expiration:86400000}") // 24 hours
    private int jwtExpirationInMs;

    private Key getSigningKey() {
        byte[] keyBytes = jwtSecret.getBytes(StandardCharsets.UTF_8);
        return new SecretKeySpec(keyBytes, "HmacSHA256");
    }

    public String generateToken(Authentication authentication) {
        UserDetails userPrincipal = (UserDetails) authentication.getPrincipal();
        return generateTokenFromUsername(userPrincipal.getUsername());
    }

    public String generateTokenFromUsername(String username) {
        Date expiryDate = new Date(System.currentTimeMillis() + jwtExpirationInMs);
        
        // Simple JWT implementation - in production use proper JWT library
        Map<String, Object> claims = new HashMap<>();
        claims.put("sub", username);
        claims.put("iat", System.currentTimeMillis() / 1000);
        claims.put("exp", expiryDate.getTime() / 1000);
        
        return createSimpleJWT(claims);
    }

    public String getUsernameFromToken(String token) {
        try {
            Map<String, Object> claims = parseSimpleJWT(token);
            return (String) claims.get("sub");
        } catch (Exception e) {
            logger.error("Error extracting username from token", e);
            return null;
        }
    }

    public boolean validateToken(String authToken) {
        try {
            Map<String, Object> claims = parseSimpleJWT(authToken);
            Long exp = (Long) claims.get("exp");
            return exp != null && exp > (System.currentTimeMillis() / 1000);
        } catch (Exception e) {
            logger.error("Invalid JWT token", e);
        }
        return false;
    }

    // Simplified JWT implementation - replace with proper library in production
    private String createSimpleJWT(Map<String, Object> claims) {
        // This is a simplified implementation for demonstration
        // In production, use a proper JWT library
        StringBuilder jwt = new StringBuilder();
        jwt.append("eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9."); // Header
        
        // Payload (base64 encoded claims)
        String payload = java.util.Base64.getEncoder().encodeToString(
            claims.toString().getBytes(StandardCharsets.UTF_8));
        jwt.append(payload).append(".");
        
        // Signature (simplified)
        String signature = java.util.Base64.getEncoder().encodeToString(
            (payload + jwtSecret).getBytes(StandardCharsets.UTF_8));
        jwt.append(signature);
        
        return jwt.toString();
    }

    private Map<String, Object> parseSimpleJWT(String token) {
        // Simplified parsing - in production use proper JWT library
        String[] parts = token.split("\\.");
        if (parts.length != 3) {
            throw new IllegalArgumentException("Invalid JWT token format");
        }
        
        String payload = new String(java.util.Base64.getDecoder().decode(parts[1]), StandardCharsets.UTF_8);
        
        // Simple parsing - in production use proper JSON parsing
        Map<String, Object> claims = new HashMap<>();
        if (payload.contains("sub=")) {
            String sub = payload.substring(payload.indexOf("sub=") + 4);
            sub = sub.substring(0, sub.indexOf(",") > 0 ? sub.indexOf(",") : sub.indexOf("}"));
            claims.put("sub", sub);
        }
        if (payload.contains("exp=")) {
            String exp = payload.substring(payload.indexOf("exp=") + 4);
            exp = exp.substring(0, exp.indexOf(",") > 0 ? exp.indexOf(",") : exp.indexOf("}"));
            try {
                claims.put("exp", Long.parseLong(exp));
            } catch (NumberFormatException e) {
                claims.put("exp", System.currentTimeMillis() / 1000 + 3600); // 1 hour default
            }
        }
        
        return claims;
    }
}
