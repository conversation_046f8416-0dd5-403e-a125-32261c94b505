package com.dell.it.hip.util.redis;

import java.util.List;
import org.springframework.messaging.Message;
import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.DedupFlowStepConfig;
import com.dell.it.hip.config.FlowSteps.DocTypeConfig;

public class DedupKeyUtil {
    public static String buildDedupKey(HIPIntegrationDefinition def, DedupFlowStepConfig.DocTypeDedupConfig config, Message<?> message) {
        StringBuilder key = new StringBuilder();
        key.append(def.getServiceManagerName()).append(":")
                .append(def.getHipIntegrationName()).append(":")
                .append(def.getVersion())
                .append(config.getDocumentTypeId())
                .append(":dedup:");
        List<String> fields = config.getHeadersForKey();
        if (fields != null) {
            for (String f : fields) {
                Object val = message.getHeaders().getOrDefault(f, "NULL");
                key.append(val).append(":");
            }
        }
        return key.toString();
    }
}