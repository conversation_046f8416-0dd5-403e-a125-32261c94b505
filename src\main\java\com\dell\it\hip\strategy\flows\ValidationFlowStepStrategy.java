package com.dell.it.hip.strategy.flows;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.FlowSteps.FlowStepConfigRef;
import com.dell.it.hip.config.FlowSteps.ValidationFlowStepConfig;
import com.dell.it.hip.util.logging.TransactionLoggingUtil;
import com.dell.it.hip.util.logging.WiretapService;
import com.dell.it.hip.util.validation.MessageFormatDetector;
import com.dell.it.hip.util.validation.SchemaValidator;
import com.dell.it.hip.util.validation.StructuralValidator;

@Component("validation")
public class ValidationFlowStepStrategy extends AbstractFlowStepStrategy {
	Logger log = LoggerFactory.getLogger(ValidationFlowStepStrategy.class);
	@Autowired
	private TransactionLoggingUtil transactionLoggingUtil;

	@Autowired
	private WiretapService wiretapService;

	@Autowired
	private SchemaValidator schemaValidator;

	@Override
	public String getType() {
		return "validation";
	}

	@Override
	protected List<Message<?>> doExecute(Message<?> message, FlowStepConfigRef stepConfigRef,
			HIPIntegrationDefinition def) throws Exception {
		ValidationFlowStepConfig config = (ValidationFlowStepConfig) def.getConfigMap()
				.get(stepConfigRef.getPropertyRef());
		if (config == null) {
			throw new IllegalStateException("No Validation config for: " + stepConfigRef.getPropertyRef());
		}

		String payload = Objects.toString(message.getPayload(), "");
		String format = (String) message.getHeaders().get("HIP.payload.dataformat");
		if (format == null) {
			format = MessageFormatDetector.detect(payload);
		}
		if (!"Flat_File".equalsIgnoreCase(format)) {
			// === Structural Validation ===
			boolean structureOk = StructuralValidator.validate(payload, format);
			if (!structureOk) {
				String errMsg = "Structural validation failed for format: " + format;
				wiretapService.tap(message, def, stepConfigRef, "error", errMsg);
				transactionLoggingUtil.logError(message, def, stepConfigRef, "StructuralValidationFailure", errMsg);
				return Collections.emptyList();
			}

			// === Schema Validation (optional) ===
			if (config.isEnableSchemaValidation()) {
				String schemaKey = config.getSchemaKey();
				try {
					boolean valid = schemaValidator.validate(payload, format, schemaKey);
					if (!valid) {
						String errMsg = "Schema validation failed for format: " + format + " using Redis key: "
								+ schemaKey;
						wiretapService.tap(message, def, stepConfigRef, "error", errMsg);
						transactionLoggingUtil.logError(message, def, stepConfigRef, "SchemaValidationFailure", errMsg);
						return Collections.emptyList();
					}
				} catch (Exception ex) {
					String errMsg = "Schema validation error for format: " + format + " using Redis key: " + schemaKey
							+ ", " + ex.getMessage();
					wiretapService.tap(message, def, stepConfigRef, "error", errMsg);
					transactionLoggingUtil.logError(message, def, stepConfigRef, "SchemaValidationFailure", errMsg);
					return Collections.emptyList();
				}
			}
		}

		// === Add the detected format to header so downstream can skip rechecking ===
		Message<?> enriched = MessageBuilder.fromMessage(message).setHeader("HIP.payload.dataformat", format).build();

		String infoMsg = "Validation passed for format: " + format;
		wiretapService.tap(enriched, def, stepConfigRef, "info", infoMsg);
		transactionLoggingUtil.logInfo(enriched, def, stepConfigRef, infoMsg);

		// === Success: pass message through ===
		return Collections.singletonList(message);
	}
}