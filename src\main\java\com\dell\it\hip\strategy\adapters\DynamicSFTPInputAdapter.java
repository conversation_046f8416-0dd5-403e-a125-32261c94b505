package com.dell.it.hip.strategy.adapters;
import java.io.ByteArrayOutputStream;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;

import org.apache.sshd.client.SshClient;
import org.apache.sshd.client.session.ClientSession;
import org.apache.sshd.sftp.client.SftpClient;
import org.apache.sshd.sftp.client.SftpClient.Attributes;
import org.apache.sshd.sftp.client.SftpClient.DirEntry;
import org.apache.sshd.sftp.client.SftpClientFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.adapters.AdapterConfigRef;
import com.dell.it.hip.config.adapters.DynamicSFTPAdapterConfig;
import com.dell.it.hip.core.HIPClusterCoordinationService;
import com.dell.it.hip.util.CompressionUtil;
import com.dell.it.hip.util.OpenTelemetryPropagationUtil;
import com.dell.it.hip.util.SftpUtil;
import com.dell.it.hip.util.ThrottleSettings;
import com.dell.it.hip.util.logging.WiretapService;
import com.dell.it.hip.util.redis.SFTPFileLockManager;

import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.SpanKind;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Scope;

@Component("sftpAdapter")
public class DynamicSFTPInputAdapter extends AbstractDynamicInputAdapter {

    private static final Logger logger = LoggerFactory.getLogger(DynamicSFTPInputAdapter.class);

    @Autowired private SFTPFileLockManager sftpFileLockManager;
    @Autowired private ThreadPoolTaskExecutor taskExecutor;
    @Autowired(required = false) private HIPClusterCoordinationService clusterCoordinationService;
    @Autowired private WiretapService wiretapService;
    @Autowired private OpenTelemetryPropagationUtil openTelemetryPropagationUtil;

    private final Map<String, Future<?>> pollingTasks = new ConcurrentHashMap<>();
    private final Map<String, AtomicBoolean> pausedMap = new ConcurrentHashMap<>();
    private final Set<String> forcePollSet = ConcurrentHashMap.newKeySet();

    private final String nodeId = UUID.randomUUID().toString();
    private String getNodeId() { return nodeId; }

    @Override
    public String getType() { return "sftpAdapter"; }
    
    @Autowired
    private Tracer tracer;

    @Override
    public void buildProducer(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        if (!getType().equals(ref.getType())) return;
        DynamicSFTPAdapterConfig cfg = (DynamicSFTPAdapterConfig) def.getConfigMap().get(ref.getPropertyRef());
        if (cfg == null)
            throw new IllegalStateException("No config found for SFTP adapter ref: " + ref.getPropertyRef());

        String adapterKey = key(def, ref);

        pausedMap.put(adapterKey, new AtomicBoolean(false));
        Runnable poller = () -> pollLoop(def, ref, cfg, adapterKey);
        Future<?> future = taskExecutor.submit(poller);
        registerAdapterInstance(def, ref, new SFTPAdapterInstance(adapterKey, future));
        pollingTasks.put(adapterKey, future);

        logger.info("Started SFTP poller for {}", adapterKey);
    }

    private void pollLoop(HIPIntegrationDefinition def, AdapterConfigRef ref, DynamicSFTPAdapterConfig cfg, String adapterKey) {
        long lastPollTime = 0;
        while (!Thread.currentThread().isInterrupted()) {
            try {
                if (pausedMap.getOrDefault(adapterKey, new AtomicBoolean(false)).get()) {
                    logger.info("SFTP adapter paused: {}", adapterKey);
                    Thread.sleep(2000L);
                    continue;
                }

                boolean forcePoll = isForcePollRequested(def, ref);
                long now = System.currentTimeMillis();
                boolean intervalDue = (now - lastPollTime) >= (cfg.getPollingIntervalMs() != null ? cfg.getPollingIntervalMs() : 60000L);

                if (forcePoll || intervalDue) {
                    pollSftpForFiles(def, ref, cfg);
                    clearForcePoll(def, ref);
                    lastPollTime = now;
                }

                if (!forcePoll) {
                    Thread.sleep(cfg.getPollingIntervalMs() != null ? cfg.getPollingIntervalMs() : 60000L);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                logger.info("SFTP poller thread interrupted for {}", adapterKey);
                break;
            } catch (Exception ex) {
                logger.error("Exception in SFTP polling for {}: {}", adapterKey, ex.getMessage(), ex);
            }
        }
        logger.info("SFTP poller loop exited for {}", adapterKey);
    }

    private void pollSftpForFiles(HIPIntegrationDefinition def, AdapterConfigRef ref, DynamicSFTPAdapterConfig cfg) {
        String remoteDir = cfg.getRemoteDirectory();
        String filePattern = cfg.getFileNamePattern();
        String charset = cfg.getCharset() != null ? cfg.getCharset() : "UTF-8";
        SshClient client = SshClient.setUpDefaultClient();
        client.start();

        try (ClientSession session = SftpUtil.buildClientSession(cfg, client);
             SftpClient sftp = SftpClientFactory.instance().createSftpClient(session)) {

            Iterable<DirEntry> files = sftp.readDir(remoteDir);
            for (DirEntry entry : files) {
                String fileName = entry.getFilename();
                if (".".equals(fileName) || "..".equals(fileName)) continue;
                if (filePattern != null && !fileName.matches(filePattern)) continue;

                boolean locked = sftpFileLockManager.acquireLock(
                        def.getHipIntegrationName(), def.getVersion(), ref.getId(), fileName, getNodeId(), 600
                );
                if (!locked) continue;

                SftpClient.Handle handle = null;
                
             // 1. Start a new span since SFTP is always a source (no upstream tracecontext)
                Span span = tracer.spanBuilder("sftp.receive")
                        .setSpanKind(SpanKind.CONSUMER)
                        .startSpan();
                try(Scope scope = span.makeCurrent()) {
                    // --- THROTTLE: Only process if not over limit ---
                    ThrottleSettings settings = getThrottleSettings(def, ref);
                    boolean allowed = throttlingService.tryConsumeToken(
                            serviceManagerName,
                            def.getHipIntegrationName(),
                            def.getVersion(),
                            ref.getId(),
                            settings
                    );
                    if (!allowed) {
                        logger.info("SFTP throttle: skipping file {} (integration {}) due to rate limit", fileName, def.getHipIntegrationName());
                        // Just skip for now, will retry next poll
                        continue;
                    }

                    handle = sftp.open(remoteDir + "/" + fileName, EnumSet.of(SftpClient.OpenMode.Read));
                    ByteArrayOutputStream out = new ByteArrayOutputStream();
                    byte[] buf = new byte[8192];
                    int read;
                    long offset = 0;
                    while ((read = sftp.read(handle, offset, buf, 0, buf.length)) > 0) {
                        out.write(buf, 0, read);
                        offset += read;
                    }
                    byte[] fileContent = out.toByteArray();

                    if (cfg.isCompressed()) fileContent = CompressionUtil.decompress(fileContent);

                    Message<?> msg = buildMessage(def, ref, fileName, fileContent, cfg, entry, charset);
                    
                    logger.debug("SFTP tracing span started: traceId={}, spanId={}", 
                            span.getSpanContext().getTraceId(), span.getSpanContext().getSpanId());

                    // Inject OTel context if not present
                    Message<?> msgWithTrace = openTelemetryPropagationUtil.injectTraceContext(msg);

                    // --- WIRETAP: started event as message enters HIP flow ---
                    wiretapService.tap(
                            msgWithTrace,
                            def,
                            ref,
                            "started",
                            "HIPIntegration received file from SFTP: " + fileName
                    );

                    // Use correct inputChannel always
                    processInboundMessage(def, ref, msgWithTrace, getInputChannel(def));
                    handlePostProcess(sftp, cfg, remoteDir, fileName);

                } catch (Exception ex) {
                    logger.error("Failed to process SFTP file {}", fileName, ex);
                    wiretapService.tap(null, def, ref, "error", "Failed to process SFTP file: " + fileName);
                } finally {
                	span.end();
                    if (handle != null) {
                        try { sftp.close(handle); } catch (Exception e) { /* log or ignore */ }
                    }
                    sftpFileLockManager.releaseLock(def.getHipIntegrationName(), def.getVersion(), ref.getId(), fileName);
                }
            }
        } catch (Exception ex) {
            logger.error("Exception in SFTP connection/listing for {}: {}", remoteDir, ex.getMessage(), ex);
        } finally {
            client.stop();
        }
    }

    private Message<?> buildMessage(
            HIPIntegrationDefinition def, AdapterConfigRef ref, String fileName, byte[] fileContent,
            DynamicSFTPAdapterConfig cfg, DirEntry entry, String charset
    ) {
        MessageBuilder<byte[]> mb = MessageBuilder.withPayload(fileContent);

        Map<String, Object> sftpHeaders = new HashMap<>();
        sftpHeaders.put("file_name", fileName);
        sftpHeaders.put("charset", charset);
        if (entry != null) {
            Attributes attrs = entry.getAttributes();
            sftpHeaders.put("file_size", attrs.getSize());
            sftpHeaders.put("file_mod_time", attrs.getModifyTime().toMillis());
            sftpHeaders.put("file_permissions", attrs.getPermissions());
            sftpHeaders.put("file_is_dir", attrs.isDirectory());
        }

        if (cfg.getHeadersToExtract() != null && !cfg.getHeadersToExtract().isEmpty()) {
            Map<String, Object> filtered = new HashMap<>();
            for (String key : cfg.getHeadersToExtract()) {
                if (sftpHeaders.containsKey(key)) filtered.put(key, sftpHeaders.get(key));
            }
            sftpHeaders = filtered;
        }

        mb.setHeader("hip.adapter.sftp", sftpHeaders);
        return mb.build();
    }

    private void handlePostProcess(SftpClient sftp, DynamicSFTPAdapterConfig cfg, String remoteDir, String fileName) {
        try {
            if ("delete".equalsIgnoreCase(cfg.getPostProcessAction())) {
                sftp.remove(remoteDir + "/" + fileName);
                logger.info("Deleted SFTP file: {}", fileName);
            } else if ("rename".equalsIgnoreCase(cfg.getPostProcessAction())) {
                String newFileName = (cfg.getRenamePattern() != null)
                        ? cfg.getRenamePattern().replace("{file}", fileName)
                        : fileName + ".processed";
                sftp.rename(remoteDir + "/" + fileName, remoteDir + "/" + newFileName);
                logger.info("Renamed SFTP file: {} -> {}", fileName, newFileName);
            }
        } catch (Exception ex) {
            logger.warn("Post-process failed for file {}: {}", fileName, ex.getMessage());
        }
    }

    @Override
    protected Message<?> toMessage(HIPIntegrationDefinition def, AdapterConfigRef ref, Object raw) {
        if (raw instanceof Message<?> msg) return msg;
        return MessageBuilder.withPayload(raw).build();
    }

    // --- Force Poll (for SFTP callback support) ---
    public void triggerForcePoll(String hipIntegrationName, String version, String adapterId) {
        forcePollSet.add(forcePollKey(hipIntegrationName, version, adapterId));
        logger.info("Force poll triggered for SFTP: {}:{}:{}", hipIntegrationName, version, adapterId);
    }

    private boolean isForcePollRequested(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        return forcePollSet.contains(forcePollKey(def, ref));
    }

    private void clearForcePoll(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        forcePollSet.remove(forcePollKey(def, ref));
    }

    private String forcePollKey(HIPIntegrationDefinition def, AdapterConfigRef ref) {
        return forcePollKey(def.getHipIntegrationName(), def.getVersion(), ref.getId());
    }

    private String forcePollKey(String hipIntegrationName, String version, String adapterId) {
        return hipIntegrationName + ":" + version + ":" + adapterId;
    }

    @Override
    protected void shutdownAdapterInstance(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        if (instance instanceof SFTPAdapterInstance sftpInstance) {
            if (sftpInstance.pollerTask != null && !sftpInstance.pollerTask.isDone()) {
                sftpInstance.pollerTask.cancel(true);
            }
            logger.info("SFTP polling stopped for {}", sftpInstance.adapterKey);
            pausedMap.remove(sftpInstance.adapterKey);
            pollingTasks.remove(sftpInstance.adapterKey);
        }
    }

    @Override
    protected void startAdapterInstance(AdapterInstance instance) {
        // No-op: polling starts on buildProducer; can be enhanced if needed
    }

    @Override
    protected void stopAdapterInstance(AdapterInstance instance) {
        shutdownAdapterInstance(null, null, instance);
    }

    @Override
    protected void doPause(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        String adapterKey = key(def, ref);
        pausedMap.computeIfAbsent(adapterKey, k -> new AtomicBoolean(false)).set(true);
        if (clusterCoordinationService != null) clusterCoordinationService.pause(def, ref);
        if (instance instanceof SFTPAdapterInstance sftpInst) {
            if (sftpInst.pollerTask != null && !sftpInst.pollerTask.isDone()) {
                sftpInst.pollerTask.cancel(true);
            }
            logger.info("SFTP polling paused for {}", adapterKey);
        }
    }

    @Override
    protected void doResume(HIPIntegrationDefinition def, AdapterConfigRef ref, AdapterInstance instance) {
        String adapterKey = key(def, ref);
        pausedMap.computeIfAbsent(adapterKey, k -> new AtomicBoolean(false)).set(false);

        DynamicSFTPAdapterConfig cfg = (DynamicSFTPAdapterConfig) def.getConfigMap().get(ref.getPropertyRef());
        Runnable poller = () -> pollLoop(def, ref, cfg, adapterKey);
        Future<?> future = taskExecutor.submit(poller);
        registerAdapterInstance(def, ref, new SFTPAdapterInstance(adapterKey, future));
        pollingTasks.put(adapterKey, future);

        if (clusterCoordinationService != null) clusterCoordinationService.resume(def, ref);

        logger.info("SFTP polling resumed for {}", adapterKey);
    }

    public static class SFTPAdapterInstance extends AdapterInstance {
        final String adapterKey;
        final Future<?> pollerTask;
        public SFTPAdapterInstance(String adapterKey, Future<?> pollerTask) {
            this.adapterKey = adapterKey;
            this.pollerTask = pollerTask;
        }
    }
}