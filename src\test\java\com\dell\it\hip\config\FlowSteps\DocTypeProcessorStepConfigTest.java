package com.dell.it.hip.config.FlowSteps;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class DocTypeProcessorStepConfigTest {

    @Test
    public void testDotNotationDeserialization() throws Exception {
        // JSON with dot notation and array indices - the format we need to support
        String json = "{\n"
                + "  \"supportedDocTypesPerFormat.JSON[0]\": \"testdoc:1\",\n"
                + "  \"supportedDocTypesPerFormat.JSON[1]\": \"testdoce1:1\",\n"
                + "  \"supportedDocTypesPerFormat.XML[0]\": \"xmldoc:1\",\n"
                + "  \"genericDocType\": \"GENERIC\",\n"
                + "  \"allowGenericDocType\": true,\n"
                + "  \"terminateOnUnknownFormat\": false\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();
        
        // Test deserialization
        DocTypeProcessorStepConfig config = objectMapper.readValue(json, DocTypeProcessorStepConfig.class);
        
        // Verify the deserialization worked correctly
        assertNotNull(config);
        assertNotNull(config.getSupportedDocTypesPerFormat());
        
        // Check JSON format mapping
        assertTrue(config.getSupportedDocTypesPerFormat().containsKey("JSON"));
        assertEquals(2, config.getSupportedDocTypesPerFormat().get("JSON").size());
        assertEquals("testdoc:1", config.getSupportedDocTypesPerFormat().get("JSON").get(0));
        assertEquals("testdoce1:1", config.getSupportedDocTypesPerFormat().get("JSON").get(1));
        
        // Check XML format mapping
        assertTrue(config.getSupportedDocTypesPerFormat().containsKey("XML"));
        assertEquals(1, config.getSupportedDocTypesPerFormat().get("XML").size());
        assertEquals("xmldoc:1", config.getSupportedDocTypesPerFormat().get("XML").get(0));
        
        // Check other properties
        assertEquals("GENERIC", config.getGenericDocType());
        assertTrue(config.isAllowGenericDocType());
        assertFalse(config.isTerminateOnUnknownFormat());
        
        System.out.println("✅ Successfully deserialized dot notation JSON!");
        System.out.println("Supported doc types for JSON: " + config.getSupportedDocTypesPerFormat().get("JSON"));
        System.out.println("Supported doc types for XML: " + config.getSupportedDocTypesPerFormat().get("XML"));
    }

    @Test
    public void testOriginalHIPIntegrationMapperJson() throws Exception {
        // This is the exact JSON from HIPIntegrationMapper.java
        String json = "{\r\n"
                + "        \"supportedDocTypesPerFormat.JSON[0]\": \"testdoc:1\",\r\n"
                + "        \"supportedDocTypesPerFormat.JSON[1]\": \"testdoce1:1\"\r\n"
                + "      }";

        ObjectMapper objectMapper = new ObjectMapper();
        
        // Test deserialization - this should now work without UnrecognizedPropertyException
        DocTypeProcessorStepConfig config = objectMapper.readValue(json, DocTypeProcessorStepConfig.class);
        
        // Verify the deserialization worked correctly
        assertNotNull(config);
        assertNotNull(config.getSupportedDocTypesPerFormat());
        assertTrue(config.getSupportedDocTypesPerFormat().containsKey("JSON"));
        assertEquals(2, config.getSupportedDocTypesPerFormat().get("JSON").size());
        assertEquals("testdoc:1", config.getSupportedDocTypesPerFormat().get("JSON").get(0));
        assertEquals("testdoce1:1", config.getSupportedDocTypesPerFormat().get("JSON").get(1));
        
        System.out.println("✅ HIPIntegrationMapper JSON format works!");
        System.out.println("Config: " + config);
    }

    @Test
    public void testAttributeMappingsListDeserialization() throws Exception {
        // JSON with attributeMappings as an array (List<AttributeMapping>)
        String json = "{\n"
                + "  \"supportedDocTypesPerFormat.JSON[0]\": \"testdoc:1\",\n"
                + "  \"supportedDocTypesPerFormat.JSON[1]\": \"testdoce1:1\",\n"
                + "  \"genericDocType\": \"GENERIC\",\n"
                + "  \"allowGenericDocType\": true,\n"
                + "  \"terminateOnUnknownFormat\": false,\n"
                + "  \"attributeMappings\": [\n"
                + "    {\n"
                + "      \"attributeName\": \"docId\",\n"
                + "      \"derivedFrom\": \"HEADER\",\n"
                + "      \"expression\": \"$.documentId\"\n"
                + "    },\n"
                + "    {\n"
                + "      \"attributeName\": \"sender\",\n"
                + "      \"derivedFrom\": \"PAYLOAD\",\n"
                + "      \"expression\": \"$.sender.id\"\n"
                + "    }\n"
                + "  ]\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();

        // Test deserialization
        DocTypeProcessorStepConfig config = objectMapper.readValue(json, DocTypeProcessorStepConfig.class);

        // Verify the deserialization worked correctly
        assertNotNull(config);
        assertNotNull(config.getSupportedDocTypesPerFormat());
        assertNotNull(config.getAttributeMappings());

        // Check supportedDocTypesPerFormat (dot notation)
        assertTrue(config.getSupportedDocTypesPerFormat().containsKey("JSON"));
        assertEquals(2, config.getSupportedDocTypesPerFormat().get("JSON").size());
        assertEquals("testdoc:1", config.getSupportedDocTypesPerFormat().get("JSON").get(0));
        assertEquals("testdoce1:1", config.getSupportedDocTypesPerFormat().get("JSON").get(1));

        // Check attributeMappings (List<AttributeMapping>)
        assertEquals(2, config.getAttributeMappings().size());

        AttributeMapping firstMapping = config.getAttributeMappings().get(0);
        assertEquals("docId", firstMapping.getAttributeName());
        assertEquals("HEADER", firstMapping.getDerivedFrom());
        assertEquals("$.documentId", firstMapping.getExpression());

        AttributeMapping secondMapping = config.getAttributeMappings().get(1);
        assertEquals("sender", secondMapping.getAttributeName());
        assertEquals("PAYLOAD", secondMapping.getDerivedFrom());
        assertEquals("$.sender.id", secondMapping.getExpression());

        // Check other properties
        assertEquals("GENERIC", config.getGenericDocType());
        assertTrue(config.isAllowGenericDocType());
        assertFalse(config.isTerminateOnUnknownFormat());

        System.out.println("✅ Successfully deserialized JSON with List<AttributeMapping>!");
        System.out.println("Attribute mappings: " + config.getAttributeMappings());
    }

    @Test
    public void testEmptyAttributeMappingsList() throws Exception {
        // JSON with empty attributeMappings array
        String json = "{\n"
                + "  \"supportedDocTypesPerFormat.JSON[0]\": \"testdoc:1\",\n"
                + "  \"genericDocType\": \"GENERIC\",\n"
                + "  \"attributeMappings\": []\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();

        // Test deserialization
        DocTypeProcessorStepConfig config = objectMapper.readValue(json, DocTypeProcessorStepConfig.class);

        // Verify the deserialization worked correctly
        assertNotNull(config);
        assertNotNull(config.getAttributeMappings());
        assertEquals(0, config.getAttributeMappings().size());

        System.out.println("✅ Successfully handled empty attributeMappings list!");
    }

    @Test
    public void testMissingAttributeMappings() throws Exception {
        // JSON without attributeMappings property
        String json = "{\n"
                + "  \"supportedDocTypesPerFormat.JSON[0]\": \"testdoc:1\",\n"
                + "  \"genericDocType\": \"GENERIC\"\n"
                + "}";

        ObjectMapper objectMapper = new ObjectMapper();

        // Test deserialization
        DocTypeProcessorStepConfig config = objectMapper.readValue(json, DocTypeProcessorStepConfig.class);

        // Verify the deserialization worked correctly
        assertNotNull(config);
        // attributeMappings should be null when not provided
        assertNull(config.getAttributeMappings());

        System.out.println("✅ Successfully handled missing attributeMappings!");
    }
}
