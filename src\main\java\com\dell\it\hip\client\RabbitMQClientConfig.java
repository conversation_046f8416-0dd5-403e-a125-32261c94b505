package com.dell.it.hip.client;

import org.springframework.amqp.core.AcknowledgeMode;
import java.util.List;
import java.util.Map;

/**
 * Configuration class for RabbitMQClient that mirrors the property structure
 * used in DynamicRabbitMQAdapterConfig and DynamicRabbitMQHandlerConfig.
 * 
 * This class can be used for both consumer (adapter testing) and producer (handler testing)
 * configurations by using the appropriate property prefixes.
 */
public class RabbitMQClientConfig {
    
    // Core RabbitMQ connection properties
    private String host;
    private Integer port;
    private String virtualHost;
    private String username;
    private String password;
    
    // Consumer properties
    private String queueName;
    private Integer concurrency;
    private Integer prefetchCount;
    private AcknowledgeMode acknowledgeMode;
    private Integer channelCacheSize;
    
    // Producer properties
    private String exchange;
    private String routingKey;
    private Boolean mandatory;
    private Boolean persistent;
    
    // Authentication and SSL
    private String authenticationType;
    private String sslTruststoreLocation;
    private String sslTruststorePassword;
    private String sslKeystoreLocation;
    private String sslKeystorePassword;
    private String sslKeyPassword;
    
    // Application-level
    private Boolean compressed;
    private Boolean gzipEnabled;
    private List<String> headersToExtract;
    private String messageConverterClass;
    private Map<String, Object> parameters;
    
    // Default constructor
    public RabbitMQClientConfig() {}
    
    // Builder pattern constructor
    private RabbitMQClientConfig(Builder builder) {
        this.host = builder.host;
        this.port = builder.port;
        this.virtualHost = builder.virtualHost;
        this.username = builder.username;
        this.password = builder.password;
        this.queueName = builder.queueName;
        this.concurrency = builder.concurrency;
        this.prefetchCount = builder.prefetchCount;
        this.acknowledgeMode = builder.acknowledgeMode;
        this.channelCacheSize = builder.channelCacheSize;
        this.exchange = builder.exchange;
        this.routingKey = builder.routingKey;
        this.mandatory = builder.mandatory;
        this.persistent = builder.persistent;
        this.authenticationType = builder.authenticationType;
        this.sslTruststoreLocation = builder.sslTruststoreLocation;
        this.sslTruststorePassword = builder.sslTruststorePassword;
        this.sslKeystoreLocation = builder.sslKeystoreLocation;
        this.sslKeystorePassword = builder.sslKeystorePassword;
        this.sslKeyPassword = builder.sslKeyPassword;
        this.compressed = builder.compressed;
        this.gzipEnabled = builder.gzipEnabled;
        this.headersToExtract = builder.headersToExtract;
        this.messageConverterClass = builder.messageConverterClass;
        this.parameters = builder.parameters;
    }
    
    /**
     * Create a consumer configuration with the provided test properties
     */
    public static RabbitMQClientConfig createConsumerConfig() {
        return new Builder()
            .host("paas-rmq-aic-dev01.us.dell.com")
            .port(8071)
            .virtualHost("AIC_ACTMON_DEV")
            .username("AMERICAS\\svc_npaicdvgsmrmqv1")
            .password("~94YzSH?*VbAUXMf_pICeJTL")
            .queueName("Test.Q")
            .authenticationType("TLS")
            .acknowledgeMode(AcknowledgeMode.AUTO)
            .prefetchCount(10)
            .concurrency(1)
            .compressed(false)
            .build();
    }
    
    /**
     * Create a producer configuration with the provided test properties
     */
    public static RabbitMQClientConfig createProducerConfig() {
        return new Builder()
            .host("paas-rmq-aic-dev01.us.dell.com")
            .port(8071)
            .virtualHost("AIC_ACTMON_DEV")
            .username("AMERICAS\\svc_npaicdvgsmrmqv1")
            .password("~94YzSH?*VbAUXMf_pICeJTL")
            .exchange("Test_Exg")
            .routingKey("test.routing.key")
            .authenticationType("TLS")
            .mandatory(false)
            .persistent(false)
            .gzipEnabled(false)
            .build();
    }
    
    // Getters and setters
    public String getHost() { return host; }
    public void setHost(String host) { this.host = host; }
    
    public Integer getPort() { return port; }
    public void setPort(Integer port) { this.port = port; }
    
    public String getVirtualHost() { return virtualHost; }
    public void setVirtualHost(String virtualHost) { this.virtualHost = virtualHost; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
    
    public String getQueueName() { return queueName; }
    public void setQueueName(String queueName) { this.queueName = queueName; }
    
    public Integer getConcurrency() { return concurrency; }
    public void setConcurrency(Integer concurrency) { this.concurrency = concurrency; }
    
    public Integer getPrefetchCount() { return prefetchCount; }
    public void setPrefetchCount(Integer prefetchCount) { this.prefetchCount = prefetchCount; }
    
    public AcknowledgeMode getAcknowledgeMode() { return acknowledgeMode; }
    public void setAcknowledgeMode(AcknowledgeMode acknowledgeMode) { this.acknowledgeMode = acknowledgeMode; }
    
    public Integer getChannelCacheSize() { return channelCacheSize; }
    public void setChannelCacheSize(Integer channelCacheSize) { this.channelCacheSize = channelCacheSize; }
    
    public String getExchange() { return exchange; }
    public void setExchange(String exchange) { this.exchange = exchange; }
    
    public String getRoutingKey() { return routingKey; }
    public void setRoutingKey(String routingKey) { this.routingKey = routingKey; }
    
    public Boolean getMandatory() { return mandatory; }
    public void setMandatory(Boolean mandatory) { this.mandatory = mandatory; }
    
    public Boolean getPersistent() { return persistent; }
    public void setPersistent(Boolean persistent) { this.persistent = persistent; }
    
    public String getAuthenticationType() { return authenticationType; }
    public void setAuthenticationType(String authenticationType) { this.authenticationType = authenticationType; }
    
    public String getSslTruststoreLocation() { return sslTruststoreLocation; }
    public void setSslTruststoreLocation(String sslTruststoreLocation) { this.sslTruststoreLocation = sslTruststoreLocation; }
    
    public String getSslTruststorePassword() { return sslTruststorePassword; }
    public void setSslTruststorePassword(String sslTruststorePassword) { this.sslTruststorePassword = sslTruststorePassword; }
    
    public String getSslKeystoreLocation() { return sslKeystoreLocation; }
    public void setSslKeystoreLocation(String sslKeystoreLocation) { this.sslKeystoreLocation = sslKeystoreLocation; }
    
    public String getSslKeystorePassword() { return sslKeystorePassword; }
    public void setSslKeystorePassword(String sslKeystorePassword) { this.sslKeystorePassword = sslKeystorePassword; }
    
    public String getSslKeyPassword() { return sslKeyPassword; }
    public void setSslKeyPassword(String sslKeyPassword) { this.sslKeyPassword = sslKeyPassword; }
    
    public Boolean getCompressed() { return compressed; }
    public void setCompressed(Boolean compressed) { this.compressed = compressed; }
    
    public Boolean getGzipEnabled() { return gzipEnabled; }
    public void setGzipEnabled(Boolean gzipEnabled) { this.gzipEnabled = gzipEnabled; }
    
    public List<String> getHeadersToExtract() { return headersToExtract; }
    public void setHeadersToExtract(List<String> headersToExtract) { this.headersToExtract = headersToExtract; }
    
    public String getMessageConverterClass() { return messageConverterClass; }
    public void setMessageConverterClass(String messageConverterClass) { this.messageConverterClass = messageConverterClass; }
    
    public Map<String, Object> getParameters() { return parameters; }
    public void setParameters(Map<String, Object> parameters) { this.parameters = parameters; }

    // Builder pattern for easy configuration
    public static class Builder {
        private String host;
        private Integer port;
        private String virtualHost;
        private String username;
        private String password;
        private String queueName;
        private Integer concurrency;
        private Integer prefetchCount;
        private AcknowledgeMode acknowledgeMode;
        private Integer channelCacheSize;
        private String exchange;
        private String routingKey;
        private Boolean mandatory;
        private Boolean persistent;
        private String authenticationType;
        private String sslTruststoreLocation;
        private String sslTruststorePassword;
        private String sslKeystoreLocation;
        private String sslKeystorePassword;
        private String sslKeyPassword;
        private Boolean compressed;
        private Boolean gzipEnabled;
        private List<String> headersToExtract;
        private String messageConverterClass;
        private Map<String, Object> parameters;

        public Builder host(String host) { this.host = host; return this; }
        public Builder port(Integer port) { this.port = port; return this; }
        public Builder virtualHost(String virtualHost) { this.virtualHost = virtualHost; return this; }
        public Builder username(String username) { this.username = username; return this; }
        public Builder password(String password) { this.password = password; return this; }
        public Builder queueName(String queueName) { this.queueName = queueName; return this; }
        public Builder concurrency(Integer concurrency) { this.concurrency = concurrency; return this; }
        public Builder prefetchCount(Integer prefetchCount) { this.prefetchCount = prefetchCount; return this; }
        public Builder acknowledgeMode(AcknowledgeMode acknowledgeMode) { this.acknowledgeMode = acknowledgeMode; return this; }
        public Builder channelCacheSize(Integer channelCacheSize) { this.channelCacheSize = channelCacheSize; return this; }
        public Builder exchange(String exchange) { this.exchange = exchange; return this; }
        public Builder routingKey(String routingKey) { this.routingKey = routingKey; return this; }
        public Builder mandatory(Boolean mandatory) { this.mandatory = mandatory; return this; }
        public Builder persistent(Boolean persistent) { this.persistent = persistent; return this; }
        public Builder authenticationType(String authenticationType) { this.authenticationType = authenticationType; return this; }
        public Builder sslTruststoreLocation(String sslTruststoreLocation) { this.sslTruststoreLocation = sslTruststoreLocation; return this; }
        public Builder sslTruststorePassword(String sslTruststorePassword) { this.sslTruststorePassword = sslTruststorePassword; return this; }
        public Builder sslKeystoreLocation(String sslKeystoreLocation) { this.sslKeystoreLocation = sslKeystoreLocation; return this; }
        public Builder sslKeystorePassword(String sslKeystorePassword) { this.sslKeystorePassword = sslKeystorePassword; return this; }
        public Builder sslKeyPassword(String sslKeyPassword) { this.sslKeyPassword = sslKeyPassword; return this; }
        public Builder compressed(Boolean compressed) { this.compressed = compressed; return this; }
        public Builder gzipEnabled(Boolean gzipEnabled) { this.gzipEnabled = gzipEnabled; return this; }
        public Builder headersToExtract(List<String> headersToExtract) { this.headersToExtract = headersToExtract; return this; }
        public Builder messageConverterClass(String messageConverterClass) { this.messageConverterClass = messageConverterClass; return this; }
        public Builder parameters(Map<String, Object> parameters) { this.parameters = parameters; return this; }

        public RabbitMQClientConfig build() {
            return new RabbitMQClientConfig(this);
        }
    }
}
