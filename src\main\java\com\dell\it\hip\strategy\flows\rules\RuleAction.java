package com.dell.it.hip.strategy.flows.rules;

import java.util.Map;

import org.springframework.messaging.Message;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.rules.Rule;

public interface RuleAction {
    String getName();
    String getType();
    /**
     * Perform this action as part of rule processing.
     * @param message    The inbound Spring message.
     * @param params     The action-specific parameters (from the rule or action descriptor).
     * @param rule       The Rule object currently being evaluated.
     * @param def        The HIPIntegrationDefinition in scope.
     * @return           A new (possibly updated) Message to use in further processing.
     */
    Message<?> performAction(
            Message<?> message,
            Map<String, Object> params,
            Rule rule,
            HIPIntegrationDefinition def,
            Map<String, Object> context
    );
}