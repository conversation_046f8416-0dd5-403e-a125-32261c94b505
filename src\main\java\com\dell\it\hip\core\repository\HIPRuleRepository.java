package com.dell.it.hip.core.repository;

import com.dell.it.hip.config.rules.HIPRuleEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface HIPRuleRepository extends JpaRepository<HIPRuleEntity, Long> {

    // Find one rule by exact key
    HIPRuleEntity findByRuleKey(String ruleKey);

    // Find all rules by exact key (in practice, should only be one)
    List<HIPRuleEntity> findAllByRuleKey(String ruleKey);

    // For FlowRouting, fetch all rules with prefix for a given integration
    @Query("SELECT r FROM HIPRuleEntity r WHERE r.ruleKey LIKE ?1% AND (r.deleted IS NULL OR r.deleted = false)")
    List<HIPRuleEntity> findAllByRuleKeyPrefix(String ruleKeyPrefix);

    // Or, if you prefer JPQL:
    @Query("SELECT r FROM HIPRuleEntity r WHERE r.ruleKey LIKE CONCAT(:prefix, '%') AND (r.deleted IS NULL OR r.deleted = false)")
    List<HIPRuleEntity> findByRuleKeyPrefix(@Param("prefix") String prefix);

    // For refresh, bulk fetch by multiple keys
    List<HIPRuleEntity> findAllByRuleKeyIn(List<String> ruleKeys);

    // Optional: Find all not deleted
    @Query("SELECT r FROM HIPRuleEntity r WHERE (r.deleted IS NULL OR r.deleted = false)")
    List<HIPRuleEntity> findAllActive();
}
