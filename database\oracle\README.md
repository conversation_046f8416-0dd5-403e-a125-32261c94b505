# Oracle DDL Scripts for HIP Integration Request Entity

This directory contains Oracle database DDL scripts for the `HIPIntegrationRequestEntity` class from the HIP Integration Platform.

## Files Overview

### 1. `hip_integration_request_ddl.sql`
Main DDL script that creates the core database structure:
- **Sequence**: `hip_integration_requests_seq` for primary key generation
- **Table**: `hip_integration_requests` with all required columns
- **Indexes**: Performance optimization indexes
- **Constraints**: Primary key, unique constraints, and check constraints
- **Triggers**: Auto-increment and audit timestamp triggers

### 2. `hip_integration_request_views_and_procedures.sql`
Additional database objects for enhanced functionality:
- **Views**: Filtered and summary views
- **Stored Procedures**: Common operations like soft delete and restore
- **Functions**: Utility functions for data retrieval

## Database Schema Details

### Table Structure: `hip_integration_requests`

| Column Name | Data Type | Constraints | Description |
|-------------|-----------|-------------|-------------|
| `id` | NUMBER(19,0) | PRIMARY KEY, NOT NULL | Auto-generated unique identifier |
| `service_manager_name` | VARCHAR2(255) | NOT NULL | Service manager handling the integration |
| `hip_integration_name` | VARCHAR2(255) | NOT NULL | Name of the HIP integration |
| `version` | VARCHAR2(100) | NOT NULL | Version of the integration |
| `business_flow_name` | VARCHAR2(255) | NOT NULL | Name of the business flow |
| `tags` | VARCHAR2(1000) | NULL | Categorization tags |
| `business_flow_type` | VARCHAR2(255) | NULL | Type of business flow |
| `hip_integration_type` | VARCHAR2(255) | NULL | Type of HIP integration |
| `business_flow_version` | VARCHAR2(100) | NULL | Business flow version |
| `adapters_json` | CLOB | NULL | JSON configuration for adapters |
| `handlers_json` | CLOB | NULL | JSON configuration for handlers |
| `flow_steps_json` | CLOB | NULL | JSON configuration for flow steps |
| `property_sheets_json` | CLOB | NULL | JSON configuration for property sheets |
| `throttle_settings_json` | CLOB | NULL | JSON configuration for throttle settings |
| `status` | VARCHAR2(50) | NULL | Current status of the integration |
| `deleted` | NUMBER(1,0) | DEFAULT 0 | Soft delete flag (0=active, 1=deleted) |
| `created_at` | TIMESTAMP | NOT NULL | Record creation timestamp |
| `updated_at` | TIMESTAMP | NULL | Last update timestamp |

### Key Constraints

1. **Primary Key**: `pk_hip_integration_requests` on `id`
2. **Unique Constraint**: `uk_hip_int_req_business_key` on (`service_manager_name`, `hip_integration_name`, `version`)
3. **Check Constraint**: `chk_hip_int_req_deleted` ensures `deleted` is 0 or 1

### Indexes

- `idx_hip_int_req_service_mgr`: On `service_manager_name`
- `idx_hip_int_req_name`: On `hip_integration_name`
- `idx_hip_int_req_version`: On `version`
- `idx_hip_int_req_status`: On `status`
- `idx_hip_int_req_deleted`: On `deleted`
- `idx_hip_int_req_business_key`: Composite on business key fields
- `idx_hip_int_req_created_at`: On `created_at`
- `idx_hip_int_req_updated_at`: On `updated_at`

## Installation Instructions

### Prerequisites
- Oracle Database 12c or higher
- Appropriate schema/user with DDL privileges
- SYSDBA access for sequence and trigger creation (if required)

### Step 1: Execute Main DDL Script
```sql
-- Connect as schema owner or DBA
@hip_integration_request_ddl.sql
```

### Step 2: Execute Views and Procedures (Optional)
```sql
-- Execute additional database objects
@hip_integration_request_views_and_procedures.sql
```

### Step 3: Grant Permissions
Uncomment and modify the GRANT statements in both scripts according to your security requirements:

```sql
-- Example grants for application user
GRANT SELECT, INSERT, UPDATE, DELETE ON hip_integration_requests TO hip_app_user;
GRANT SELECT ON hip_integration_requests_seq TO hip_app_user;
GRANT SELECT ON v_active_hip_integration_requests TO hip_app_user;
GRANT EXECUTE ON sp_soft_delete_hip_integration_request TO hip_app_user;
```

## Usage Examples

### Basic Operations

#### Insert a new record:
```sql
INSERT INTO hip_integration_requests (
    service_manager_name,
    hip_integration_name,
    version,
    business_flow_name,
    status
) VALUES (
    'OrderService',
    'OrderProcessingIntegration',
    '1.0.0',
    'ProcessOrderFlow',
    'ACTIVE'
);
```

#### Query active integrations:
```sql
SELECT * FROM v_active_hip_integration_requests
WHERE service_manager_name = 'OrderService';
```

#### Soft delete a record:
```sql
DECLARE
    result VARCHAR2(100);
BEGIN
    sp_soft_delete_hip_integration_request(1, result);
    DBMS_OUTPUT.PUT_LINE(result);
END;
/
```

### Advanced Queries

#### Get integration summary by service manager:
```sql
SELECT * FROM v_hip_integration_request_summary
ORDER BY total_requests DESC;
```

#### Find latest version of an integration:
```sql
SELECT fn_get_latest_integration_version('OrderService', 'OrderProcessingIntegration') 
FROM dual;
```

## Maintenance

### Regular Maintenance Tasks

1. **Monitor sequence usage**:
   ```sql
   SELECT sequence_name, last_number FROM user_sequences 
   WHERE sequence_name = 'HIP_INTEGRATION_REQUESTS_SEQ';
   ```

2. **Check for fragmentation**:
   ```sql
   SELECT table_name, num_rows, blocks, avg_row_len 
   FROM user_tables 
   WHERE table_name = 'HIP_INTEGRATION_REQUESTS';
   ```

3. **Analyze table statistics**:
   ```sql
   EXEC DBMS_STATS.GATHER_TABLE_STATS('YOUR_SCHEMA', 'HIP_INTEGRATION_REQUESTS');
   ```

### Backup Considerations

- Ensure CLOB columns are included in backup strategies
- Consider archiving old records based on `created_at` timestamp
- Implement retention policies for soft-deleted records

## Troubleshooting

### Common Issues

1. **Sequence not found**: Ensure the sequence was created successfully
2. **Unique constraint violation**: Check for duplicate business key combinations
3. **CLOB handling**: Ensure proper handling of large JSON payloads
4. **Timestamp issues**: Verify timezone settings match application expectations

### Performance Tuning

- Monitor index usage with `V$SQL_PLAN`
- Consider partitioning for large datasets
- Optimize JSON queries using Oracle JSON functions
- Regular statistics gathering for optimal query plans

## Notes

- This schema is designed for Oracle 12c+ with JSON support
- CLOB columns store JSON data as text - consider using Oracle's native JSON datatype for 12c+
- Triggers handle auto-increment and audit fields automatically
- Soft delete pattern is implemented for data retention
