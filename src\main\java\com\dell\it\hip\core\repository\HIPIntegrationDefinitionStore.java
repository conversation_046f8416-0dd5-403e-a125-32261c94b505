package com.dell.it.hip.core.repository;

import java.util.List;

import com.dell.it.hip.config.HIPIntegrationRequestEntity;

public interface HIPIntegrationDefinitionStore {
	void save(HIPIntegrationRequestEntity entity);
    HIPIntegrationRequestEntity find(String serviceManagerName, String hipIntegrationName, String version);
    boolean exists(String serviceManagerName, String hipIntegrationName, String version);
    List<HIPIntegrationRequestEntity> findByServiceManagerName(String serviceManagerName);
    void deleteByServiceManagerNameAndHipIntegrationNameAndVersion(String serviceManagerName, String hipIntegrationName, String version);
   
}
