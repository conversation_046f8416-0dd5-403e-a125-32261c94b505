package com.dell.it.hip.util;

public interface ThrottlingService {

    /**
     * Attempts to consume a throttle token for the given adapter.
     * Returns true if allowed, false if throttled.
     */
    boolean tryConsumeToken(String serviceManagerName, String integrationId, String integrationVersion,String adapterId,
                            ThrottleSettings settings);

    /**
     * Resets the throttle state for the given adapter.
     */
    void resetThrottle(String serviceManagerName, String integrationId, String integrationVersion, String adapterId);

    /**
     * Updates the throttle settings for the given adapter.
     */
    void updateThrottle(String serviceManagerName, String integrationId, String integrationVersion,String adapterId,
                        ThrottleSettings settings);

    /**
     * Returns the current throttle settings for the given adapter.
     */
    ThrottleSettings getThrottleSettings(String serviceManagerName, String integrationId, String integrationVersion, String adapterId);
}