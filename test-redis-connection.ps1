# Test Script for Redis Connection Fix
# This script helps verify that the Redis connection issue has been resolved

Write-Host "🔧 Testing Redis Connection Fix..." -ForegroundColor Green
Write-Host ""

# Check if the cloud profile configuration exists
Write-Host "📋 Checking Redis configuration files..." -ForegroundColor Yellow
if (Test-Path "src/main/resources/application-cloud.yaml") {
    $cloudConfig = Get-Content "src/main/resources/application-cloud.yaml" -Raw
    if ($cloudConfig -match "redis:") {
        Write-Host "✅ Cloud profile Redis configuration found" -ForegroundColor Green
        
        if ($cloudConfig -match "redisson:") {
            Write-Host "✅ Redisson configuration found in cloud profile" -ForegroundColor Green
        } else {
            Write-Host "❌ Redisson configuration missing" -ForegroundColor Red
        }
        
        if ($cloudConfig -match "ssl:") {
            Write-Host "✅ SSL configuration found" -ForegroundColor Green
        } else {
            Write-Host "❌ SSL configuration missing" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Redis configuration not found in cloud profile" -ForegroundColor Red
    }
} else {
    Write-Host "❌ application-cloud.yaml not found" -ForegroundColor Red
}

# Check POM.xml for Redisson version
Write-Host "📦 Checking Redisson version in POM..." -ForegroundColor Yellow
if (Test-Path "pom.xml") {
    $pomContent = Get-Content "pom.xml" -Raw
    if ($pomContent -match "redisson-spring-boot-starter") {
        if ($pomContent -match "<redisson\.version>3\.35\.0</redisson\.version>") {
            Write-Host "✅ Redisson version 3.35.0 configured correctly" -ForegroundColor Green
        } elseif ($pomContent -match "3\.49\.0") {
            Write-Host "❌ Old Redisson version 3.49.0 still present" -ForegroundColor Red
        } else {
            Write-Host "⚠️  Redisson version found but may need verification" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ Redisson dependency not found in POM" -ForegroundColor Red
    }
} else {
    Write-Host "❌ pom.xml not found" -ForegroundColor Red
}

# Check Redis configuration class
Write-Host "🔧 Checking Redis configuration class..." -ForegroundColor Yellow
if (Test-Path "src/main/java/com/dell/it/hip/config/RedisConfig.java") {
    $redisConfig = Get-Content "src/main/java/com/dell/it/hip/config/RedisConfig.java" -Raw
    if ($redisConfig -match "ConditionalOnProperty") {
        Write-Host "✅ Conditional Redis configuration found" -ForegroundColor Green
    } else {
        Write-Host "❌ Conditional configuration missing" -ForegroundColor Red
    }
    
    if ($redisConfig -match "connection test") {
        Write-Host "✅ Connection testing logic found" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Connection testing may be missing" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ RedisConfig.java not found" -ForegroundColor Red
}

# Check Redis health indicator
Write-Host "🏥 Checking Redis health indicator..." -ForegroundColor Yellow
if (Test-Path "src/main/java/com/dell/it/hip/config/RedisHealthIndicator.java") {
    Write-Host "✅ Custom Redis health indicator found" -ForegroundColor Green
} else {
    Write-Host "⚠️  Custom Redis health indicator not found" -ForegroundColor Yellow
}

# Check launch configuration
Write-Host "🚀 Checking launch configuration..." -ForegroundColor Yellow
if (Test-Path ".vscode/launch.json") {
    $launchConfig = Get-Content ".vscode/launch.json" -Raw
    if ($launchConfig -match "Cloud Profile") {
        Write-Host "✅ Cloud Profile launch configuration found" -ForegroundColor Green
        
        if ($launchConfig -match "SPRING_PROFILES_ACTIVE.*cloud") {
            Write-Host "✅ Cloud profile environment variable configured" -ForegroundColor Green
        } else {
            Write-Host "❌ Cloud profile environment variable missing" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Cloud Profile launch configuration not found" -ForegroundColor Red
    }
} else {
    Write-Host "❌ launch.json not found" -ForegroundColor Red
}

# Test Maven build
Write-Host "🔨 Testing Maven build..." -ForegroundColor Yellow
try {
    $buildResult = mvn compile -q 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Maven build successful" -ForegroundColor Green
    } else {
        Write-Host "❌ Maven build failed" -ForegroundColor Red
        Write-Host "   Build output: $buildResult" -ForegroundColor Cyan
    }
} catch {
    Write-Host "❌ Maven build test failed" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎯 Next Steps to Test Redis Connection:" -ForegroundColor Cyan
Write-Host "1. Start the application with Cloud Profile:" -ForegroundColor White
Write-Host "   - Open VS Code Spring Boot Dashboard" -ForegroundColor White
Write-Host "   - Select 'Launch HipServicesApplication (Cloud Profile)'" -ForegroundColor White
Write-Host "   - Click Start" -ForegroundColor White
Write-Host ""
Write-Host "2. Check application logs for:" -ForegroundColor White
Write-Host "   - 'Redis connection test successful'" -ForegroundColor White
Write-Host "   - 'Creating StringRedisTemplate with Redis enabled: true'" -ForegroundColor White
Write-Host "   - No 'HTTP/1.1 400 Bad Request' errors" -ForegroundColor White
Write-Host ""
Write-Host "3. Test Redis health endpoint:" -ForegroundColor White
Write-Host "   curl http://localhost:8080/hip-services/actuator/health/redis" -ForegroundColor White
Write-Host ""
Write-Host "📚 For detailed information, see: REDIS_CONNECTION_FIX.md" -ForegroundColor Cyan
Write-Host ""
Write-Host "🎉 Redis connection fix verification complete!" -ForegroundColor Green
