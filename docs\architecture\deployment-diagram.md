# HIP Services Deployment Diagram

This diagram illustrates how the HIP Services framework is deployed across different environments, showing the configuration management infrastructure and how property naming conventions are applied in deployment scenarios.

```mermaid
graph TB
    %% Cloud Infrastructure
    subgraph "Cloud Infrastructure"
        subgraph "Configuration Management"
            subgraph "Config Server Cluster"
                CS1[Config Server 1<br/>Port: 8888]
                CS2[Config Server 2<br/>Port: 8888]
                CS3[Config Server 3<br/>Port: 8888]
            end

            subgraph "Property Repositories"
                GIT[Git Repository<br/>- shared-kafka-properties.yml<br/>- shared-enterprise-security.yml<br/>- orderProcessing-adapter-kafka.yml<br/>- orderProcessing-handler-sftp.yml]
                VAULT[HashiCorp Vault<br/>- Encrypted Secrets<br/>- SSL Certificates<br/>- API Keys]
            end
        end

        subgraph "Service Mesh"
            subgraph "HIP Service Instances"
                HIP1[HIP Service Instance 1<br/>- PropertySheetFetcher<br/>- ConfigClassRegistry<br/>- Dynamic Adapters/Handlers]
                HIP2[HIP Service Instance 2<br/>- PropertySheetFetcher<br/>- ConfigClassRegistry<br/>- Dynamic Adapters/Handlers]
                HIP3[HIP Service Instance 3<br/>- PropertySheetFetcher<br/>- ConfigClassRegistry<br/>- Dynamic Adapters/Handlers]
            end

            LB[Load Balancer<br/>NGINX/HAProxy]
        end

        subgraph "Monitoring & Observability"
            PROM[Prometheus<br/>Metrics Collection]
            GRAF[Grafana<br/>Dashboards]
            JAEGER[Jaeger<br/>Distributed Tracing]
            ELK[ELK Stack<br/>Centralized Logging]
        end
    end

    %% External Systems
    subgraph "External Integration Points"
        subgraph "Message Brokers"
            KAFKA_PROD[Kafka Production<br/>kafka.producer.bootstrap.servers<br/>kafka.consumer.bootstrap.servers]
            IBMMQ_PROD[IBM MQ Production<br/>ibmmq.producer.queue.manager<br/>ibmmq.consumer.queue.manager]
            RABBITMQ_PROD[RabbitMQ Production<br/>rabbitmq.producer.routing.key<br/>rabbitmq.consumer.ssl.enabled]
        end

        subgraph "File Systems"
            SFTP_PROD[SFTP Servers<br/>sftp.producer.remote.directory<br/>sftp.consumer.private.key.path]
            NAS_PROD[NAS Storage<br/>nas.producer.mount.path<br/>nas.consumer.file.filter]
        end

        subgraph "APIs"
            HTTPS_PROD[HTTPS APIs<br/>https.producer.api.key.header<br/>https.consumer.oauth.required]
        end
    end

    %% Development Environment
    subgraph "Development Environment"
        subgraph "Local Development"
            DEV_CONFIG[Local Config Server<br/>localhost:8888]
            DEV_HIP[HIP Service Local<br/>- Hot Reload<br/>- Debug Mode<br/>- Test Configurations]
        end

        subgraph "Test Systems"
            KAFKA_TEST[Kafka Test<br/>localhost:9092]
            IBMMQ_TEST[IBM MQ Test<br/>localhost:1414]
            RABBITMQ_TEST[RabbitMQ Test<br/>localhost:5672]
        end
    end

    %% Configuration Flow
    GIT --> CS1
    GIT --> CS2
    GIT --> CS3
    VAULT --> CS1
    VAULT --> CS2
    VAULT --> CS3

    CS1 --> HIP1
    CS2 --> HIP2
    CS3 --> HIP3

    %% Load Balancing
    LB --> HIP1
    LB --> HIP2
    LB --> HIP3

    %% External Connections
    HIP1 --> KAFKA_PROD
    HIP1 --> IBMMQ_PROD
    HIP1 --> RABBITMQ_PROD
    HIP1 --> SFTP_PROD
    HIP1 --> NAS_PROD
    HIP1 --> HTTPS_PROD

    HIP2 --> KAFKA_PROD
    HIP2 --> IBMMQ_PROD
    HIP2 --> RABBITMQ_PROD
    HIP2 --> SFTP_PROD
    HIP2 --> NAS_PROD
    HIP2 --> HTTPS_PROD

    HIP3 --> KAFKA_PROD
    HIP3 --> IBMMQ_PROD
    HIP3 --> RABBITMQ_PROD
    HIP3 --> SFTP_PROD
    HIP3 --> NAS_PROD
    HIP3 --> HTTPS_PROD

    %% Monitoring
    HIP1 --> PROM
    HIP2 --> PROM
    HIP3 --> PROM
    PROM --> GRAF

    HIP1 --> JAEGER
    HIP2 --> JAEGER
    HIP3 --> JAEGER

    HIP1 --> ELK
    HIP2 --> ELK
    HIP3 --> ELK

    %% Development
    DEV_CONFIG --> DEV_HIP
    DEV_HIP --> KAFKA_TEST
    DEV_HIP --> IBMMQ_TEST
    DEV_HIP --> RABBITMQ_TEST

    %% Styling
    classDef configServer fill:#e3f2fd,stroke:#0277bd,stroke-width:2px
    classDef hipService fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef external fill:#ffebee,stroke:#c62828,stroke-width:2px
    classDef monitoring fill:#e8f5e8,stroke:#2e7d32,stroke-width:2px
    classDef development fill:#fff3e0,stroke:#f57c00,stroke-width:2px

    class CS1,CS2,CS3,DEV_CONFIG configServer
    class HIP1,HIP2,HIP3,DEV_HIP hipService
    class KAFKA_PROD,IBMMQ_PROD,RABBITMQ_PROD,SFTP_PROD,NAS_PROD,HTTPS_PROD,KAFKA_TEST,IBMMQ_TEST,RABBITMQ_TEST external
    class PROM,GRAF,JAEGER,ELK monitoring
    class DEV_CONFIG,DEV_HIP development
```

## Deployment Architecture Overview

### Cloud Infrastructure
- **Config Server Cluster**: High-availability configuration management with 3 instances
- **Service Mesh**: Load-balanced HIP service instances with dynamic scaling
- **Monitoring Stack**: Comprehensive observability with Prometheus, Grafana, Jaeger, and ELK

### Configuration Management
- **Git Repository**: Version-controlled property sheets with environment-specific configurations
- **HashiCorp Vault**: Secure storage for sensitive configuration data
- **Property Naming**: Consistent dot-separated lowercase convention across all environments

### External Integration Points
All external systems use the standardized property naming convention:
- **Message Brokers**: Kafka, IBM MQ, RabbitMQ with producer/consumer specific properties
- **File Systems**: SFTP and NAS with protocol-specific configurations
- **APIs**: HTTPS endpoints with authentication and security configurations

### Development Environment
- **Local Development**: Simplified setup with local config server and test systems
- **Hot Reload**: Dynamic configuration updates for rapid development cycles
- **Test Integration**: Local instances of all external systems for testing
