package com.dell.it.hip.config.FlowSteps;

import java.util.List;
import java.util.Objects;

import lombok.Data;

@Data
public class AttributeMapping {
    private String attributeName;      // Name of header to add


    private String derivedFrom; // Enum to choose strategy
    private String expression;         // Regex, XPath, JSONPath, etc.
    private boolean required;          // If true, step will fail if missing
    private List<String> usage;
    public AttributeMapping() {}

    public AttributeMapping(String attributeName, String derivedFrom, String expression, boolean required, List<String> usage) {
        this.attributeName = attributeName;
        this.derivedFrom = derivedFrom;
        this.expression = expression;
        this.required = required;
        this.usage = usage;
    }

    public String getAttributeName() {
        return attributeName;
    }
    public void setAttributeName(String attributeName) {
        this.attributeName = attributeName;
    }

    public String getDerivedFrom() {
        return derivedFrom;
    }
    public void setDerivedFrom(String derivedFrom) {
        this.derivedFrom = derivedFrom;
    }

    public String getExpression() {
        return expression;
    }
    public void setExpression(String expression) {
        this.expression = expression;
    }

    public boolean isRequired() {
        return required;
    }
    public void setRequired(boolean required) {
        this.required = required;
    }
    public List<String> getUsage() {
        return usage;
    }

    public void setUsage(List<String> usage) {
        this.usage = usage;
    }


    @Override
    public String toString() {
        return "AttributeMapping{" +
                "attributeName='" + attributeName + '\'' +
                ", derivedFrom=" + derivedFrom +
                ", expression='" + expression + '\'' +
                ", required=" + required +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof AttributeMapping)) return false;
        AttributeMapping that = (AttributeMapping) o;
        return required == that.required &&
                Objects.equals(attributeName, that.attributeName) &&
                derivedFrom == that.derivedFrom &&
                Objects.equals(expression, that.expression);
    }

    @Override
    public int hashCode() {
        return Objects.hash(attributeName, derivedFrom, expression, required);
    }
}