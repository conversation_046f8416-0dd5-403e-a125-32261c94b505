package com.dell.it.hip.client;

import java.util.Map;

/**
 * Configuration class for NasClient that mirrors the property structure
 * used in DynamicNASAdapterConfig and DynamicNasHandlerConfig.
 * 
 * This class can be used for both consumer (adapter testing) and producer (handler testing)
 * configurations by using the appropriate property prefixes.
 * 
 * Supports both NFS and SMB protocols for network-attached storage operations.
 */
public class NasClientConfig {
    
    // Core NAS connection properties
    private String protocol;              // "nfs" or "smb"
    private String host;                  // Host/IP for SMB; not needed for NFS if local mount
    private String remoteDirectory;       // Directory/share/subpath
    private String fileSeparator = "/";   // "/" for NFS, usually "\\" for SMB
    
    // SMB-specific properties
    private String shareName;             // Required for SMB (e.g., "SHARE" in \\host\SHARE\path\to\file)
    private String username;              // SMB username
    private String password;              // SMB password
    private String domain;                // Optional: For Active Directory/Kerberos
    private String mountPath;             // OS-mounted path, e.g. /mnt/smbshare
    
    // Consumer-specific properties (adapter testing)
    private String fileNamePattern;       // Regex for files, e.g. ".*\\.csv"
    private String fileSortOrder;         // "OLDEST", "NEWEST", or null
    private Integer maxFilesPerPoll;      // Limit per poll cycle
    private Long pollingIntervalMs = 60000L; // How often to poll (ms), default 60000
    private Long fileAgeMs;               // Minimum file age before pickup (ms)
    private Boolean ignoreHiddenFiles;    // Skip hidden files
    private String charset = "UTF-8";     // e.g., "UTF-8"
    private String postProcessAction;     // "delete", "rename", or "move"
    private String renamePattern;         // e.g. "{file}.processed"
    
    // Producer-specific properties (handler testing)
    private Boolean gzipEnabled = false;  // Enable gzip compression
    
    // Advanced properties
    private Map<String, Object> parameters; // Extra options as needed
    
    // Default constructor
    public NasClientConfig() {}
    
    // Builder pattern constructor
    private NasClientConfig(Builder builder) {
        this.protocol = builder.protocol;
        this.host = builder.host;
        this.remoteDirectory = builder.remoteDirectory;
        this.fileSeparator = builder.fileSeparator;
        this.shareName = builder.shareName;
        this.username = builder.username;
        this.password = builder.password;
        this.domain = builder.domain;
        this.mountPath = builder.mountPath;
        this.fileNamePattern = builder.fileNamePattern;
        this.fileSortOrder = builder.fileSortOrder;
        this.maxFilesPerPoll = builder.maxFilesPerPoll;
        this.pollingIntervalMs = builder.pollingIntervalMs;
        this.fileAgeMs = builder.fileAgeMs;
        this.ignoreHiddenFiles = builder.ignoreHiddenFiles;
        this.charset = builder.charset;
        this.postProcessAction = builder.postProcessAction;
        this.renamePattern = builder.renamePattern;
        this.gzipEnabled = builder.gzipEnabled;
        this.parameters = builder.parameters;
    }
    
    /**
     * Create a consumer configuration for NFS with the provided test properties
     */
    public static NasClientConfig createNfsConsumerConfig() {
        return new Builder()
            .protocol("nfs")
            .remoteDirectory("/opt/test")
            .fileSeparator("/")
            .fileNamePattern(".*")
            .pollingIntervalMs(60000L)
            .charset("UTF-8")
            .postProcessAction("delete")
            .build();
    }
    
    /**
     * Create a producer configuration for NFS with the provided test properties
     */
    public static NasClientConfig createNfsProducerConfig() {
        return new Builder()
            .protocol("nfs")
            .remoteDirectory("/opt/test")
            .fileSeparator("/")
            .gzipEnabled(false)
            .build();
    }
    
    /**
     * Create a consumer configuration for SMB with the provided test properties
     */
    public static NasClientConfig createSmbConsumerConfig() {
        return new Builder()
            .protocol("smb")
            .host("localhost")
            .shareName("test")
            .remoteDirectory("/opt/test")
            .fileSeparator("/")
            .username("testuser")
            .password("testpass")
            .fileNamePattern(".*")
            .pollingIntervalMs(60000L)
            .charset("UTF-8")
            .postProcessAction("delete")
            .build();
    }
    
    /**
     * Create a producer configuration for SMB with the provided test properties
     */
    public static NasClientConfig createSmbProducerConfig() {
        return new Builder()
            .protocol("smb")
            .host("localhost")
            .shareName("test")
            .remoteDirectory("/opt/test")
            .fileSeparator("/")
            .username("testuser")
            .password("testpass")
            .gzipEnabled(false)
            .build();
    }
    
    // Getters and setters
    public String getProtocol() { return protocol; }
    public void setProtocol(String protocol) { this.protocol = protocol; }
    
    public String getHost() { return host; }
    public void setHost(String host) { this.host = host; }
    
    public String getRemoteDirectory() { return remoteDirectory; }
    public void setRemoteDirectory(String remoteDirectory) { this.remoteDirectory = remoteDirectory; }
    
    public String getFileSeparator() { return fileSeparator; }
    public void setFileSeparator(String fileSeparator) { this.fileSeparator = fileSeparator; }
    
    public String getShareName() { return shareName; }
    public void setShareName(String shareName) { this.shareName = shareName; }
    
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
    
    public String getDomain() { return domain; }
    public void setDomain(String domain) { this.domain = domain; }
    
    public String getMountPath() { return mountPath; }
    public void setMountPath(String mountPath) { this.mountPath = mountPath; }
    
    public String getFileNamePattern() { return fileNamePattern; }
    public void setFileNamePattern(String fileNamePattern) { this.fileNamePattern = fileNamePattern; }
    
    public String getFileSortOrder() { return fileSortOrder; }
    public void setFileSortOrder(String fileSortOrder) { this.fileSortOrder = fileSortOrder; }
    
    public Integer getMaxFilesPerPoll() { return maxFilesPerPoll; }
    public void setMaxFilesPerPoll(Integer maxFilesPerPoll) { this.maxFilesPerPoll = maxFilesPerPoll; }
    
    public Long getPollingIntervalMs() { return pollingIntervalMs; }
    public void setPollingIntervalMs(Long pollingIntervalMs) { this.pollingIntervalMs = pollingIntervalMs; }
    
    public Long getFileAgeMs() { return fileAgeMs; }
    public void setFileAgeMs(Long fileAgeMs) { this.fileAgeMs = fileAgeMs; }
    
    public Boolean getIgnoreHiddenFiles() { return ignoreHiddenFiles; }
    public void setIgnoreHiddenFiles(Boolean ignoreHiddenFiles) { this.ignoreHiddenFiles = ignoreHiddenFiles; }
    
    public String getCharset() { return charset; }
    public void setCharset(String charset) { this.charset = charset; }
    
    public String getPostProcessAction() { return postProcessAction; }
    public void setPostProcessAction(String postProcessAction) { this.postProcessAction = postProcessAction; }
    
    public String getRenamePattern() { return renamePattern; }
    public void setRenamePattern(String renamePattern) { this.renamePattern = renamePattern; }
    
    public Boolean getGzipEnabled() { return gzipEnabled; }
    public void setGzipEnabled(Boolean gzipEnabled) { this.gzipEnabled = gzipEnabled; }
    
    public Map<String, Object> getParameters() { return parameters; }
    public void setParameters(Map<String, Object> parameters) { this.parameters = parameters; }

    // Builder pattern for easy configuration
    public static class Builder {
        private String protocol;
        private String host;
        private String remoteDirectory;
        private String fileSeparator = "/";
        private String shareName;
        private String username;
        private String password;
        private String domain;
        private String mountPath;
        private String fileNamePattern;
        private String fileSortOrder;
        private Integer maxFilesPerPoll;
        private Long pollingIntervalMs = 60000L;
        private Long fileAgeMs;
        private Boolean ignoreHiddenFiles;
        private String charset = "UTF-8";
        private String postProcessAction;
        private String renamePattern;
        private Boolean gzipEnabled = false;
        private Map<String, Object> parameters;

        public Builder protocol(String protocol) { this.protocol = protocol; return this; }
        public Builder host(String host) { this.host = host; return this; }
        public Builder remoteDirectory(String remoteDirectory) { this.remoteDirectory = remoteDirectory; return this; }
        public Builder fileSeparator(String fileSeparator) { this.fileSeparator = fileSeparator; return this; }
        public Builder shareName(String shareName) { this.shareName = shareName; return this; }
        public Builder username(String username) { this.username = username; return this; }
        public Builder password(String password) { this.password = password; return this; }
        public Builder domain(String domain) { this.domain = domain; return this; }
        public Builder mountPath(String mountPath) { this.mountPath = mountPath; return this; }
        public Builder fileNamePattern(String fileNamePattern) { this.fileNamePattern = fileNamePattern; return this; }
        public Builder fileSortOrder(String fileSortOrder) { this.fileSortOrder = fileSortOrder; return this; }
        public Builder maxFilesPerPoll(Integer maxFilesPerPoll) { this.maxFilesPerPoll = maxFilesPerPoll; return this; }
        public Builder pollingIntervalMs(Long pollingIntervalMs) { this.pollingIntervalMs = pollingIntervalMs; return this; }
        public Builder fileAgeMs(Long fileAgeMs) { this.fileAgeMs = fileAgeMs; return this; }
        public Builder ignoreHiddenFiles(Boolean ignoreHiddenFiles) { this.ignoreHiddenFiles = ignoreHiddenFiles; return this; }
        public Builder charset(String charset) { this.charset = charset; return this; }
        public Builder postProcessAction(String postProcessAction) { this.postProcessAction = postProcessAction; return this; }
        public Builder renamePattern(String renamePattern) { this.renamePattern = renamePattern; return this; }
        public Builder gzipEnabled(Boolean gzipEnabled) { this.gzipEnabled = gzipEnabled; return this; }
        public Builder parameters(Map<String, Object> parameters) { this.parameters = parameters; return this; }

        public NasClientConfig build() {
            return new NasClientConfig(this);
        }
    }
}
