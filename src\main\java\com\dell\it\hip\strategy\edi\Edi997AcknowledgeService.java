package com.dell.it.hip.strategy.edi;

import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;

import org.apache.commons.io.IOUtils;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Service;

import io.xlate.edi.stream.EDIInputFactory;
import io.xlate.edi.stream.EDIStreamEvent;
import io.xlate.edi.stream.EDIStreamReader;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class Edi997AcknowledgeService {

	public AcknowledgeObect ediX12AckObj(Message<?> message) throws IOException {
		log.info("Inside EDIX12Acknowledge----------->");
		String gsControlNum = null;
		String geControlNum = null;
		Map<String, Object> headerMap = new HashMap<>();
		String ediType;
		String isaControlNumber = null;
		String ieaControlNumber = null;
		EDIStreamReader reader = null;
		int ieaFunctionCount = 0;
		int numOfIncludeSegements = 4;
		AcknowledgeObect ackObj = new AcknowledgeObect();
		/* (1) Open the EDI file - any InputStream can be used. */
		try {
			String payload = message.getPayload().toString();
			EDIInputFactory factory = EDIInputFactory.newFactory();
			InputStream stream = IOUtils.toInputStream(payload, "UTF-8");
			reader = factory.createEDIStreamReader(stream);
			EDIStreamEvent event;
			String segment = null;
			int actualFunctionalGroupCount = 0;
			int geTransctionGroupCount = 0;
			int stTransGroup = 0;
			FunctionalAck fAck = new FunctionalAck();
			AK2 ak2 = new AK2();
			AK5 ak5 = new AK5();
			AK1 ak1 = new AK1();
			List<TransactionSetResponse> ak2List = new ArrayList<>();
			List<FunctionalAck> fAckList = new ArrayList<>();
			AK9 ak9 = new AK9();
			TransactionSetResponse tsr = new TransactionSetResponse();
			ST st = new ST();
			SE se = new SE();
			GS gs = new GS();
			GE ge = new GE();
			ISA isa = new ISA();
			IEA iea = new IEA();

			/* (3) Loop over the reader's events */
			while (reader.hasNext()) {
				event = reader.next();

				if (event == EDIStreamEvent.START_SEGMENT) {
					/*
					 * (4) Each time a segment is encountered, save the segment tag in a local
					 * variable
					 */
					segment = reader.getText();
					if ("GS".equals(segment)) {
						stTransGroup = 0;
						actualFunctionalGroupCount++;
						log.info("GS count....{}", actualFunctionalGroupCount);
						gs.setGsDate(getDate());
						gs.setGsTime(getTime());
						gs.setGsControlNumber(getRandomNumber());
						
					} else if ("ST".equals(segment)) {
						stTransGroup++;
						log.info("GS count...." + stTransGroup);
						ak2 = new AK2();
						ak5= new AK5();
					}else if ("SE".equals(segment)) {
						tsr.setAK2(ak2);
						tsr.setAK5(ak5);
						ak2List.add(tsr);
						ak9.setAcknowledgmentCode("A");
						ak9.setNumberOfAcceptedSets(stTransGroup);
						ak9.setNumberOfRejectedSets(stTransGroup);
						ak9.setNumberOfTransactionSets(stTransGroup);
						tsr = new TransactionSetResponse();
						st = new ST();
						se= new SE();
					}
					else if ("GE".equals(segment)) {
						st.setTsControlNumber(getRandomNumberFour());
						st.setTsIdentifierCode("997");
						fAck.setGS(gs);
						fAck.setST(st);
						se.setNumberOfIncludeSegements(numOfIncludeSegements);
						se.setTsControlNumber(st.getTsControlNumber());
						fAck.setAK1(ak1);
						fAck.setTransactionSetResponse(ak2List);
						fAck.setAK9(ak9);
						fAck.setSE(se);
						ge.setNumOfTransactionSets(stTransGroup);
						ge.setGeControlNumber(gs.getGsControlNumber());						
						fAck.setGE(ge);
						fAckList.add(fAck);
						ackObj.setFunctionalAck(fAckList);
						fAck = new FunctionalAck();
						ak9 = new AK9();
						ak2List = new ArrayList<>();
						numOfIncludeSegements=4;
						gs= new GS();
						ge= new GE();
					}
				} else if (event == EDIStreamEvent.ELEMENT_DATA) {
					if ("GS".equals(segment)) {
						if(reader.getLocation().getElementPosition() == 1) {
							ak1.setFunctionCode(reader.getText());
						}
						if (reader.getLocation().getElementPosition() == 6) {
							gsControlNum = reader.getText();
							ak1.setGroupControlNumber(gsControlNum);
							log.info("GS gsControlNum....", gsControlNum);
						}
						if(reader.getLocation().getElementPosition() == 2) {
							gs.setGsSendersCode(reader.getText());
						}
						if(reader.getLocation().getElementPosition() == 3) {
							gs.setGsReceiversCode(reader.getText());
						}
					}
					if ("ST".equals(segment)) {
						if (reader.getLocation().getElementPosition() == 1) {
							String doctype = reader.getText();
							//headerMap.put(Constant.DOCTYPE, doctype);
							log.info("DOCTYPE....", doctype);
							ak2.setTransactionSetIdentifier(doctype);
							numOfIncludeSegements++;
						}
						if (reader.getLocation().getElementPosition() == 2) {
							ak2.setTransactionControlNumber(reader.getText());
							ak5.setTsAckCode("A");
							numOfIncludeSegements++;
						}
					}
					// Check if segment is ISA (Interchange Control Header)
					if ("ISA".equals(segment)) {
					    int position = reader.getLocation().getElementPosition();
					    String text = reader.getText();
					    
					    switch (position) {
						    case 1:
						    	isa.setISA01(text);
					            break;
						    case 2:
						    	isa.setISA02(text);
					            break;
						    case 3:
						    	isa.setISA03(text);
					            break;
						    case 4:
						    	isa.setISA04(text);
					            break;
						    case 5:
						    	isa.setISA05(text);
					            break;					            
					        case 6:
					            headerMap.put("ISASENDERID", text);
					            log.info("Sender....");
					            isa.setISA06(text);
					            break;
					        case 7:
						    	isa.setISA07(text);
					            break;
					        case 8:
					            headerMap.put("ISARECIVERID", text);
					            log.info("ReceiverID....");
					            isa.setISA08(text);
					            break;
					        case 9:
						    	isa.setISA09(getDate());
					            break;
					        case 10:
						    	isa.setISA10(getTime());
					            break;
					        case 11:
					            headerMap.put("ACK", "U".equals(text) ? "true" : "false");
					            isa.setISA11(text);
					            break;
					        case 12:
						    	isa.setISA12(text);
					            break;
					        case 13:
					            isaControlNumber = text;
					            log.info("ISA isaControlNumber....", isaControlNumber);
					            isa.setISA13(getRandomNumber());
					            break;
					        default:
					            break;
					    }
					}

					if ("GE".equals(segment)) {
						if (reader.getLocation().getElementPosition() == 2) {// ISA13 contains Interchange Control
																				// Number
							geControlNum = reader.getText();
							log.info("GE geControlNum....", geControlNum);
							if (geControlNum != null && gsControlNum != null && geControlNum.equals(gsControlNum)) {
								log.info("EDI message is valid. GS and GE Control numbers match.");
							} else {
								headerMap.put("validInput", "false");
								headerMap.put("ermsg",
										"EDI message is invalid. GS and GE Control numbers do not match.");
								break;
							}
						} else if (reader.getLocation().getElementPosition() == 1) {
							geTransctionGroupCount = Integer.parseInt(reader.getText());
							log.info("GE transctionGroupCount....", geTransctionGroupCount);
							if (Objects.equals(geTransctionGroupCount, stTransGroup)) {
								log.info("Transaction count is equal..so, it is valid edi");
							} else {
								headerMap.put("validInput", "false");
								headerMap.put("ermsg", "Transaction count is not equal in GE..so, it is not valid edi");
								break;

							}
						}
					}

					// Check if segment is IEA (Interchange Control Trailer)
					if ("IEA".equals(segment)) {
						if (reader.getLocation().getElementPosition() == 2) {// IEA02 contains Number of Included Groups
							iea.setIEA01(ieaFunctionCount);
							iea.setIEA02(isa.getISA13());
							ackObj.setISA(isa);
							ackObj.setIEA(iea);
							ieaControlNumber = reader.getText();
							log.info("IEA getLocation....", ieaControlNumber);
							if (isaControlNumber != null && ieaControlNumber != null
									&& isaControlNumber.equals(ieaControlNumber)) {
								log.info("EDI message is valid. ISA and IEA Control numbers match.");
							} else {
								headerMap.put("validInput", "false");
								headerMap.put("ermsg",
										"EDI message is invalid.ISA and IEA Control numbers do not match.");
								break;
							}
						}
						if (reader.getLocation().getElementPosition() == 1) {// IEA02 contains Number of Included
																				// Groups
							ieaFunctionCount = Integer.parseInt(reader.getText());
							log.info("IEA FUNCTIONCOUNT...", ieaFunctionCount);
							if (Objects.equals(ieaFunctionCount, actualFunctionalGroupCount)) {
								log.info("Functional count is equal..so, it is valid edi");
							} else {
								headerMap.put("validInput", "false");
								headerMap.put("ermsg", "Functional count is not equal in IEA..so, it is not valid edi");
								break;

							}
						}
					}

				} else if (event == EDIStreamEvent.START_INTERCHANGE) {
					ediType = reader.getStandard();
					//headerMap.put(Constant.EDI_TYPE, ediType);
					actualFunctionalGroupCount=0;
					log.info("EDIType...", ediType);

				}
			}

		} catch (Exception e) {
			e.printStackTrace();
			log.error("Invalid EDI format");
			headerMap.put("validInput", "false");
			return new AcknowledgeObect();
		} finally {
			if (null != reader) {
				reader.close();
			}
		}
		return ackObj;
	}
	
	private String getDate() {
		//return LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
		return new SimpleDateFormat("yyMMdd").format(Calendar.getInstance().getTime());
	}
	
	private String getTime() {
		return new SimpleDateFormat("HHMM").format(Calendar.getInstance().getTime());
	}
	
	private String getRandomNumber() {
		Random rnd = new Random();
	    int number = rnd.nextInt(999999999);
	    return String.format("%09d", number);
		
	}
	private String getRandomNumberFour() {
		Random rnd = new Random();
	    int number = rnd.nextInt(9999);
	    return String.format("%04d", number);
		
	}

}