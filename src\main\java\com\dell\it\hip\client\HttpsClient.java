package com.dell.it.hip.client;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import com.dell.it.hip.util.CompressionUtil;

import reactor.netty.http.client.HttpClient;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * HttpsClient utility class for testing HTTPS message send and receive operations.
 * This class mirrors the implementation patterns used in DynamicHttpsInputAdapter 
 * and DynamicHttpsOutputHandler for compatibility with HIP services framework.
 * 
 * Supports HTTPS operations with authentication, OAuth2, compression, and various HTTP methods.
 * 
 * Usage:
 * 1. Create HttpsClient with consumer and producer configurations
 * 2. Use sendMessage() to test handler functionality
 * 3. Use receiveMessage() to test adapter functionality (via HTTP server simulation)
 * 4. Use startListener() for continuous monitoring
 */
public class HttpsClient implements AutoCloseable {
    
    private static final Logger logger = LoggerFactory.getLogger(HttpsClient.class);
    
    // Configuration for consumer (adapter testing)
    private final HttpsClientConfig consumerConfig;
    
    // Configuration for producer (handler testing)  
    private final HttpsClientConfig producerConfig;
    
    // OAuth2 token cache
    private final Map<String, TokenInfo> tokenCache = new ConcurrentHashMap<>();
    
    // Connection management
    private ExecutorService listenerExecutor;
    private final AtomicBoolean listenerRunning = new AtomicBoolean(false);
    
    // Internal token info class
    private static class TokenInfo {
        String accessToken;
        Instant expiry;
    }
    
    public HttpsClient(HttpsClientConfig consumerConfig, HttpsClientConfig producerConfig) {
        this.consumerConfig = consumerConfig;
        this.producerConfig = producerConfig;
    }
    
    /**
     * Send a message to HTTPS endpoint using the same approach as DynamicHttpsOutputHandler
     */
    public HttpsResponse sendMessage(String message) throws Exception {
        return sendMessage(message.getBytes(StandardCharsets.UTF_8));
    }
    
    /**
     * Send a byte array message to HTTPS endpoint using the same approach as DynamicHttpsOutputHandler
     */
    public HttpsResponse sendMessage(byte[] payload) throws Exception {
        return sendMessage(payload, new HashMap<>());
    }
    
    /**
     * Send a message with custom headers to HTTPS endpoint
     */
    public HttpsResponse sendMessage(byte[] payload, Map<String, String> customHeaders) throws Exception {
        logger.info("Sending message to HTTPS endpoint: {}, method: {}", 
                   producerConfig.getEndpointUrl(), producerConfig.getHttpMethod());
        
        int attempts = producerConfig.getRetryAttempts() != null ? producerConfig.getRetryAttempts() : 1;
        long backoffMs = producerConfig.getRetryBackoffMs() != null ? producerConfig.getRetryBackoffMs() : 1000L;
        
        Exception lastError = null;
        for (int i = 0; i < attempts; i++) {
            try {
                return sendHttpRequest(payload, customHeaders);
            } catch (Exception ex) {
                logger.warn("HTTPS request attempt {} failed: {}", i + 1, ex.getMessage());
                lastError = ex;
                if (i < attempts - 1) {
                    Thread.sleep(backoffMs);
                }
            }
        }
        
        throw lastError != null ? lastError : new RuntimeException("HTTPS request failed after " + attempts + " attempts");
    }
    
    /**
     * Simulate receiving a message (for adapter testing)
     * In real scenarios, this would be called by an HTTP server
     */
    public HttpsMessage receiveMessage(String requestBody, Map<String, String> headers) throws Exception {
        logger.info("Receiving HTTPS message");
        
        // Simulate authentication check
        if (!authenticate(headers)) {
            throw new SecurityException("Authentication failed");
        }
        
        // Extract headers if configured
        Map<String, String> extractedHeaders = new HashMap<>();
        if (consumerConfig.getHeadersToExtract() != null) {
            for (String headerName : consumerConfig.getHeadersToExtract()) {
                if (headers.containsKey(headerName)) {
                    extractedHeaders.put(headerName, headers.get(headerName));
                }
            }
        } else {
            extractedHeaders.putAll(headers);
        }
        
        // Check request size limit
        if (consumerConfig.getMaxRequestSizeBytes() != null && 
            requestBody.getBytes(StandardCharsets.UTF_8).length > consumerConfig.getMaxRequestSizeBytes()) {
            throw new IllegalArgumentException("Request size exceeds maximum allowed: " + consumerConfig.getMaxRequestSizeBytes());
        }
        
        logger.info("HTTPS message received successfully");
        
        return new HttpsMessage(requestBody, extractedHeaders, System.currentTimeMillis());
    }
    
    /**
     * Start a continuous listener (simulated for testing purposes)
     */
    public void startListener(MessageHandler messageHandler) throws Exception {
        if (listenerRunning.get()) {
            logger.warn("Listener is already running");
            return;
        }
        
        logger.info("Starting HTTPS listener simulation");
        
        listenerExecutor = Executors.newSingleThreadExecutor();
        listenerRunning.set(true);
        
        listenerExecutor.submit(() -> {
            while (listenerRunning.get()) {
                try {
                    // Simulate periodic message reception
                    // In real scenarios, this would be handled by an HTTP server
                    Thread.sleep(5000); // Wait 5 seconds between simulated messages
                    
                    if (listenerRunning.get()) {
                        // Simulate a received message
                        HttpsMessage simulatedMessage = new HttpsMessage(
                            "Simulated HTTPS message - " + System.currentTimeMillis(),
                            Map.of("Content-Type", "application/json", "X-Source", "simulation"),
                            System.currentTimeMillis()
                        );
                        
                        try {
                            messageHandler.handleMessage(simulatedMessage);
                        } catch (Exception ex) {
                            logger.error("Error handling simulated message: {}", ex.getMessage(), ex);
                        }
                    }
                    
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception ex) {
                    if (listenerRunning.get()) {
                        logger.error("Error in HTTPS listener: {}", ex.getMessage(), ex);
                        try {
                            Thread.sleep(5000); // Wait before retrying
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }
            }
        });
        
        logger.info("HTTPS listener started");
    }
    
    /**
     * Stop the message listener
     */
    public void stopListener() {
        if (!listenerRunning.get()) {
            return;
        }
        
        listenerRunning.set(false);
        
        if (listenerExecutor != null) {
            listenerExecutor.shutdown();
            try {
                if (!listenerExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    listenerExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                listenerExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        logger.info("HTTPS listener stopped");
    }
    
    /**
     * Wait for a specific number of messages with timeout
     */
    public List<HttpsMessage> waitForMessages(int expectedCount, long timeoutMs, MessageHandler messageHandler) throws Exception {
        CountDownLatch latch = new CountDownLatch(expectedCount);
        List<HttpsMessage> receivedMessages = Collections.synchronizedList(new ArrayList<>());
        
        startListener(message -> {
            receivedMessages.add(message);
            messageHandler.handleMessage(message);
            latch.countDown();
        });
        
        boolean received = latch.await(timeoutMs, TimeUnit.MILLISECONDS);
        stopListener();
        
        if (!received) {
            throw new RuntimeException("Did not receive expected " + expectedCount + " messages within " + timeoutMs + "ms");
        }
        
        logger.info("Successfully received {} messages", expectedCount);
        return receivedMessages;
    }
    
    /**
     * Close all connections and clean up resources
     */
    @Override
    public void close() {
        stopListener();
        tokenCache.clear();
        logger.info("HttpsClient closed");
    }

    // Private helper methods

    private HttpsResponse sendHttpRequest(byte[] payload, Map<String, String> customHeaders) throws Exception {
        // Set up client with optional connect timeout - same as DynamicHttpsOutputHandler
        HttpClient httpClient = HttpClient.create();
        if (producerConfig.getConnectTimeoutMs() != null) {
            httpClient = httpClient.option(
                    io.netty.channel.ChannelOption.CONNECT_TIMEOUT_MILLIS, producerConfig.getConnectTimeoutMs().intValue());
        }

        WebClient.Builder builder = WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient));
        if (producerConfig.getMaxInMemorySize() != null) {
            builder.codecs(codecs -> codecs.defaultCodecs().maxInMemorySize(producerConfig.getMaxInMemorySize()));
        }
        WebClient webClient = builder.build();

        // Prepare headers - same as DynamicHttpsOutputHandler
        HttpHeaders headers = new HttpHeaders();
        if (producerConfig.getHeaders() != null) {
            producerConfig.getHeaders().forEach(headers::add);
        }
        if (customHeaders != null) {
            customHeaders.forEach(headers::add);
        }
        if (producerConfig.getApiKeyHeader() != null && producerConfig.getApiKeyValue() != null) {
            headers.add(producerConfig.getApiKeyHeader(), producerConfig.getApiKeyValue());
        }
        if (Boolean.TRUE.equals(producerConfig.getCompressed())) {
            headers.add(HttpHeaders.CONTENT_ENCODING, "gzip");
        }
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);

        // OAuth2: Get token if enabled - same as DynamicHttpsOutputHandler
        if (Boolean.TRUE.equals(producerConfig.isOauthEnabled())) {
            String accessToken = getAccessToken(
                    producerConfig.getOauthTokenUrl(),
                    producerConfig.getOauthClientId(),
                    producerConfig.getOauthClientSecret(),
                    producerConfig.getOauthScope(),
                    producerConfig.getOauthAudience(),
                    producerConfig.getOauthAdditionalParams());
            if (accessToken != null) {
                headers.setBearerAuth(accessToken);
            }
        }

        // Prepare payload - same as DynamicHttpsOutputHandler
        Object finalPayload = payload;
        if (Boolean.TRUE.equals(producerConfig.getCompressed())) {
            finalPayload = CompressionUtil.compress(payload);
        }

        WebClient.RequestHeadersSpec<?> requestSpec;
        WebClient.RequestBodySpec req = webClient
                .method(HttpMethod.valueOf(producerConfig.getHttpMethod() != null ? producerConfig.getHttpMethod() : "POST"))
                .uri(producerConfig.getEndpointUrl())
                .headers(h -> h.addAll(headers));

        if (!"GET".equalsIgnoreCase(producerConfig.getHttpMethod())) {
            requestSpec = req.body(BodyInserters.fromValue(finalPayload));
        } else {
            requestSpec = req;
        }

        try {
            String responseBody = requestSpec
                    .retrieve()
                    .bodyToMono(String.class)
                    .timeout(Duration.ofMillis(producerConfig.getReadTimeoutMs() != null ? producerConfig.getReadTimeoutMs() : 30000))
                    .block();

            logger.info("HTTPS request successful");

            return new HttpsResponse(200, responseBody, new HashMap<>(), System.currentTimeMillis());

        } catch (WebClientResponseException ex) {
            logger.error("HTTPS request failed with status: {}, body: {}", ex.getStatusCode(), ex.getResponseBodyAsString());

            return new HttpsResponse(ex.getStatusCode().value(), ex.getResponseBodyAsString(),
                                   convertHeaders(ex.getHeaders()), System.currentTimeMillis());
        }
    }

    private boolean authenticate(Map<String, String> headers) {
        // API key check - same as DynamicHttpsInputAdapter
        if (consumerConfig.getApiKeyHeader() != null) {
            String suppliedKey = headers.get(consumerConfig.getApiKeyHeader());
            if (!Objects.equals(suppliedKey, consumerConfig.getApiKeyValue())) {
                logger.warn("API key authentication failed");
                return false;
            }
        }

        // OAuth2 check - same as DynamicHttpsInputAdapter
        if (consumerConfig.isOAuthRequired()) {
            String authHeader = headers.get("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                logger.warn("OAuth2 Bearer token required but not provided");
                return false;
            }
            String bearerToken = authHeader.substring(7);
            if (bearerToken.trim().isEmpty()) {
                logger.warn("Bearer token is empty");
                return false;
            }
            // Add OAuth2 validation if needed
        }

        return true;
    }

    private String getAccessToken(String tokenUrl, String clientId, String clientSecret,
                                 String scope, String audience, Map<String, String> additionalParams) {
        String cacheKey = tokenUrl + "|" + clientId + "|" + clientSecret + "|" + scope + "|" + audience;
        TokenInfo cached = tokenCache.get(cacheKey);

        if (cached != null && cached.expiry.isAfter(Instant.now().plusSeconds(60))) {
            return cached.accessToken;
        }

        try {
            WebClient webClient = WebClient.builder().build();

            Map<String, String> form = new HashMap<>();
            form.put("grant_type", "client_credentials");
            form.put("client_id", clientId);
            form.put("client_secret", clientSecret);
            if (scope != null) form.put("scope", scope);
            if (audience != null) form.put("audience", audience);
            if (additionalParams != null) form.putAll(additionalParams);

            @SuppressWarnings("unchecked")
            Map<String, Object> response = webClient.post()
                    .uri(tokenUrl)
                    .contentType(MediaType.APPLICATION_FORM_URLENCODED)
                    .bodyValue(form)
                    .retrieve()
                    .bodyToMono(Map.class)
                    .block();

            if (response != null && response.containsKey("access_token")) {
                String accessToken = response.get("access_token").toString();
                int expiresIn = Integer.parseInt(response.get("expires_in").toString());

                TokenInfo tokenInfo = new TokenInfo();
                tokenInfo.accessToken = accessToken;
                tokenInfo.expiry = Instant.now().plusSeconds(expiresIn);

                tokenCache.put(cacheKey, tokenInfo);

                return accessToken;
            }
        } catch (Exception ex) {
            logger.error("Failed to get OAuth2 access token: {}", ex.getMessage(), ex);
        }

        return null;
    }

    private Map<String, String> convertHeaders(HttpHeaders httpHeaders) {
        Map<String, String> result = new HashMap<>();
        httpHeaders.forEach((key, values) -> {
            if (!values.isEmpty()) {
                result.put(key, values.get(0));
            }
        });
        return result;
    }

    /**
     * Functional interface for handling received messages
     */
    @FunctionalInterface
    public interface MessageHandler {
        void handleMessage(HttpsMessage message);
    }

    /**
     * Represents an HTTPS message with metadata
     */
    public static class HttpsMessage {
        private final String content;
        private final Map<String, String> headers;
        private final long timestamp;

        public HttpsMessage(String content, Map<String, String> headers, long timestamp) {
            this.content = content;
            this.headers = headers != null ? new HashMap<>(headers) : new HashMap<>();
            this.timestamp = timestamp;
        }

        public String getContent() { return content; }
        public Map<String, String> getHeaders() { return new HashMap<>(headers); }
        public long getTimestamp() { return timestamp; }

        @Override
        public String toString() {
            return String.format("HttpsMessage{content='%s', headers=%s, timestamp=%d}",
                               content, headers, timestamp);
        }
    }

    /**
     * Represents an HTTPS response with metadata
     */
    public static class HttpsResponse {
        private final int statusCode;
        private final String body;
        private final Map<String, String> headers;
        private final long timestamp;

        public HttpsResponse(int statusCode, String body, Map<String, String> headers, long timestamp) {
            this.statusCode = statusCode;
            this.body = body;
            this.headers = headers != null ? new HashMap<>(headers) : new HashMap<>();
            this.timestamp = timestamp;
        }

        public int getStatusCode() { return statusCode; }
        public String getBody() { return body; }
        public Map<String, String> getHeaders() { return new HashMap<>(headers); }
        public long getTimestamp() { return timestamp; }

        public boolean isSuccessful() { return statusCode >= 200 && statusCode < 300; }

        @Override
        public String toString() {
            return String.format("HttpsResponse{statusCode=%d, body='%s', headers=%s, timestamp=%d}",
                               statusCode, body, headers, timestamp);
        }
    }
}
