package com.dell.it.hip.util.dataformatUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

public class JsonUtil {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    // Extract the root field name of a JSON object
    public static String getRootElement(String json) {
        try {
            JsonNode root = OBJECT_MAPPER.readTree(json);
            if (root.isObject() && root.fieldNames().hasNext()) {
                return root.fieldNames().next();
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    // Extract field by JSON path (simple dot notation, e.g. "foo.bar.baz")
    public static String extractField(String json, String jsonPath) {
        try {
            JsonNode node = OBJECT_MAPPER.readTree(json);
            String[] parts = jsonPath.split("\\.");
            for (String part : parts) {
                if (node == null) return null;
                node = node.get(part);
            }
            return node != null && !node.isNull() ? node.asText() : null;
        } catch (Exception e) {
            return null;
        }
    }

    // Split JSON array string into each item as string
    public static List<String> splitByJsonPath(String json, String jsonPath) throws Exception {
        if (json == null || json.isEmpty() || jsonPath == null) return Collections.emptyList();
        JsonNode root = OBJECT_MAPPER.readTree(json);
        JsonNode node = root.at(jsonPath); // For basic JSON Pointer support ("/items" etc.)
        List<String> result = new ArrayList<>();
        if (node.isArray()) {
            for (JsonNode child : node) {
                result.add(OBJECT_MAPPER.writeValueAsString(child));
            }
        } else if (node.isValueNode()) {
            result.add(node.asText());
        } else {
            // Fallback: try to treat root as array if no path given
            if (root.isArray()) {
                for (JsonNode child : root) {
                    result.add(OBJECT_MAPPER.writeValueAsString(child));
                }
            }
        }
        return result;
    }

    // Split entire JSON if it's a top-level array
    public static List<String> splitJsonArray(String json) throws Exception {
        if (json == null || json.isEmpty()) return Collections.emptyList();
        JsonNode root = OBJECT_MAPPER.readTree(json);
        List<String> result = new ArrayList<>();
        if (root.isArray()) {
            for (JsonNode child : root) {
                result.add(OBJECT_MAPPER.writeValueAsString(child));
            }
        }
        return result;
    }
    /**
     * Deserialize a JSON string to an object of the specified class.
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (Exception ex) {
            throw new RuntimeException("Failed to parse JSON to " + clazz.getSimpleName() + ": " + ex.getMessage(), ex);
        }
    }

    /**
     * Optional: Serialize an object to JSON.
     */
    public static String toJson(Object obj) {
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (Exception ex) {
            throw new RuntimeException("Failed to serialize object to JSON: " + ex.getMessage(), ex);
        }
    }
}