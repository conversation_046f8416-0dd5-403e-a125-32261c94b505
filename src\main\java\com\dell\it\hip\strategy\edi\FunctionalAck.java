package com.dell.it.hip.strategy.edi;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import lombok.Data;

@Data
@JsonPropertyOrder({"GS","ST", "AK1", "transactionSetResponse","AK9","SE","GE"})
public class FunctionalAck {
	private GS GS;
	private ST ST;
	private AK1 AK1;
    private List<TransactionSetResponse> transactionSetResponse;
    private AK9 AK9;
    private SE SE;
    private GE GE;

}