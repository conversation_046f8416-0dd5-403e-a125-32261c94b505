# Docker Environment Validation Script for TestContainers
# This script validates that <PERSON><PERSON> is properly configured for running integration tests

Write-Host "=== Docker Environment Validation for TestContainers ===" -ForegroundColor Cyan

# Check if Docker is installed
Write-Host "`nChecking Docker installation..." -ForegroundColor Yellow
try {
    $dockerVersion = docker --version 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Docker is installed: $dockerVersion" -ForegroundColor Green
    } else {
        Write-Host "✗ Docker is not installed or not in PATH" -ForegroundColor Red
        Write-Host "Please install Docker Desktop from: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "✗ Docker is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Docker Desktop from: https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
    exit 1
}

# Check if Docker daemon is running
Write-Host "`nChecking Docker daemon status..." -ForegroundColor Yellow
try {
    $dockerInfo = docker info 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Docker daemon is running" -ForegroundColor Green
    } else {
        Write-Host "✗ Docker daemon is not running" -ForegroundColor Red
        Write-Host "Please start Docker Desktop" -ForegroundColor Yellow
        exit 1
    }
} catch {
    Write-Host "✗ Cannot connect to Docker daemon" -ForegroundColor Red
    Write-Host "Please ensure Docker Desktop is running" -ForegroundColor Yellow
    exit 1
}

# Check Docker connectivity
Write-Host "`nTesting Docker connectivity..." -ForegroundColor Yellow
try {
    docker ps 2>&1 | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Docker connectivity test passed" -ForegroundColor Green
    } else {
        Write-Host "✗ Docker connectivity test failed" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "✗ Docker connectivity test failed" -ForegroundColor Red
    exit 1
}

# Test pulling a small image (for TestContainers)
Write-Host "`nTesting Docker image pull capability..." -ForegroundColor Yellow
try {
    docker pull hello-world:latest 2>&1 | Out-Null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Docker image pull test passed" -ForegroundColor Green
        # Clean up
        docker rmi hello-world:latest 2>&1 | Out-Null
    } else {
        Write-Host "✗ Docker image pull test failed" -ForegroundColor Red
        Write-Host "This might indicate network connectivity issues" -ForegroundColor Yellow
    }
} catch {
    Write-Host "✗ Docker image pull test failed" -ForegroundColor Red
}

# Check available resources
Write-Host "`nChecking Docker resources..." -ForegroundColor Yellow
try {
    $dockerStats = docker system df 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ Docker system resources:" -ForegroundColor Green
        Write-Host $dockerStats
    } else {
        Write-Host "⚠ Could not retrieve Docker system information" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠ Could not retrieve Docker system information" -ForegroundColor Yellow
}

Write-Host "`n=== Docker Environment Validation Complete ===" -ForegroundColor Cyan
Write-Host "✓ Docker environment is ready for TestContainers integration tests" -ForegroundColor Green
Write-Host "`nYou can now run integration tests with:" -ForegroundColor White
Write-Host "  mvn test -Dtest=RedisIntegrationTest" -ForegroundColor Cyan
Write-Host "  mvn test -Dtest=MessageFlowIntegrationTest" -ForegroundColor Cyan
Write-Host "  mvn verify  # Run all integration tests" -ForegroundColor Cyan
