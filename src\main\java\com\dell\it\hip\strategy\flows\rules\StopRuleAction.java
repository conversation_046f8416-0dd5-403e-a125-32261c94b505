package com.dell.it.hip.strategy.flows.rules;

import java.util.Map;

import org.springframework.messaging.Message;

import com.dell.it.hip.config.HIPIntegrationDefinition;
import com.dell.it.hip.config.rules.Rule;

public class StopRuleAction implements RuleAction {
    @Override
    public String getName() {
        return "stop";
    }

    @Override
    public String getType() {
        return "stop";
    }

    @Override
    public Message<?> performAction(Message<?> message, Map<String, Object> params, Rule rule, HIPIntegrationDefinition def, Map<String, Object> context) {
        context.put("stopRuleProcessing", Boolean.TRUE);
        return message;
    }
}