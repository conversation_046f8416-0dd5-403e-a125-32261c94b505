package com.dell.it.hip.core.repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import com.dell.it.hip.config.HIPIntegrationRequestEntity;
import com.fasterxml.jackson.databind.ObjectMapper;

@Component("redisStore") 
@ConditionalOnProperty(name = "integration.store.type", havingValue = "REDIS")
public class HIPIntegrationDefinitionRedisStore implements HIPIntegrationDefinitionStore {
	
	@Autowired 
	private StringRedisTemplate redisTemplate;
    private static final String PREFIX = "hip_integration:";
    private String key(String serviceManager, String name, String version) {
        return PREFIX + serviceManager + ":" + name + ":" + version;
    }
    @Override
    public void save(HIPIntegrationRequestEntity entity) {
        String redisKey = key(entity.getServiceManagerName(), entity.getHipIntegrationName(), entity.getVersion());
        try {
            String value = new ObjectMapper().writeValueAsString(entity);
            redisTemplate.opsForValue().set(redisKey, value);
        } catch (Exception e) {
            throw new RuntimeException("Failed to save to Redis", e);
        }
    }
    @Override
    public HIPIntegrationRequestEntity find(String serviceManagerName, String hipIntegrationName, String version) {
        String redisKey = key(serviceManagerName, hipIntegrationName, version);
        String value = redisTemplate.opsForValue().get(redisKey);
        if (value == null) return null;
        try {
            return new ObjectMapper().readValue(value, HIPIntegrationRequestEntity.class);
        } catch (Exception e) {
            throw new RuntimeException("Failed to deserialize from Redis", e);
        }
    }
    @Override
    public boolean exists(String serviceManagerName, String hipIntegrationName, String version) {
        String redisKey = key(serviceManagerName, hipIntegrationName, version);
        return Boolean.TRUE.equals(redisTemplate.hasKey(redisKey));
    }
    @Override
    public List<HIPIntegrationRequestEntity> findByServiceManagerName(String serviceManagerName) {
        Set<String> keys = redisTemplate.keys(PREFIX + serviceManagerName + ":*");
        if (keys == null || keys.isEmpty()) return Collections.emptyList();
        List<HIPIntegrationRequestEntity> out = new ArrayList<>();
        for (String k : keys) {
            String value = redisTemplate.opsForValue().get(k);
            if (value != null) {
                try {
                    out.add(new ObjectMapper().readValue(value, HIPIntegrationRequestEntity.class));
                } catch (Exception ignored) {}
            }
        }
        return out;
    }
	@Override
	public void deleteByServiceManagerNameAndHipIntegrationNameAndVersion(String serviceManagerName,
			String hipIntegrationName, String version) {
		 String redisKey = key(serviceManagerName, hipIntegrationName, version);
	        redisTemplate.delete(redisKey);
		
	}

}
