package com.dell.it.hip.util.adapters;

import java.util.HashMap;
import java.util.Map;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

import com.dell.it.hip.config.adapters.DynamicKafkaAdapterConfig;

public class KafkaAdapterUtil {

    /** Build a ConsumerFactory using the adapter config and HIPIntegrationContext properties */
    public static ConsumerFactory<String, byte[]> buildConsumerFactory(DynamicKafkaAdapterConfig cfg, Map<String, Object> propsFromContext) {
        Map<String, Object> props = new HashMap<>();
        // Merge context properties (from config server/property sheets)
        if (propsFromContext != null) props.putAll(propsFromContext);

        // Adapter-specific overrides
        props.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, cfg.getBootstrapServers());
        props.put(ConsumerConfig.GROUP_ID_CONFIG, cfg.getGroupId());
        props.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, ByteArrayDeserializer.class);
        props.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, ByteArrayDeserializer.class);
        props.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
        // Add more as needed (e.g., security, custom props)

        return new DefaultKafkaConsumerFactory<>(props);
    }

/** Convert a ConsumerRecord to a Spring Message, mapping headers and metadata */
public static Message<byte[]> toMessage(ConsumerRecord<String, byte[]> record, String integrationId) {
    if (record == null) {
        throw new IllegalArgumentException("ConsumerRecord cannot be null");
    }
    if (integrationId == null || integrationId.isEmpty()) {
        throw new IllegalArgumentException("Integration ID cannot be null or empty");
    }
    
    Map<String, Object> headers = KafkaHeaderMapperUtil.extract(record.headers());
    headers.put("HIPIntegrationDefinitionId", integrationId);
    headers.put("kafka_offset", record.offset());
    headers.put("kafka_partition", record.partition());
    // Add record timestamp if available
    if (record.timestamp() > 0) {
        headers.put("kafka_timestamp", record.timestamp());
    }

    return MessageBuilder.withPayload(record.value())
            .copyHeaders(headers)
            .build();
}
}