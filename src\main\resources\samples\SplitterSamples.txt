EDI X12 Splitter
{
  "stepType": "splitter",
  "propertyRef": "splitterStep1",
  "inputFormat": "EDI_X12",
  "splitX12": true,
  "x12SplitLevel": "TRANSACTION",     // or "GROUP", "INTERCHANGE"
  "allowMultipleInterchanges": true,
  "x12SegmentDelimiter": "~",         // Optional: override detected
  "x12ElementDelimiter": "*",         // Optional: override detected
  "x12SubElementDelimiter": ":"       // Optional
  "copyHeaders": true
}
EDI EDIFACT Splitter
{
  "stepType": "splitter",
  "propertyRef": "splitterStep2",
  "inputFormat": "EDI_EDIFACT",
  "splitEdifact": true,
  "edifactSplitLevel": "MESSAGE",      // or "GROUP", "INTERCHANGE"
  "allowMultipleEdifactInterchanges": true,
  "edifactSegmentDelimiter": "'",      // Optional: override detected
  "edifactElementDelimiter": "+",      // Optional: override detected
  "edifactSubElementDelimiter": ":"    // Optional
}
XML Splitter
{
  "stepType": "splitter",
  "propertyRef": "splitterStep3",
  "inputFormat": "XML",
  "splitXml": true,
  "xmlXPathExpression": "//Invoice"    // XPath to the node to split by (e.g., each <Invoice>)
}

CSV Splitter
{
  "stepType": "splitter",
  "propertyRef": "splitterStep4",
  "inputFormat": "CSV",
  "splitCsvLines": true
}
JSON Array Splitter
{
  "stepType": "splitter",
  "propertyRef": "splitterStep5",
  "inputFormat": "JSON",
  "splitJsonArray": true,
  "jsonPathExpression": "$.orders[*]"   // JSONPath to the array elements to split by
}
Generic Regex Splitter
{
  "stepType": "splitter",
  "propertyRef": "splitterStep6",
  "genericSplitting": true,
  "splitterRegex": "\\n\\n+"      // e.g., split on double newlines, or use any regex as required
}
