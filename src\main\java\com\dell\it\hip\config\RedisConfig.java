package com.dell.it.hip.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Configuration
public class RedisConfig {

    private static final Logger logger = LoggerFactory.getLogger(RedisConfig.class);

    @Value("${spring.data.redis.enabled:true}")
    private boolean redisEnabled;

    /**
     * StringRedisTemplate - only created when Redis is enabled.
     */
    @Bean
    @ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
        logger.info("Creating StringRedisTemplate with Redis enabled: {}", redisEnabled);
        try {
            StringRedisTemplate template = new StringRedisTemplate(redisConnectionFactory);
            // Test the connection
            template.getConnectionFactory().getConnection().ping();
            logger.info("Redis connection test successful");
            return template;
        } catch (Exception e) {
            logger.error("Failed to create StringRedisTemplate: {}", e.getMessage(), e);
            throw new RuntimeException("Redis connection failed", e);
        }
    }

    /**
     * Redis Message Listener Container - only created when Redis is enabled.
     */
    @Bean
    @ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
    public RedisMessageListenerContainer redisMessageListenerContainer(RedisConnectionFactory redisConnectionFactory) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(redisConnectionFactory);
        return container;
    }

	@Bean
	@ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
	public RedisTemplate<String, HIPClusterEvent> hipEventRedisTemplate(RedisConnectionFactory factory) {
		logger.info("Creating HIPClusterEvent RedisTemplate");
		RedisTemplate<String, HIPClusterEvent> template = new RedisTemplate<>();
		template.setConnectionFactory(factory);
		template.setKeySerializer(new StringRedisSerializer());
		template.setValueSerializer(new Jackson2JsonRedisSerializer<>(HIPClusterEvent.class));
		template.afterPropertiesSet();
		return template;
	}
}