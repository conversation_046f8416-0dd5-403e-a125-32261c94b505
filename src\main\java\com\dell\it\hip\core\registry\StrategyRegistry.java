package com.dell.it.hip.core.registry;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.dell.it.hip.strategy.adapters.InputAdapterStrategy;
import com.dell.it.hip.strategy.flows.FlowStepStrategy;
import com.dell.it.hip.strategy.handlers.HandlerStrategy;

/**
 * Registry for all input/output adapter, flowstep, and handler strategies and their config class mapping.
 * ConfigClassRegistry allows runtime type lookup for refs.
 */
@Component
public class StrategyRegistry {

    private final Map<String, InputAdapterStrategy> adapterStrategies = new ConcurrentHashMap<>();
    private final Map<String, FlowStepStrategy> stepStrategies = new ConcurrentHashMap<>();
    private final Map<String, HandlerStrategy> handlerStrategies = new ConcurrentHashMap<>();
    private final ConfigClassRegistry configClassRegistry;

    @Autowired
    public StrategyRegistry(
            Map<String, InputAdapterStrategy> adapterStrategyMap,
            Map<String, FlowStepStrategy> stepStrategyMap,
            Map<String, HandlerStrategy> handlerStrategyMap,
            ConfigClassRegistry configClassRegistry
    ) {
        // Each @Component("name") strategy is picked up by Spring as a bean in the maps
        this.adapterStrategies.putAll(adapterStrategyMap);
        this.stepStrategies.putAll(stepStrategyMap);
        this.handlerStrategies.putAll(handlerStrategyMap);
        this.configClassRegistry = configClassRegistry;
    }

    public Map<String, InputAdapterStrategy> getAdapterStrategies() {
        return adapterStrategies;
    }

    public Map<String, FlowStepStrategy> getStepStrategies() {
        return stepStrategies;
    }

    public Map<String, HandlerStrategy> getHandlerStrategies() {
        return handlerStrategies;
    }

    public InputAdapterStrategy getAdapterStrategy(String type) {
        return adapterStrategies.get(type);
    }

    public FlowStepStrategy getStepStrategy(String type) {
        return stepStrategies.get(type);
    }

    public HandlerStrategy getHandlerStrategy(String type) {
        return handlerStrategies.get(type);
    }

    public ConfigClassRegistry getConfigClassRegistry() {
        return configClassRegistry;
    }
}