package com.dell.it.hip.client;

import jcifs.CIFSContext;
import jcifs.config.PropertyConfiguration;
import jcifs.context.BaseContext;
import jcifs.smb.NtlmPasswordAuthenticator;
import jcifs.smb.SmbFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.dell.it.hip.util.CompressionUtil;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Pattern;

/**
 * NasClient utility class for testing NAS message send and receive operations.
 * This class mirrors the implementation patterns used in DynamicNasInputAdapter 
 * and DynamicNasOutputHandler for compatibility with HIP services framework.
 * 
 * Supports both NFS and SMB protocols for network-attached storage operations.
 * 
 * Usage:
 * 1. Create NasClient with consumer and producer configurations
 * 2. Use sendMessage() to test handler functionality
 * 3. Use receiveMessage() to test adapter functionality
 * 4. Use startListener() for continuous file monitoring
 */
public class NasClient implements AutoCloseable {
    
    private static final Logger logger = LoggerFactory.getLogger(NasClient.class);
    
    // Configuration for consumer (adapter testing)
    private final NasClientConfig consumerConfig;
    
    // Configuration for producer (handler testing)  
    private final NasClientConfig producerConfig;
    
    // Connection management
    private ExecutorService listenerExecutor;
    private final AtomicBoolean listenerRunning = new AtomicBoolean(false);
    
    public NasClient(NasClientConfig consumerConfig, NasClientConfig producerConfig) {
        this.consumerConfig = consumerConfig;
        this.producerConfig = producerConfig;
    }
    
    /**
     * Send a message to NAS using the same approach as DynamicNasOutputHandler
     */
    public void sendMessage(String fileName, String message) throws Exception {
        sendMessage(fileName, message.getBytes(StandardCharsets.UTF_8));
    }
    
    /**
     * Send a byte array message to NAS using the same approach as DynamicNasOutputHandler
     */
    public void sendMessage(String fileName, byte[] payload) throws Exception {
        logger.info("Sending message to NAS protocol: {}, file: {}", 
                   producerConfig.getProtocol(), fileName);
        
        // Apply compression if enabled
        byte[] finalPayload = Boolean.TRUE.equals(producerConfig.getGzipEnabled())
                ? CompressionUtil.compress(payload)
                : payload;
        
        String protocol = producerConfig.getProtocol();
        if ("smb".equalsIgnoreCase(protocol)) {
            handleSmbWrite(fileName, finalPayload);
        } else if ("nfs".equalsIgnoreCase(protocol)) {
            handleNfsWrite(fileName, finalPayload);
        } else {
            throw new IllegalArgumentException("Unsupported NAS protocol: " + protocol);
        }
        
        logger.info("NAS upload successful: {}", fileName);
    }
    
    /**
     * Receive a single file from NAS using the same approach as DynamicNasInputAdapter
     */
    public NasMessage receiveMessage() throws Exception {
        return receiveMessage(null);
    }
    
    /**
     * Receive a single file from NAS with optional file pattern filter
     */
    public NasMessage receiveMessage(String filePattern) throws Exception {
        logger.info("Receiving message from NAS protocol: {}", consumerConfig.getProtocol());
        
        String protocol = consumerConfig.getProtocol();
        String pattern = filePattern != null ? filePattern : 
                        consumerConfig.getFileNamePattern() != null ? consumerConfig.getFileNamePattern() : ".*";
        Pattern regex = Pattern.compile(pattern);
        
        if ("smb".equalsIgnoreCase(protocol)) {
            if (consumerConfig.getMountPath() != null && new File(consumerConfig.getMountPath()).exists()) {
                return pollLocalFiles(consumerConfig.getMountPath(), regex);
            } else {
                return pollRemoteSmbFiles(regex);
            }
        } else if ("nfs".equalsIgnoreCase(protocol)) {
            return pollLocalFiles(consumerConfig.getRemoteDirectory(), regex);
        } else {
            throw new IllegalArgumentException("Unsupported NAS protocol: " + protocol);
        }
    }
    
    /**
     * Start a continuous file listener using the same approach as DynamicNasInputAdapter
     */
    public void startListener(MessageHandler messageHandler) throws Exception {
        if (listenerRunning.get()) {
            logger.warn("Listener is already running");
            return;
        }
        
        logger.info("Starting NAS listener for protocol: {}", consumerConfig.getProtocol());
        
        listenerExecutor = Executors.newSingleThreadExecutor();
        listenerRunning.set(true);
        
        listenerExecutor.submit(() -> {
            while (listenerRunning.get()) {
                try {
                    NasMessage message = receiveMessage();
                    if (message != null) {
                        try {
                            messageHandler.handleMessage(message);
                        } catch (Exception ex) {
                            logger.error("Error handling message: {}", ex.getMessage(), ex);
                        }
                    }
                    
                    // Wait for polling interval
                    Thread.sleep(consumerConfig.getPollingIntervalMs());
                    
                } catch (InterruptedException ex) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception ex) {
                    if (listenerRunning.get()) {
                        logger.error("Error in NAS listener: {}", ex.getMessage(), ex);
                        try {
                            Thread.sleep(5000); // Wait before retrying
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }
            }
        });
        
        logger.info("NAS listener started");
    }
    
    /**
     * Stop the message listener
     */
    public void stopListener() {
        if (!listenerRunning.get()) {
            return;
        }
        
        listenerRunning.set(false);
        
        if (listenerExecutor != null) {
            listenerExecutor.shutdown();
            try {
                if (!listenerExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    listenerExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                listenerExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        logger.info("NAS listener stopped");
    }
    
    /**
     * Wait for a specific number of files with timeout
     */
    public List<NasMessage> waitForMessages(int expectedCount, long timeoutMs, MessageHandler messageHandler) throws Exception {
        CountDownLatch latch = new CountDownLatch(expectedCount);
        List<NasMessage> receivedMessages = Collections.synchronizedList(new ArrayList<>());
        
        startListener(message -> {
            receivedMessages.add(message);
            messageHandler.handleMessage(message);
            latch.countDown();
        });
        
        boolean received = latch.await(timeoutMs, TimeUnit.MILLISECONDS);
        stopListener();
        
        if (!received) {
            throw new RuntimeException("Did not receive expected " + expectedCount + " files within " + timeoutMs + "ms");
        }
        
        logger.info("Successfully received {} files", expectedCount);
        return receivedMessages;
    }
    
    /**
     * Close all connections and clean up resources
     */
    @Override
    public void close() {
        stopListener();
        logger.info("NasClient closed");
    }
    
    // Private helper methods
    
    private void handleSmbWrite(String fileName, byte[] payload) throws Exception {
        // JCIFS-NG context setup - same as DynamicNasOutputHandler
        Properties prop = new Properties();
        prop.setProperty("jcifs.smb.client.enableSMB2", "true");
        CIFSContext base = new BaseContext(new PropertyConfiguration(prop));
        CIFSContext context = base.withCredentials(
                new NtlmPasswordAuthenticator(
                        producerConfig.getDomain() != null ? producerConfig.getDomain() : "",
                        producerConfig.getUsername(),
                        producerConfig.getPassword() != null ? producerConfig.getPassword() : ""
                )
        );
        
        // smb://host/share/dir/file
        String smbPath = "smb://" + producerConfig.getHost() + "/" + producerConfig.getShareName() + "/" +
                (producerConfig.getRemoteDirectory() != null && !producerConfig.getRemoteDirectory().isEmpty() ?
                        producerConfig.getRemoteDirectory().replace("\\", "/").replaceAll("^/+", "").replaceAll("/+$", "") + "/" : "")
                + fileName;
        
        SmbFile file = new SmbFile(smbPath, context);
        
        if (file.exists()) {
            throw new RuntimeException("SMB file already exists: " + smbPath);
        }
        
        // Write file
        try (OutputStream os = file.getOutputStream()) {
            os.write(payload);
        }
    }
    
    private void handleNfsWrite(String fileName, byte[] payload) throws Exception {
        String dir = producerConfig.getRemoteDirectory() != null ? producerConfig.getRemoteDirectory() : ".";
        String nfsFilePath = dir.endsWith(producerConfig.getFileSeparator()) ? 
                           dir + fileName : dir + producerConfig.getFileSeparator() + fileName;
        Path filePath = Paths.get(nfsFilePath);
        
        if (Files.exists(filePath)) {
            throw new RuntimeException("NFS file already exists: " + nfsFilePath);
        }
        
        Files.write(filePath, payload, StandardOpenOption.CREATE_NEW, StandardOpenOption.WRITE);
    }

    private NasMessage pollLocalFiles(String directory, Pattern filePattern) throws Exception {
        File dir = new File(directory);
        if (!dir.exists() || !dir.isDirectory()) {
            logger.warn("NAS directory does not exist: {}", directory);
            return null;
        }

        File[] files = dir.listFiles();
        if (files == null || files.length == 0) {
            return null;
        }

        // Sort files based on configuration
        List<File> fileList = Arrays.asList(files);
        if ("OLDEST".equalsIgnoreCase(consumerConfig.getFileSortOrder())) {
            fileList.sort(Comparator.comparing(File::lastModified));
        } else if ("NEWEST".equalsIgnoreCase(consumerConfig.getFileSortOrder())) {
            fileList.sort(Comparator.comparing(File::lastModified).reversed());
        }

        for (File file : fileList) {
            if (!file.isFile() || !filePattern.matcher(file.getName()).matches()) {
                continue;
            }

            // Apply file age filter
            if (consumerConfig.getFileAgeMs() != null) {
                long fileAge = System.currentTimeMillis() - file.lastModified();
                if (fileAge < consumerConfig.getFileAgeMs()) {
                    continue;
                }
            }

            // Skip hidden files if configured
            if (Boolean.TRUE.equals(consumerConfig.getIgnoreHiddenFiles()) && file.isHidden()) {
                continue;
            }

            try {
                // Read file content
                byte[] content = Files.readAllBytes(file.toPath());

                // Apply decompression if needed (assuming files might be compressed)
                // Note: The adapter doesn't have a compressed flag, but we'll check for gzip magic bytes
                if (content.length > 2 && content[0] == (byte) 0x1f && content[1] == (byte) 0x8b) {
                    content = CompressionUtil.decompress(content);
                }

                String charset = consumerConfig.getCharset() != null ? consumerConfig.getCharset() : "UTF-8";
                String fileContent = new String(content, charset);

                // Handle post-processing
                handlePostProcess(file);

                logger.info("NAS file received successfully: {}", file.getName());

                return new NasMessage(fileContent, file.getName(), file.length(),
                                    file.lastModified(), new HashMap<>());

            } catch (Exception ex) {
                logger.error("Failed to process file {}: {}", file.getName(), ex.getMessage(), ex);
                continue;
            }
        }

        return null;
    }

    private NasMessage pollRemoteSmbFiles(Pattern filePattern) throws Exception {
        // JCIFS-NG context setup - same as DynamicNasInputAdapter
        Properties prop = new Properties();
        prop.setProperty("jcifs.smb.client.enableSMB2", "true");
        CIFSContext base = new BaseContext(new PropertyConfiguration(prop));
        CIFSContext context = base.withCredentials(
                new NtlmPasswordAuthenticator(
                        consumerConfig.getDomain() != null ? consumerConfig.getDomain() : "",
                        consumerConfig.getUsername(),
                        consumerConfig.getPassword() != null ? consumerConfig.getPassword() : ""
                )
        );

        String smbPath = "smb://" + consumerConfig.getHost() + "/" + consumerConfig.getShareName() + "/" +
                (consumerConfig.getRemoteDirectory() != null ? consumerConfig.getRemoteDirectory() + "/" : "");
        SmbFile dir = new SmbFile(smbPath, context);

        if (!dir.exists() || !dir.isDirectory()) {
            logger.warn("NAS SMB directory does not exist: {}", smbPath);
            return null;
        }

        SmbFile[] files = dir.listFiles();
        if (files == null || files.length == 0) {
            return null;
        }

        // Sort files based on configuration
        List<SmbFile> fileList = Arrays.asList(files);
        if ("OLDEST".equalsIgnoreCase(consumerConfig.getFileSortOrder())) {
            fileList.sort(Comparator.comparing(SmbFile::getLastModified));
        } else if ("NEWEST".equalsIgnoreCase(consumerConfig.getFileSortOrder())) {
            fileList.sort(Comparator.comparing(SmbFile::getLastModified).reversed());
        }

        int maxFilesPerPoll = consumerConfig.getMaxFilesPerPoll() != null ? consumerConfig.getMaxFilesPerPoll() : 100;
        int processed = 0;

        for (SmbFile file : fileList) {
            if (!file.isFile() || !filePattern.matcher(file.getName()).matches()) {
                continue;
            }
            if (processed >= maxFilesPerPoll) {
                break;
            }

            // Skip hidden files if configured
            if (Boolean.TRUE.equals(consumerConfig.getIgnoreHiddenFiles()) && file.isHidden()) {
                continue;
            }

            // Apply file age filter
            if (consumerConfig.getFileAgeMs() != null) {
                long fileAge = System.currentTimeMillis() - file.getLastModified();
                if (fileAge < consumerConfig.getFileAgeMs()) {
                    continue;
                }
            }

            try (InputStream is = file.getInputStream()) {
                byte[] content = is.readAllBytes();

                // Apply decompression - same as adapter
                content = CompressionUtil.decompress(content);

                String charset = consumerConfig.getCharset() != null ? consumerConfig.getCharset() : "UTF-8";
                String fileContent = new String(content, charset);

                // Handle post-processing
                handlePostProcessSmb(file);

                logger.info("SMB file received successfully: {}", file.getName());

                return new NasMessage(fileContent, file.getName(), file.length(),
                                    file.getLastModified(), new HashMap<>());

            } catch (Exception ex) {
                logger.error("Failed to process SMB file {}: {}", file.getName(), ex.getMessage(), ex);
                continue;
            }
        }

        return null;
    }

    private void handlePostProcess(File file) {
        try {
            String action = consumerConfig.getPostProcessAction();
            if ("delete".equalsIgnoreCase(action)) {
                Files.delete(file.toPath());
                logger.info("Deleted NAS file: {}", file.getName());
            } else if ("rename".equalsIgnoreCase(action)) {
                String newFileName = (consumerConfig.getRenamePattern() != null)
                        ? consumerConfig.getRenamePattern().replace("{file}", file.getName())
                        : file.getName() + ".processed";
                Path newPath = file.toPath().getParent().resolve(newFileName);
                Files.move(file.toPath(), newPath);
                logger.info("Renamed NAS file: {} -> {}", file.getName(), newFileName);
            }
        } catch (Exception ex) {
            logger.warn("Post-process failed for file {}: {}", file.getName(), ex.getMessage());
        }
    }

    private void handlePostProcessSmb(SmbFile file) {
        try {
            String action = consumerConfig.getPostProcessAction();
            if ("delete".equalsIgnoreCase(action)) {
                file.delete();
                logger.info("Deleted SMB file: {}", file.getName());
            } else if ("rename".equalsIgnoreCase(action)) {
                String newFileName = (consumerConfig.getRenamePattern() != null)
                        ? consumerConfig.getRenamePattern().replace("{file}", file.getName())
                        : file.getName() + ".processed";
                String parentUrl = file.getParent();
                SmbFile parent = new SmbFile(parentUrl, file.getContext());
                SmbFile newFile = new SmbFile(parent, newFileName);
                file.renameTo(newFile);
                logger.info("Renamed SMB file: {} -> {}", file.getName(), newFileName);
            }
        } catch (Exception ex) {
            logger.warn("Post-process failed for SMB file {}: {}", file.getName(), ex.getMessage());
        }
    }

    /**
     * Functional interface for handling received messages
     */
    @FunctionalInterface
    public interface MessageHandler {
        void handleMessage(NasMessage message);
    }

    /**
     * Represents a NAS message with metadata
     */
    public static class NasMessage {
        private final String content;
        private final String fileName;
        private final long fileSize;
        private final long lastModified;
        private final Map<String, String> headers;

        public NasMessage(String content, String fileName, long fileSize, long lastModified, Map<String, String> headers) {
            this.content = content;
            this.fileName = fileName;
            this.fileSize = fileSize;
            this.lastModified = lastModified;
            this.headers = headers != null ? new HashMap<>(headers) : new HashMap<>();
        }

        public String getContent() { return content; }
        public String getFileName() { return fileName; }
        public long getFileSize() { return fileSize; }
        public long getLastModified() { return lastModified; }
        public Map<String, String> getHeaders() { return new HashMap<>(headers); }

        @Override
        public String toString() {
            return String.format("NasMessage{fileName='%s', fileSize=%d, lastModified=%d, content='%s', headers=%s}",
                               fileName, fileSize, lastModified, content, headers);
        }
    }
}
