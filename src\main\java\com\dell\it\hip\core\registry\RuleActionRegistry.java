package com.dell.it.hip.core.registry;

import org.springframework.stereotype.Component;

import com.dell.it.hip.config.rules.RuleActionDescriptor;
import com.dell.it.hip.strategy.flows.rules.FlowResponseAction;
import com.dell.it.hip.strategy.flows.rules.FlowTargetsResponseAction;
import com.dell.it.hip.strategy.flows.rules.RuleAction;
import com.dell.it.hip.strategy.flows.rules.StopRuleAction;
@Component
public class RuleActionRegistry {

    public RuleAction createAction(RuleActionDescriptor desc) {
        switch (desc.getType()) {
			case "Route Document":
            case "FlowResponse":
                return new FlowResponseAction();
            case "FlowTargetsResponse":
                return new FlowTargetsResponseAction();
            case "Stop":
            case "stop-step":
                return new StopRuleAction();
            // Add more types as needed
            default:
                throw new IllegalArgumentException("Unknown RuleAction type: " + desc.getType());
        }
    }
}