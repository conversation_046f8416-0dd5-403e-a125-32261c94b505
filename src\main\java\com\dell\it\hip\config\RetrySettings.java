package com.dell.it.hip.config;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RetrySettings {
    private int maxAttempts = 3;
    private long backOffPeriodMs = 1000;

    // getters and setters
    public int getMaxAttempts() { return maxAttempts; }
    public void setMaxAttempts(int maxAttempts) { this.maxAttempts = maxAttempts; }
    public long getBackOffPeriodMs() { return backOffPeriodMs; }
    public void setBackOffPeriodMs(long backOffPeriodMs) { this.backOffPeriodMs = backOffPeriodMs; }
}