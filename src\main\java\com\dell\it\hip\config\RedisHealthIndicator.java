package com.dell.it.hip.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.stereotype.Component;

/**
 * Custom Redis Health Indicator to provide better diagnostics for Redis connectivity issues.
 */
@Component
@ConditionalOnProperty(name = "spring.data.redis.enabled", havingValue = "true")
public class RedisHealthIndicator implements HealthIndicator {

    private static final Logger logger = LoggerFactory.getLogger(RedisHealthIndicator.class);

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    @Override
    public Health health() {
        try {
            // Test Redis connection
            var connection = redisConnectionFactory.getConnection();
            
            // Ping Redis server
            String pong = connection.ping();
            connection.close();
            
            logger.debug("Redis health check successful: {}", pong);
            
            return Health.up()
                    .withDetail("redis", "Available")
                    .withDetail("ping", pong)
                    .withDetail("connectionFactory", redisConnectionFactory.getClass().getSimpleName())
                    .build();
                    
        } catch (Exception e) {
            logger.error("Redis health check failed: {}", e.getMessage(), e);
            
            return Health.down()
                    .withDetail("redis", "Unavailable")
                    .withDetail("error", e.getMessage())
                    .withDetail("errorType", e.getClass().getSimpleName())
                    .withException(e)
                    .build();
        }
    }
}
