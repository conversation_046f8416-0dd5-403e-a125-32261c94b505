package com.dell.it.hip.util.redis;

public class HIPRedisKeyUtil {

    // ---- Cluster-wide control/pubsub topics ----

    // Single topic per ServiceManager for all cluster control events
    public static String clusterControlTopic(String serviceManagerName) {
        return "hip:runtime:cluster:control:" + serviceManagerName;
    }

    // ---- Adapter pause/resume key ----

    public static String adapterPauseKey(String serviceManagerName, String integrationId, String integrationVersion, String adapterId) {
        return "hip:runtime:pause:" + serviceManagerName + ":" + integrationId + ":" + integrationVersion + ":" + adapterId;
    }

    // ---- Throttle settings key ----

    public static String throttleKey(String serviceManagerName, String integrationId, String integrationVersion, String adapterId) {
        return "hip:runtime:throttle:" + serviceManagerName + ":" + integrationId + ":" + integrationVersion + ":" + adapterId;
    }

    public static String statusKey(String serviceManagerName, String integrationName, String integrationVersion) {
        return String.format("hip:runtime:%s:status:%s:%s", serviceManagerName, integrationName, integrationVersion);
    }

    // ---- Rate limit key ----
    public static String rateLimitKey(String serviceManagerName, String integrationId, String integrationVersion, String adapterId) {
        return "hip:runtime:ratelimit:" + serviceManagerName + ":" + integrationId + ":" + integrationVersion + ":" + adapterId;
    }

    // ---- Deduplication key (adapter scope, use with ":dedupId" appended) ----
    public static String dedupKey(String serviceManagerName, String integrationId, String integrationVersion, String adapterId) {
        return "hip:runtime:dedup:" + serviceManagerName + ":" + integrationId + ":" + integrationVersion + ":" + adapterId;
    }

    // ---- Wiretap/Log ----

    public static String wiretapKey(String serviceManagerName, String integrationId, String integrationVersion, String adapterType) {
        return "hip:runtime:wiretap:" + serviceManagerName + ":" + integrationId + ":" + integrationVersion + ":" + adapterType;
    }

    // ---- Aggregator/Aggregation Groups ----

    public static String aggregationGroupKey(String serviceManagerName, String integrationId, String integrationVersion, String groupId) {
        return "hip:runtime:aggregation:" + serviceManagerName + ":" + integrationId + ":" + integrationVersion + ":" + groupId;
    }

    // ---- OpenTelemetry Context ----

    public static String traceContextKey(String serviceManagerName, String integrationId, String integrationVersion, String messageId) {
        return "hip:runtime:trace:" + serviceManagerName + ":" + integrationId + ":" + integrationVersion + ":" + messageId;
    }

    /*public static String flowRoutingRulePrefix(String serviceManagerName, String integrationName, String version) {
        return serviceManagerName + ":" + integrationName + ":" + version + ":flowrule:";
    }*/
    // ---- Schema Cache ----

    public static String schemaCacheKey(String serviceManagerName, String integrationId, String integrationVersion, String schemaType) {
        return "hip:config:schema:" + serviceManagerName + ":" + integrationId + ":" + integrationVersion + ":" + schemaType;
    }

    // ---- SFTP Force Poll (if still required) ----
    public static String clusterSftpForcePollTopic(String integrationId, String integrationVersion, String adapterId) {
        return "hip:runtime:cluster:sftp:forcepoll:" + integrationId + ":" + integrationVersion + ":" + adapterId;
    }
    //-- docType Cache ----
    public static String docTypeKey(String docType, String version) {
        return "hip:config:doctype:" + docType + (version != null ? ":" + version : "");
    }
    public static String docTypeKey(String docTypeVersionKey) {
        // docTypeVersionKey = "INVOICE:1.0" or just "INVOICE"
        if (docTypeVersionKey.startsWith("doctype:")) return docTypeVersionKey;
        return "hip:config:doctype:" + docTypeVersionKey;
    }

    // ---- Custom/Misc ----
    public static String customKey(String prefix, String serviceManagerName, String integrationId, String integrationVersion, String... extra) {
        StringBuilder sb = new StringBuilder(prefix)
                .append(":").append(serviceManagerName)
                .append(":").append(integrationId)
                .append(":").append(integrationVersion);
        for (String e : extra) {
            sb.append(":").append(e);
        }
        return sb.toString();
    }
    public static String sftpFileLockKey(String serviceManagerName, String integrationName, String integrationVersion, String adapterId, String fileName) {
        return String.format("hip:runtime:sftp:lock:%s:%s:%s:%s:%s",
                serviceManagerName, integrationName, integrationVersion, adapterId, fileName);
    }
    public static String handlerPauseKey(String serviceManagerName, String integrationName, String integrationVersion, String handlerId) {
        return String.format("hip:runtime:handler:pause:%s:%s:%s:%s", serviceManagerName, integrationName, integrationVersion, handlerId);
    }
    /*public static String ruleKey(String ruleName,String ruleVersion) {
        return "hip:config:rule:" + ruleName+ ":" + ruleVersion;
    }*/
    // ---- Flow Routing Rules (used by FlowRoutingFlowStepStrategy) ----
    public static String flowRoutingRulePrefix(String serviceManagerName, String integrationName, String version) {
        // e.g., "svc:integration:ver:flowrule:"
        return serviceManagerName + ":" + integrationName + ":" + version + ":flowrule:";
    }

    // ---- Mapping Transformer Rules (used by MappingTransformerFlowStepStrategy) ----
    public static String mappingRuleKey(String ruleName, String ruleVersion) {
        // e.g., "hip:config:mappingrule:myrule:1.0"
        return "hip:config:mappingrule:" + ruleName + ":" + ruleVersion;
    }
    public static String mappingRulePrefix() {
        return "hip:config:mappingrule:";
    }

    // ---- Flow Targets Routing Rules (used by FlowTargetsRoutingFlowStepStrategy) ----
    public static String flowTargetsRuleKey(String ruleName, String ruleVersion) {
        // e.g., "hip:config:flowtargetrule:myrule:1.0"
        return "hip:config:flowtargetrule:" + ruleName + ":" + ruleVersion;
    }
    public static String flowTargetsRulePrefix() {
        return "hip:config:flowtargetrule:";
    }

    // ---- Generic Rule Key (if needed elsewhere) ----
    public static String ruleKey(String ruleName, String ruleVersion) {
        return "hip:config:rule:" + ruleName + ":" + ruleVersion;
    }

}